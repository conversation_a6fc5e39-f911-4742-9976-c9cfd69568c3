============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Mon Aug  4 15:55:32 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1580 instances
RUN-0007 : 377 luts, 953 seqs, 129 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2123 nets
RUN-1001 : 1542 nets have 2 pins
RUN-1001 : 479 nets have [3 - 5] pins
RUN-1001 : 57 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     251     
RUN-1001 :   No   |  No   |  Yes  |     123     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1578 instances, 377 luts, 953 seqs, 199 slices, 20 macros(199 instances: 129 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7560, tnet num: 2121, tinst num: 1578, tnode num: 10721, tedge num: 12839.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2121 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.443104s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (102.3%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 529701
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1578.
PHY-3001 : End clustering;  0.000032s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 456984, overlap = 20.25
PHY-3002 : Step(2): len = 430796, overlap = 15.75
PHY-3002 : Step(3): len = 417328, overlap = 20.25
PHY-3002 : Step(4): len = 404133, overlap = 13.5
PHY-3002 : Step(5): len = 391927, overlap = 18
PHY-3002 : Step(6): len = 372390, overlap = 15.75
PHY-3002 : Step(7): len = 365400, overlap = 18
PHY-3002 : Step(8): len = 352882, overlap = 15.75
PHY-3002 : Step(9): len = 338779, overlap = 13.5
PHY-3002 : Step(10): len = 332562, overlap = 13.5
PHY-3002 : Step(11): len = 326122, overlap = 11.25
PHY-3002 : Step(12): len = 318310, overlap = 11.25
PHY-3002 : Step(13): len = 311512, overlap = 13.5
PHY-3002 : Step(14): len = 306132, overlap = 13.5
PHY-3002 : Step(15): len = 299446, overlap = 13.5
PHY-3002 : Step(16): len = 291387, overlap = 15.75
PHY-3002 : Step(17): len = 286497, overlap = 15.75
PHY-3002 : Step(18): len = 279071, overlap = 15.75
PHY-3002 : Step(19): len = 272952, overlap = 15.75
PHY-3002 : Step(20): len = 267976, overlap = 15.75
PHY-3002 : Step(21): len = 262741, overlap = 15.75
PHY-3002 : Step(22): len = 255766, overlap = 15.75
PHY-3002 : Step(23): len = 249943, overlap = 15.75
PHY-3002 : Step(24): len = 245270, overlap = 15.75
PHY-3002 : Step(25): len = 239661, overlap = 13.5
PHY-3002 : Step(26): len = 233986, overlap = 13.5
PHY-3002 : Step(27): len = 228902, overlap = 13.5
PHY-3002 : Step(28): len = 224473, overlap = 20.25
PHY-3002 : Step(29): len = 218193, overlap = 20.25
PHY-3002 : Step(30): len = 212480, overlap = 20.25
PHY-3002 : Step(31): len = 208381, overlap = 20.25
PHY-3002 : Step(32): len = 202767, overlap = 20.25
PHY-3002 : Step(33): len = 198793, overlap = 20.25
PHY-3002 : Step(34): len = 192245, overlap = 20.25
PHY-3002 : Step(35): len = 188688, overlap = 20.25
PHY-3002 : Step(36): len = 183551, overlap = 20.25
PHY-3002 : Step(37): len = 176754, overlap = 20.25
PHY-3002 : Step(38): len = 172743, overlap = 20.25
PHY-3002 : Step(39): len = 169856, overlap = 20.25
PHY-3002 : Step(40): len = 160115, overlap = 20.25
PHY-3002 : Step(41): len = 152585, overlap = 20.25
PHY-3002 : Step(42): len = 150236, overlap = 20.25
PHY-3002 : Step(43): len = 146773, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000109209
PHY-3002 : Step(44): len = 148287, overlap = 18
PHY-3002 : Step(45): len = 147069, overlap = 13.5
PHY-3002 : Step(46): len = 145714, overlap = 15.75
PHY-3002 : Step(47): len = 143238, overlap = 13.5
PHY-3002 : Step(48): len = 138908, overlap = 13.5
PHY-3002 : Step(49): len = 137347, overlap = 13.5
PHY-3002 : Step(50): len = 130703, overlap = 9
PHY-3002 : Step(51): len = 128497, overlap = 11.25
PHY-3002 : Step(52): len = 125558, overlap = 11.25
PHY-3002 : Step(53): len = 123821, overlap = 11.25
PHY-3002 : Step(54): len = 121798, overlap = 15.75
PHY-3002 : Step(55): len = 120631, overlap = 15.75
PHY-3002 : Step(56): len = 118760, overlap = 13.5
PHY-3002 : Step(57): len = 114232, overlap = 13.5
PHY-3002 : Step(58): len = 111959, overlap = 11.25
PHY-3002 : Step(59): len = 110912, overlap = 11.25
PHY-3002 : Step(60): len = 109016, overlap = 13.5
PHY-3002 : Step(61): len = 103308, overlap = 15.75
PHY-3002 : Step(62): len = 100405, overlap = 15.75
PHY-3002 : Step(63): len = 99057.6, overlap = 13.5
PHY-3002 : Step(64): len = 98289.6, overlap = 13.5
PHY-3002 : Step(65): len = 96232.4, overlap = 13.5
PHY-3002 : Step(66): len = 92076.4, overlap = 13.5
PHY-3002 : Step(67): len = 90303.8, overlap = 13.5
PHY-3002 : Step(68): len = 88834.6, overlap = 13.5
PHY-3002 : Step(69): len = 87913.3, overlap = 11.25
PHY-3002 : Step(70): len = 86244.6, overlap = 15.75
PHY-3002 : Step(71): len = 82554.2, overlap = 15.75
PHY-3002 : Step(72): len = 80002.7, overlap = 13.5
PHY-3002 : Step(73): len = 77521.8, overlap = 11.25
PHY-3002 : Step(74): len = 76631.4, overlap = 11.6875
PHY-3002 : Step(75): len = 76086.2, overlap = 14.0625
PHY-3002 : Step(76): len = 75478.2, overlap = 12.5625
PHY-3002 : Step(77): len = 74509.3, overlap = 10.75
PHY-3002 : Step(78): len = 73208.2, overlap = 11.0625
PHY-3002 : Step(79): len = 72512.8, overlap = 11
PHY-3002 : Step(80): len = 71778.3, overlap = 8.875
PHY-3002 : Step(81): len = 70656.3, overlap = 11.1875
PHY-3002 : Step(82): len = 69879.7, overlap = 13.5
PHY-3002 : Step(83): len = 69417.1, overlap = 13.625
PHY-3002 : Step(84): len = 68269, overlap = 11.4375
PHY-3002 : Step(85): len = 67387.3, overlap = 11.5
PHY-3002 : Step(86): len = 66535.9, overlap = 11.5
PHY-3002 : Step(87): len = 65859.4, overlap = 11.75
PHY-3002 : Step(88): len = 65772.6, overlap = 9.625
PHY-3002 : Step(89): len = 65501.5, overlap = 9.625
PHY-3002 : Step(90): len = 65028.4, overlap = 9.5625
PHY-3002 : Step(91): len = 64581.5, overlap = 11.75
PHY-3002 : Step(92): len = 64072.2, overlap = 11.625
PHY-3002 : Step(93): len = 63297.7, overlap = 13.625
PHY-3002 : Step(94): len = 62527.1, overlap = 11.4375
PHY-3002 : Step(95): len = 61980.8, overlap = 11.1875
PHY-3002 : Step(96): len = 61115.6, overlap = 11.25
PHY-3002 : Step(97): len = 60310.9, overlap = 8.875
PHY-3002 : Step(98): len = 60049.7, overlap = 8.875
PHY-3002 : Step(99): len = 58679, overlap = 11.875
PHY-3002 : Step(100): len = 58478, overlap = 11.8125
PHY-3002 : Step(101): len = 57575, overlap = 11.6875
PHY-3002 : Step(102): len = 57560, overlap = 11.5625
PHY-3002 : Step(103): len = 57276.8, overlap = 11.5625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000218417
PHY-3002 : Step(104): len = 57459.6, overlap = 11.5625
PHY-3002 : Step(105): len = 57312.7, overlap = 11.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000436835
PHY-3002 : Step(106): len = 57562.1, overlap = 11.125
PHY-3002 : Step(107): len = 57665.9, overlap = 11.125
PHY-3001 : Before Legalized: Len = 57665.9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.017884s wall, 0.015625s user + 0.015625s system = 0.031250s CPU (174.7%)

PHY-3001 : After Legalized: Len = 59453.6, Over = 2.125
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2121 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.162199s wall, 0.140625s user + 0.031250s system = 0.171875s CPU (106.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000441924
PHY-3002 : Step(108): len = 59897.7, overlap = 7.3125
PHY-3002 : Step(109): len = 58944.1, overlap = 8.5625
PHY-3002 : Step(110): len = 58684.4, overlap = 8.46875
PHY-3002 : Step(111): len = 57754.5, overlap = 7
PHY-3002 : Step(112): len = 56866.3, overlap = 7.21875
PHY-3002 : Step(113): len = 56175.4, overlap = 7.65625
PHY-3002 : Step(114): len = 55205.9, overlap = 8.9375
PHY-3002 : Step(115): len = 53740, overlap = 10.6875
PHY-3002 : Step(116): len = 52674.6, overlap = 13
PHY-3002 : Step(117): len = 51394.8, overlap = 13.875
PHY-3002 : Step(118): len = 50023.8, overlap = 14.125
PHY-3002 : Step(119): len = 48824.9, overlap = 14.5625
PHY-3002 : Step(120): len = 47663.3, overlap = 14.125
PHY-3002 : Step(121): len = 46659.9, overlap = 13.6562
PHY-3002 : Step(122): len = 46217.3, overlap = 13.8438
PHY-3002 : Step(123): len = 45883, overlap = 13.5625
PHY-3002 : Step(124): len = 45392.8, overlap = 13.6562
PHY-3002 : Step(125): len = 44807.7, overlap = 14.2188
PHY-3002 : Step(126): len = 44484.1, overlap = 15.9062
PHY-3002 : Step(127): len = 44440, overlap = 18.5625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000883847
PHY-3002 : Step(128): len = 44214.8, overlap = 19.0625
PHY-3002 : Step(129): len = 43754.5, overlap = 18.3125
PHY-3002 : Step(130): len = 43584.3, overlap = 18.2812
PHY-3002 : Step(131): len = 43584.3, overlap = 18.2812
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00176769
PHY-3002 : Step(132): len = 43772.9, overlap = 17.125
PHY-3002 : Step(133): len = 43834.4, overlap = 16.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2121 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.136731s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (102.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.43874e-05
PHY-3002 : Step(134): len = 43970.6, overlap = 57.7812
PHY-3002 : Step(135): len = 44738.1, overlap = 49.0312
PHY-3002 : Step(136): len = 45598.5, overlap = 47.375
PHY-3002 : Step(137): len = 45683.7, overlap = 47.4688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000168775
PHY-3002 : Step(138): len = 45767.8, overlap = 47.1875
PHY-3002 : Step(139): len = 46125.9, overlap = 47.4688
PHY-3002 : Step(140): len = 46815.3, overlap = 45.6562
PHY-3002 : Step(141): len = 47311.2, overlap = 39.2812
PHY-3002 : Step(142): len = 47698.8, overlap = 37.125
PHY-3002 : Step(143): len = 47863.3, overlap = 36.5312
PHY-3002 : Step(144): len = 48091.1, overlap = 36.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00033755
PHY-3002 : Step(145): len = 48040.7, overlap = 36.8125
PHY-3002 : Step(146): len = 47930.4, overlap = 29.4375
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7560, tnet num: 2121, tinst num: 1578, tnode num: 10721, tedge num: 12839.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 93.88 peak overflow 4.75
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2123.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 51056, over cnt = 234(0%), over = 1059, worst = 20
PHY-1001 : End global iterations;  0.116723s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (147.3%)

PHY-1001 : Congestion index: top1 = 44.72, top5 = 24.35, top10 = 15.51, top15 = 10.98.
PHY-1001 : End incremental global routing;  0.206117s wall, 0.234375s user + 0.031250s system = 0.265625s CPU (128.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2121 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.128306s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (97.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.401964s wall, 0.421875s user + 0.031250s system = 0.453125s CPU (112.7%)

OPT-1001 : Current memory(MB): used = 209, reserve = 176, peak = 209.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1641/2123.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 51056, over cnt = 234(0%), over = 1059, worst = 20
PHY-1002 : len = 57416, over cnt = 188(0%), over = 588, worst = 18
PHY-1002 : len = 64392, over cnt = 47(0%), over = 61, worst = 4
PHY-1002 : len = 65608, over cnt = 14(0%), over = 14, worst = 1
PHY-1002 : len = 66448, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.212166s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (125.2%)

PHY-1001 : Congestion index: top1 = 39.66, top5 = 24.81, top10 = 17.80, top15 = 13.24.
OPT-1001 : End congestion update;  0.284816s wall, 0.312500s user + 0.015625s system = 0.328125s CPU (115.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2121 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.115241s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (108.5%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.405880s wall, 0.437500s user + 0.015625s system = 0.453125s CPU (111.6%)

OPT-1001 : Current memory(MB): used = 212, reserve = 179, peak = 212.
OPT-1001 : End physical optimization;  1.474585s wall, 1.500000s user + 0.062500s system = 1.562500s CPU (106.0%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 377 LUT to BLE ...
SYN-4008 : Packed 377 LUT and 188 SEQ to BLE.
SYN-4003 : Packing 765 remaining SEQ's ...
SYN-4005 : Packed 77 SEQ with LUT/SLICE
SYN-4006 : 124 single LUT's are left
SYN-4006 : 688 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1065/1379 primitive instances ...
PHY-3001 : End packing;  0.087787s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (106.8%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 810 instances
RUN-1001 : 379 mslices, 380 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1943 nets
RUN-1001 : 1365 nets have 2 pins
RUN-1001 : 474 nets have [3 - 5] pins
RUN-1001 : 59 nets have [6 - 10] pins
RUN-1001 : 24 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 808 instances, 759 slices, 20 macros(199 instances: 129 mslices 70 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 48289, Over = 53.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6364, tnet num: 1941, tinst num: 808, tnode num: 8661, tedge num: 11262.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1941 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.559783s wall, 0.546875s user + 0.000000s system = 0.546875s CPU (97.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.88713e-05
PHY-3002 : Step(147): len = 47722.7, overlap = 54.75
PHY-3002 : Step(148): len = 47249.8, overlap = 52.75
PHY-3002 : Step(149): len = 46906.6, overlap = 53.5
PHY-3002 : Step(150): len = 46891.5, overlap = 54
PHY-3002 : Step(151): len = 46649.1, overlap = 53.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.77425e-05
PHY-3002 : Step(152): len = 47113.7, overlap = 54.5
PHY-3002 : Step(153): len = 47720.4, overlap = 50
PHY-3002 : Step(154): len = 48385.9, overlap = 47
PHY-3002 : Step(155): len = 48162.5, overlap = 47.75
PHY-3002 : Step(156): len = 47848.2, overlap = 49.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000115485
PHY-3002 : Step(157): len = 48694.7, overlap = 48
PHY-3002 : Step(158): len = 49289.6, overlap = 46.25
PHY-3002 : Step(159): len = 49819.9, overlap = 46.5
PHY-3001 : Before Legalized: Len = 49819.9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.140015s wall, 0.125000s user + 0.218750s system = 0.343750s CPU (245.5%)

PHY-3001 : After Legalized: Len = 63266, Over = 0
PHY-3001 : Trial Legalized: Len = 63266
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1941 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.139724s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (100.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000919369
PHY-3002 : Step(160): len = 59200.6, overlap = 8.5
PHY-3002 : Step(161): len = 57870.5, overlap = 10.75
PHY-3002 : Step(162): len = 55508.3, overlap = 16.75
PHY-3002 : Step(163): len = 54721.2, overlap = 20.5
PHY-3002 : Step(164): len = 54142.3, overlap = 21.75
PHY-3002 : Step(165): len = 53739.8, overlap = 22.75
PHY-3002 : Step(166): len = 53356.2, overlap = 24.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00183874
PHY-3002 : Step(167): len = 53585.5, overlap = 24
PHY-3002 : Step(168): len = 53626.1, overlap = 22.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00367747
PHY-3002 : Step(169): len = 53721.1, overlap = 21.75
PHY-3002 : Step(170): len = 53729.1, overlap = 21.5
PHY-3001 : Before Legalized: Len = 53729.1
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.013034s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (119.9%)

PHY-3001 : After Legalized: Len = 58415.2, Over = 0
PHY-3001 : Legalized: Len = 58415.2, Over = 0
PHY-3001 : Spreading special nets. 10 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.010758s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 14 instances has been re-located, deltaX = 5, deltaY = 9, maxDist = 1.
PHY-3001 : Final: Len = 58567.2, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6364, tnet num: 1941, tinst num: 808, tnode num: 8661, tedge num: 11262.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 45/1943.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 64432, over cnt = 145(0%), over = 219, worst = 7
PHY-1002 : len = 65552, over cnt = 63(0%), over = 71, worst = 2
PHY-1002 : len = 66480, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 66512, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.210275s wall, 0.265625s user + 0.046875s system = 0.312500s CPU (148.6%)

PHY-1001 : Congestion index: top1 = 31.47, top5 = 22.39, top10 = 17.03, top15 = 13.26.
PHY-1001 : End incremental global routing;  0.296399s wall, 0.359375s user + 0.046875s system = 0.406250s CPU (137.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1941 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.100000s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (93.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.445555s wall, 0.500000s user + 0.046875s system = 0.546875s CPU (122.7%)

OPT-1001 : Current memory(MB): used = 214, reserve = 181, peak = 215.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1706/1943.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 66512, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.012961s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (120.6%)

PHY-1001 : Congestion index: top1 = 31.47, top5 = 22.39, top10 = 17.03, top15 = 13.26.
OPT-1001 : End congestion update;  0.093197s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (100.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1941 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.096842s wall, 0.109375s user + 0.031250s system = 0.140625s CPU (145.2%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 768 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 808 instances, 759 slices, 20 macros(199 instances: 129 mslices 70 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 58614, Over = 0
PHY-3001 : End spreading;  0.011438s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (136.6%)

PHY-3001 : Final: Len = 58614, Over = 0
PHY-3001 : End incremental legalization;  0.065847s wall, 0.109375s user + 0.031250s system = 0.140625s CPU (213.6%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.280272s wall, 0.328125s user + 0.062500s system = 0.390625s CPU (139.4%)

OPT-1001 : Current memory(MB): used = 220, reserve = 187, peak = 220.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1941 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.089836s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (104.4%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1702/1943.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 66536, over cnt = 1(0%), over = 2, worst = 2
PHY-1002 : len = 66552, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.032018s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (97.6%)

PHY-1001 : Congestion index: top1 = 31.55, top5 = 22.37, top10 = 17.01, top15 = 13.25.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1941 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.093374s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (100.4%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.137931
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  1.628615s wall, 1.750000s user + 0.125000s system = 1.875000s CPU (115.1%)

RUN-1003 : finish command "place" in  11.287299s wall, 16.125000s user + 7.437500s system = 23.562500s CPU (208.8%)

RUN-1004 : used memory is 198 MB, reserved memory is 164 MB, peak memory is 220 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 810 instances
RUN-1001 : 379 mslices, 380 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1943 nets
RUN-1001 : 1365 nets have 2 pins
RUN-1001 : 474 nets have [3 - 5] pins
RUN-1001 : 59 nets have [6 - 10] pins
RUN-1001 : 24 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6364, tnet num: 1941, tinst num: 808, tnode num: 8661, tedge num: 11262.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 379 mslices, 380 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1941 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 63656, over cnt = 145(0%), over = 225, worst = 7
PHY-1002 : len = 64888, over cnt = 60(0%), over = 65, worst = 2
PHY-1002 : len = 65704, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 65752, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.205404s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (121.7%)

PHY-1001 : Congestion index: top1 = 31.31, top5 = 22.40, top10 = 16.95, top15 = 13.17.
PHY-1001 : End global routing;  0.290460s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (113.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 235, reserve = 202, peak = 235.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 495, reserve = 466, peak = 495.
PHY-1001 : End build detailed router design. 5.631288s wall, 5.578125s user + 0.046875s system = 5.625000s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 32928, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.809569s wall, 1.796875s user + 0.000000s system = 1.796875s CPU (99.3%)

PHY-1001 : Current memory(MB): used = 528, reserve = 499, peak = 528.
PHY-1001 : End phase 1; 1.818346s wall, 1.812500s user + 0.000000s system = 1.812500s CPU (99.7%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 28% nets.
PHY-1001 : Routed 35% nets.
PHY-1001 : Routed 45% nets.
PHY-1001 : Routed 62% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 173688, over cnt = 35(0%), over = 35, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 529, reserve = 500, peak = 530.
PHY-1001 : End initial routed; 2.125967s wall, 3.000000s user + 0.187500s system = 3.187500s CPU (149.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1725(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.549   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -46.976  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.634556s wall, 0.640625s user + 0.000000s system = 0.640625s CPU (101.0%)

PHY-1001 : Current memory(MB): used = 531, reserve = 503, peak = 531.
PHY-1001 : End phase 2; 2.760686s wall, 3.640625s user + 0.187500s system = 3.828125s CPU (138.7%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 173688, over cnt = 35(0%), over = 35, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.028904s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (108.1%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 173504, over cnt = 10(0%), over = 10, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.039120s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (79.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 173576, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.036142s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (129.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1725(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.549   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -46.976  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.683341s wall, 0.656250s user + 0.000000s system = 0.656250s CPU (96.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 7 feed throughs used by 7 nets
PHY-1001 : End commit to database; 0.342944s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (100.2%)

PHY-1001 : Current memory(MB): used = 545, reserve = 517, peak = 545.
PHY-1001 : End phase 3; 1.340622s wall, 1.312500s user + 0.000000s system = 1.312500s CPU (97.9%)

PHY-1003 : Routed, final wirelength = 173576
PHY-1001 : Current memory(MB): used = 546, reserve = 518, peak = 546.
PHY-1001 : End export database. 0.015120s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (103.3%)

PHY-1001 : End detail routing;  11.851838s wall, 12.625000s user + 0.250000s system = 12.875000s CPU (108.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6364, tnet num: 1941, tinst num: 808, tnode num: 8661, tedge num: 11262.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[38] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[2] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[40] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[4] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[0] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[1] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[47] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[2] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[6] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[52] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[7] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[54] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_66.dia[2] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[55] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_66.dia[5] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_60.mi[0] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_60.mi[1] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_63.mi[0] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_63.mi[1] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_66.mi[0] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_66.mi[1] slack -2911ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_69.mi[0] slack -2897ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_69.mi[1] slack -2679ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_72.mi[0] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_72.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_75.mi[0] slack -2684ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_75.mi[1] slack -2897ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_78.mi[0] slack -2897ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_78.mi[1] slack -2897ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_81.mi[0] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_81.mi[1] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6484, tnet num: 2001, tinst num: 868, tnode num: 8781, tedge num: 11382.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_66_mi[1] slack -195ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_69_mi[0] slack -995ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_69_mi[1] slack -219ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_72_mi[1] slack -657ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg2_syn_72_mi[0] slack -322ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_75_mi[1] slack -378ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_78_mi[1] slack -327ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_75_mi[0] slack -977ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_78_mi[0] slack -622ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_81_mi[1] slack -608ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_81_mi[0] slack -695ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_60_mi[1] slack -165ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_60_mi[0] slack -165ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_63_mi[1] slack -486ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_63_mi[0] slack -863ps
RUN-1001 : End hold fix;  4.534478s wall, 4.625000s user + 0.234375s system = 4.859375s CPU (107.2%)

RUN-1003 : finish command "route" in  17.288602s wall, 18.156250s user + 0.500000s system = 18.656250s CPU (107.9%)

RUN-1004 : used memory is 523 MB, reserved memory is 495 MB, peak memory is 546 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      904   out of  19600    4.61%
#reg                     1052   out of  19600    5.37%
#le                      1592
  #lut only               540   out of   1592   33.92%
  #reg only               688   out of   1592   43.22%
  #lut&reg                364   out of   1592   22.86%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         481
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         100
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    41
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1592   |705     |199     |1085    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1074   |293     |136     |861     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |33     |28      |5       |23      |0       |0       |
|    demodu                  |Demodulation                                     |447    |106     |41      |350     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |54     |37      |6       |44      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |11      |0       |16      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |12     |7       |0       |12      |0       |0       |
|    integ                   |Integration                                      |142    |21      |15      |114     |0       |0       |
|    modu                    |Modulation                                       |102    |46      |21      |98      |0       |1       |
|    rs422                   |Rs422Output                                      |326    |76      |46      |257     |0       |4       |
|    trans                   |SquareWaveGenerator                              |24     |16      |8       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |184    |134     |7       |118     |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |25     |18      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |122    |88      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |312    |267     |45      |72      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1386  
    #2          2       328   
    #3          3       120   
    #4          4        26   
    #5        5-10       66   
    #6        11-50      32   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6484, tnet num: 2001, tinst num: 868, tnode num: 8781, tedge num: 11382.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2001 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 868
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2003, pip num: 14540
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 12
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1287 valid insts, and 39084 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  5.495355s wall, 26.453125s user + 0.187500s system = 26.640625s CPU (484.8%)

RUN-1004 : used memory is 545 MB, reserved memory is 516 MB, peak memory is 672 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250804_155532.log"
