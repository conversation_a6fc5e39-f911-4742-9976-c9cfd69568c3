============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Mon Aug 11 16:13:53 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1574 instances
RUN-0007 : 371 luts, 951 seqs, 131 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2122 nets
RUN-1001 : 1547 nets have 2 pins
RUN-1001 : 472 nets have [3 - 5] pins
RUN-1001 : 60 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     125     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1572 instances, 371 luts, 951 seqs, 201 slices, 20 macros(201 instances: 131 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7555, tnet num: 2120, tinst num: 1572, tnode num: 10717, tedge num: 12860.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2120 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.252156s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (99.1%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 532440
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1572.
PHY-3001 : End clustering;  0.000023s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 459031, overlap = 20.25
PHY-3002 : Step(2): len = 435344, overlap = 18
PHY-3002 : Step(3): len = 420609, overlap = 20.25
PHY-3002 : Step(4): len = 411141, overlap = 13.5
PHY-3002 : Step(5): len = 395678, overlap = 13.5
PHY-3002 : Step(6): len = 383188, overlap = 13.5
PHY-3002 : Step(7): len = 374779, overlap = 13.5
PHY-3002 : Step(8): len = 367401, overlap = 18
PHY-3002 : Step(9): len = 354927, overlap = 15.75
PHY-3002 : Step(10): len = 348777, overlap = 13.5
PHY-3002 : Step(11): len = 340528, overlap = 13.5
PHY-3002 : Step(12): len = 333579, overlap = 13.5
PHY-3002 : Step(13): len = 324667, overlap = 13.5
PHY-3002 : Step(14): len = 320136, overlap = 13.5
PHY-3002 : Step(15): len = 311561, overlap = 15.75
PHY-3002 : Step(16): len = 304710, overlap = 15.75
PHY-3002 : Step(17): len = 298510, overlap = 15.75
PHY-3002 : Step(18): len = 293306, overlap = 15.75
PHY-3002 : Step(19): len = 284860, overlap = 15.75
PHY-3002 : Step(20): len = 280609, overlap = 15.75
PHY-3002 : Step(21): len = 275160, overlap = 15.75
PHY-3002 : Step(22): len = 269429, overlap = 13.5
PHY-3002 : Step(23): len = 259985, overlap = 13.5
PHY-3002 : Step(24): len = 256886, overlap = 15.75
PHY-3002 : Step(25): len = 250850, overlap = 20.25
PHY-3002 : Step(26): len = 241924, overlap = 20.25
PHY-3002 : Step(27): len = 236085, overlap = 20.25
PHY-3002 : Step(28): len = 233429, overlap = 20.25
PHY-3002 : Step(29): len = 219901, overlap = 20.25
PHY-3002 : Step(30): len = 212334, overlap = 20.25
PHY-3002 : Step(31): len = 209224, overlap = 20.25
PHY-3002 : Step(32): len = 204840, overlap = 20.25
PHY-3002 : Step(33): len = 177502, overlap = 18
PHY-3002 : Step(34): len = 171963, overlap = 21.25
PHY-3002 : Step(35): len = 170174, overlap = 21.25
PHY-3002 : Step(36): len = 158138, overlap = 21.25
PHY-3002 : Step(37): len = 117422, overlap = 18
PHY-3002 : Step(38): len = 116304, overlap = 20.25
PHY-3002 : Step(39): len = 112656, overlap = 13.5
PHY-3002 : Step(40): len = 109627, overlap = 15.75
PHY-3002 : Step(41): len = 108384, overlap = 18
PHY-3002 : Step(42): len = 107075, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.5748e-05
PHY-3002 : Step(43): len = 107869, overlap = 11.25
PHY-3002 : Step(44): len = 106840, overlap = 15.75
PHY-3002 : Step(45): len = 106294, overlap = 15.75
PHY-3002 : Step(46): len = 105687, overlap = 13.5
PHY-3002 : Step(47): len = 104084, overlap = 15.75
PHY-3002 : Step(48): len = 101697, overlap = 16
PHY-3002 : Step(49): len = 99675.4, overlap = 14
PHY-3002 : Step(50): len = 99093.6, overlap = 11.75
PHY-3002 : Step(51): len = 97142.1, overlap = 14
PHY-3002 : Step(52): len = 93628.2, overlap = 15.75
PHY-3002 : Step(53): len = 91954, overlap = 15.75
PHY-3002 : Step(54): len = 91260.1, overlap = 18
PHY-3002 : Step(55): len = 90366, overlap = 13.5
PHY-3002 : Step(56): len = 89368.7, overlap = 13.5
PHY-3002 : Step(57): len = 87876.1, overlap = 15.75
PHY-3002 : Step(58): len = 85847.2, overlap = 15.75
PHY-3002 : Step(59): len = 82513.4, overlap = 16
PHY-3002 : Step(60): len = 82161.9, overlap = 18.5625
PHY-3002 : Step(61): len = 81268.3, overlap = 18.375
PHY-3002 : Step(62): len = 78336.6, overlap = 12.6875
PHY-3002 : Step(63): len = 77510.1, overlap = 12.75
PHY-3002 : Step(64): len = 75835.2, overlap = 15.0625
PHY-3002 : Step(65): len = 74387.2, overlap = 17.4375
PHY-3002 : Step(66): len = 74118.7, overlap = 17.5625
PHY-3002 : Step(67): len = 73108, overlap = 15.3125
PHY-3002 : Step(68): len = 71901.3, overlap = 15.375
PHY-3002 : Step(69): len = 71254, overlap = 15.3125
PHY-3002 : Step(70): len = 70251.1, overlap = 15.5625
PHY-3002 : Step(71): len = 69606.1, overlap = 17.9375
PHY-3002 : Step(72): len = 69600.9, overlap = 19
PHY-3002 : Step(73): len = 69211.9, overlap = 16.6875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000171496
PHY-3002 : Step(74): len = 68854, overlap = 16.5625
PHY-3002 : Step(75): len = 68779, overlap = 16.5625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000342992
PHY-3002 : Step(76): len = 69059.7, overlap = 16.5625
PHY-3002 : Step(77): len = 69085, overlap = 16.5625
PHY-3001 : Before Legalized: Len = 69085
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005497s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 73134.6, Over = 3.0625
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2120 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.058412s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (80.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(78): len = 72986.6, overlap = 8.40625
PHY-3002 : Step(79): len = 71703, overlap = 9
PHY-3002 : Step(80): len = 70481.6, overlap = 9.1875
PHY-3002 : Step(81): len = 69547, overlap = 10.125
PHY-3002 : Step(82): len = 68103.1, overlap = 10.4688
PHY-3002 : Step(83): len = 66934.3, overlap = 10.3125
PHY-3002 : Step(84): len = 65825.6, overlap = 8.15625
PHY-3002 : Step(85): len = 64515.5, overlap = 11.0312
PHY-3002 : Step(86): len = 63191.7, overlap = 8.90625
PHY-3002 : Step(87): len = 61462.6, overlap = 12.75
PHY-3002 : Step(88): len = 60416, overlap = 13.1875
PHY-3002 : Step(89): len = 59570.3, overlap = 13.3125
PHY-3002 : Step(90): len = 58773, overlap = 13.0625
PHY-3002 : Step(91): len = 57168.8, overlap = 10.9688
PHY-3002 : Step(92): len = 56398.2, overlap = 15.1562
PHY-3002 : Step(93): len = 55931, overlap = 16.6875
PHY-3002 : Step(94): len = 55174.4, overlap = 16.5
PHY-3002 : Step(95): len = 54403.2, overlap = 16.6875
PHY-3002 : Step(96): len = 54081.5, overlap = 15.375
PHY-3002 : Step(97): len = 53707.5, overlap = 13.1875
PHY-3002 : Step(98): len = 53484.4, overlap = 13
PHY-3002 : Step(99): len = 52946, overlap = 12.5625
PHY-3002 : Step(100): len = 52844.3, overlap = 12.6875
PHY-3002 : Step(101): len = 52469.9, overlap = 12.6562
PHY-3002 : Step(102): len = 52187.6, overlap = 12.5938
PHY-3002 : Step(103): len = 51407.7, overlap = 13.3125
PHY-3002 : Step(104): len = 51394.7, overlap = 13.6875
PHY-3002 : Step(105): len = 50809.6, overlap = 12.625
PHY-3002 : Step(106): len = 50465.3, overlap = 15.625
PHY-3002 : Step(107): len = 50382.2, overlap = 18.125
PHY-3002 : Step(108): len = 49735, overlap = 19.4375
PHY-3002 : Step(109): len = 49328.1, overlap = 19.6875
PHY-3002 : Step(110): len = 49225.1, overlap = 20.625
PHY-3002 : Step(111): len = 49105.8, overlap = 20.8438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000148761
PHY-3002 : Step(112): len = 48837.5, overlap = 20.7812
PHY-3002 : Step(113): len = 48904.4, overlap = 20.875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000297522
PHY-3002 : Step(114): len = 48812.4, overlap = 20.5938
PHY-3002 : Step(115): len = 48962.2, overlap = 19
PHY-3002 : Step(116): len = 49024.9, overlap = 19.7188
PHY-3002 : Step(117): len = 48820.9, overlap = 19.7188
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2120 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.056615s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (110.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.08542e-05
PHY-3002 : Step(118): len = 48940.3, overlap = 58.0938
PHY-3002 : Step(119): len = 49975.9, overlap = 56.25
PHY-3002 : Step(120): len = 50513.5, overlap = 55.5312
PHY-3002 : Step(121): len = 49739.2, overlap = 56.0625
PHY-3002 : Step(122): len = 49603.3, overlap = 55.9375
PHY-3002 : Step(123): len = 49421.5, overlap = 55.8438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000121708
PHY-3002 : Step(124): len = 50285.1, overlap = 47.6562
PHY-3002 : Step(125): len = 50443.3, overlap = 47.4062
PHY-3002 : Step(126): len = 50844.9, overlap = 46.1562
PHY-3002 : Step(127): len = 51222.6, overlap = 46.6562
PHY-3002 : Step(128): len = 51392.2, overlap = 47.0625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000243417
PHY-3002 : Step(129): len = 51597.2, overlap = 46.9062
PHY-3002 : Step(130): len = 51597.2, overlap = 46.9062
PHY-3002 : Step(131): len = 51849.4, overlap = 45.5938
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000393848
PHY-3002 : Step(132): len = 52579.5, overlap = 44.7188
PHY-3002 : Step(133): len = 53578.8, overlap = 41.5312
PHY-3002 : Step(134): len = 54266.4, overlap = 38.9062
PHY-3002 : Step(135): len = 54563.7, overlap = 37.8125
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7555, tnet num: 2120, tinst num: 1572, tnode num: 10717, tedge num: 12860.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 89.28 peak overflow 2.41
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2122.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 57864, over cnt = 237(0%), over = 1003, worst = 18
PHY-1001 : End global iterations;  0.069132s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (113.0%)

PHY-1001 : Congestion index: top1 = 47.67, top5 = 26.25, top10 = 16.74, top15 = 11.93.
PHY-1001 : End incremental global routing;  0.117927s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (92.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2120 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.063593s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.210052s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (96.7%)

OPT-1001 : Current memory(MB): used = 210, reserve = 176, peak = 210.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1643/2122.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 57864, over cnt = 237(0%), over = 1003, worst = 18
PHY-1002 : len = 65248, over cnt = 167(0%), over = 428, worst = 14
PHY-1002 : len = 70232, over cnt = 31(0%), over = 32, worst = 2
PHY-1002 : len = 70464, over cnt = 15(0%), over = 16, worst = 2
PHY-1002 : len = 71000, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.100410s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (155.6%)

PHY-1001 : Congestion index: top1 = 40.28, top5 = 26.34, top10 = 18.90, top15 = 13.83.
OPT-1001 : End congestion update;  0.147846s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (148.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2120 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.054524s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (86.0%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.205551s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (129.2%)

OPT-1001 : Current memory(MB): used = 213, reserve = 179, peak = 213.
OPT-1001 : End physical optimization;  0.651674s wall, 0.687500s user + 0.031250s system = 0.718750s CPU (110.3%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 371 LUT to BLE ...
SYN-4008 : Packed 371 LUT and 190 SEQ to BLE.
SYN-4003 : Packing 761 remaining SEQ's ...
SYN-4005 : Packed 101 SEQ with LUT/SLICE
SYN-4006 : 97 single LUT's are left
SYN-4006 : 660 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1031/1347 primitive instances ...
PHY-3001 : End packing;  0.061265s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (102.0%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 799 instances
RUN-1001 : 374 mslices, 374 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1940 nets
RUN-1001 : 1367 nets have 2 pins
RUN-1001 : 468 nets have [3 - 5] pins
RUN-1001 : 62 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 797 instances, 748 slices, 20 macros(201 instances: 131 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 54394, Over = 66.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6339, tnet num: 1938, tinst num: 797, tnode num: 8641, tedge num: 11245.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1938 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.281280s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.31716e-05
PHY-3002 : Step(136): len = 53807.1, overlap = 72
PHY-3002 : Step(137): len = 53211.9, overlap = 72.75
PHY-3002 : Step(138): len = 52650.7, overlap = 73.5
PHY-3002 : Step(139): len = 52605.4, overlap = 75.75
PHY-3002 : Step(140): len = 52198.6, overlap = 73.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.63433e-05
PHY-3002 : Step(141): len = 52731.1, overlap = 70.25
PHY-3002 : Step(142): len = 53116.1, overlap = 67.5
PHY-3002 : Step(143): len = 54112.6, overlap = 61.75
PHY-3002 : Step(144): len = 54632, overlap = 60.5
PHY-3002 : Step(145): len = 54631.9, overlap = 61
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000132687
PHY-3002 : Step(146): len = 54873.3, overlap = 60.75
PHY-3002 : Step(147): len = 55491.3, overlap = 58.75
PHY-3001 : Before Legalized: Len = 55491.3
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.068374s wall, 0.031250s user + 0.125000s system = 0.156250s CPU (228.5%)

PHY-3001 : After Legalized: Len = 68773.8, Over = 0
PHY-3001 : Trial Legalized: Len = 68773.8
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1938 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.048703s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (96.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000823171
PHY-3002 : Step(148): len = 65069.1, overlap = 5.75
PHY-3002 : Step(149): len = 62806.7, overlap = 11.75
PHY-3002 : Step(150): len = 61121.1, overlap = 16.25
PHY-3002 : Step(151): len = 59585.3, overlap = 18.5
PHY-3002 : Step(152): len = 59049.2, overlap = 18
PHY-3002 : Step(153): len = 59082.9, overlap = 19.25
PHY-3002 : Step(154): len = 58969.3, overlap = 20.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00164634
PHY-3002 : Step(155): len = 59266.4, overlap = 22.25
PHY-3002 : Step(156): len = 59353.2, overlap = 21.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00329268
PHY-3002 : Step(157): len = 59528.5, overlap = 23
PHY-3002 : Step(158): len = 59559.2, overlap = 23.25
PHY-3001 : Before Legalized: Len = 59559.2
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005130s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (304.6%)

PHY-3001 : After Legalized: Len = 63822.6, Over = 0
PHY-3001 : Legalized: Len = 63822.6, Over = 0
PHY-3001 : Spreading special nets. 4 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005229s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 6 instances has been re-located, deltaX = 1, deltaY = 7, maxDist = 2.
PHY-3001 : Final: Len = 63972.6, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6339, tnet num: 1938, tinst num: 797, tnode num: 8641, tedge num: 11245.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 87/1940.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70808, over cnt = 164(0%), over = 245, worst = 8
PHY-1002 : len = 71984, over cnt = 90(0%), over = 108, worst = 5
PHY-1002 : len = 73152, over cnt = 10(0%), over = 11, worst = 2
PHY-1002 : len = 73296, over cnt = 1(0%), over = 2, worst = 2
PHY-1002 : len = 73296, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.138958s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (112.4%)

PHY-1001 : Congestion index: top1 = 32.67, top5 = 23.42, top10 = 18.24, top15 = 14.06.
PHY-1001 : End incremental global routing;  0.191199s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (114.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1938 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058378s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (80.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.277569s wall, 0.265625s user + 0.031250s system = 0.296875s CPU (107.0%)

OPT-1001 : Current memory(MB): used = 215, reserve = 182, peak = 216.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1717/1940.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73296, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005415s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.67, top5 = 23.42, top10 = 18.24, top15 = 14.06.
OPT-1001 : End congestion update;  0.050296s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1938 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061762s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.2%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 757 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 797 instances, 748 slices, 20 macros(201 instances: 131 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 63972, Over = 0
PHY-3001 : End spreading;  0.005297s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (295.0%)

PHY-3001 : Final: Len = 63972, Over = 0
PHY-3001 : End incremental legalization;  0.037729s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (124.2%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.163817s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (104.9%)

OPT-1001 : Current memory(MB): used = 220, reserve = 186, peak = 220.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1938 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050534s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.8%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1713/1940.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73288, over cnt = 1(0%), over = 2, worst = 2
PHY-1002 : len = 73272, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.015215s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (102.7%)

PHY-1001 : Congestion index: top1 = 32.63, top5 = 23.40, top10 = 18.23, top15 = 14.05.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1938 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048446s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (96.8%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 32.172414
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.864992s wall, 0.859375s user + 0.031250s system = 0.890625s CPU (103.0%)

RUN-1003 : finish command "place" in  4.925374s wall, 7.109375s user + 2.546875s system = 9.656250s CPU (196.1%)

RUN-1004 : used memory is 195 MB, reserved memory is 161 MB, peak memory is 220 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 799 instances
RUN-1001 : 374 mslices, 374 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1940 nets
RUN-1001 : 1367 nets have 2 pins
RUN-1001 : 468 nets have [3 - 5] pins
RUN-1001 : 62 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6339, tnet num: 1938, tinst num: 797, tnode num: 8641, tedge num: 11245.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 374 mslices, 374 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1938 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69560, over cnt = 164(0%), over = 255, worst = 7
PHY-1002 : len = 70696, over cnt = 101(0%), over = 127, worst = 5
PHY-1002 : len = 71488, over cnt = 46(0%), over = 56, worst = 3
PHY-1002 : len = 72344, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.127779s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (97.8%)

PHY-1001 : Congestion index: top1 = 32.28, top5 = 23.20, top10 = 18.07, top15 = 13.92.
PHY-1001 : End global routing;  0.175618s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (97.9%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 234, reserve = 202, peak = 234.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 494, reserve = 464, peak = 494.
PHY-1001 : End build detailed router design. 2.997378s wall, 2.859375s user + 0.109375s system = 2.968750s CPU (99.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 31512, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.021878s wall, 1.015625s user + 0.000000s system = 1.015625s CPU (99.4%)

PHY-1001 : Current memory(MB): used = 526, reserve = 497, peak = 526.
PHY-1001 : End phase 1; 1.027565s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (100.4%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 29% nets.
PHY-1001 : Routed 35% nets.
PHY-1001 : Routed 45% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 179872, over cnt = 34(0%), over = 34, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 526, reserve = 498, peak = 526.
PHY-1001 : End initial routed; 1.450033s wall, 2.171875s user + 0.171875s system = 2.343750s CPU (161.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1720(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.784   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.003   |  -45.353  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.320223s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (102.5%)

PHY-1001 : Current memory(MB): used = 530, reserve = 500, peak = 530.
PHY-1001 : End phase 2; 1.770350s wall, 2.500000s user + 0.171875s system = 2.671875s CPU (150.9%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 179872, over cnt = 34(0%), over = 34, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014468s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (108.0%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 179816, over cnt = 9(0%), over = 9, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.036211s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (86.3%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 179896, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.020469s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (305.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1720(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.784   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.003   |  -45.090  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.323587s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (96.6%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 5 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.172749s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (99.5%)

PHY-1001 : Current memory(MB): used = 546, reserve = 516, peak = 546.
PHY-1001 : End phase 3; 0.693666s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (105.9%)

PHY-1003 : Routed, final wirelength = 179896
PHY-1001 : Current memory(MB): used = 546, reserve = 517, peak = 546.
PHY-1001 : End export database. 0.010294s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (151.8%)

PHY-1001 : End detail routing;  6.667190s wall, 7.296875s user + 0.296875s system = 7.593750s CPU (113.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6339, tnet num: 1938, tinst num: 797, tnode num: 8641, tedge num: 11245.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[38] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[2] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[0] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[4] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[4] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[6] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[6] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[13] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[4] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[16] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[7] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg1_syn_182.mi[0] slack -2763ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_78.mi[0] slack -2627ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_78.mi[1] slack -2991ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_81.mi[0] slack -3003ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_81.mi[1] slack -2991ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_84.mi[0] slack -2871ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_84.mi[1] slack -2859ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_87.mi[0] slack -2871ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_87.mi[1] slack -2632ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_90.mi[0] slack -2500ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_90.mi[1] slack -2859ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_93.mi[0] slack -2763ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_93.mi[1] slack -2871ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_97.mi[0] slack -2871ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_97.mi[1] slack -2763ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_99.mi[0] slack -2763ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6449, tnet num: 1993, tinst num: 852, tnode num: 8751, tedge num: 11355.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg1_syn_182_mi[0] slack -537ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_78_mi[1] slack -208ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_78_mi[0] slack -645ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_81_mi[1] slack -682ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_81_mi[0] slack -583ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_87_mi[1] slack -662ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_87_mi[0] slack -445ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_84_mi[0] slack -342ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg2_syn_84_mi[1] slack -429ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_93_mi[0] slack -782ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_90_mi[1] slack -747ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_90_mi[0] slack -508ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_93_mi[1] slack -943ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_97_mi[0] slack -389ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_97_mi[1] slack -638ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_99_mi[0] slack -482ps
RUN-1001 : End hold fix;  2.971372s wall, 3.078125s user + 0.265625s system = 3.343750s CPU (112.5%)

RUN-1003 : finish command "route" in  10.134600s wall, 10.859375s user + 0.562500s system = 11.421875s CPU (112.7%)

RUN-1004 : used memory is 501 MB, reserved memory is 479 MB, peak memory is 546 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      887   out of  19600    4.53%
#reg                     1053   out of  19600    5.37%
#le                      1547
  #lut only               494   out of   1547   31.93%
  #reg only               660   out of   1547   42.66%
  #lut&reg                393   out of   1547   25.40%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         482
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         100
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    41
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1547   |686     |201     |1086    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1054   |282     |137     |864     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |21     |17      |4       |18      |0       |0       |
|    demodu                  |Demodulation                                     |434    |86      |41      |350     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |55     |28      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |5       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |14     |7       |0       |14      |0       |0       |
|    integ                   |Integration                                      |141    |29      |15      |113     |0       |0       |
|    modu                    |Modulation                                       |106    |51      |23      |102     |0       |1       |
|    rs422                   |Rs422Output                                      |316    |71      |46      |261     |0       |4       |
|    trans                   |SquareWaveGenerator                              |36     |28      |8       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |163    |131     |7       |115     |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |28      |7       |14      |0       |0       |
|    U1                      |uart_tx                                          |26     |20      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |102    |83      |0       |85      |0       |0       |
|  wendu                     |DS18B20                                          |306    |261     |45      |73      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1383  
    #2          2       335   
    #3          3       111   
    #4          4        22   
    #5        5-10       67   
    #6        11-50      32   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6449, tnet num: 1993, tinst num: 852, tnode num: 8751, tedge num: 11355.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1993 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 852
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1995, pip num: 14754
BIT-1002 : Init feedthrough completely, num: 8
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1334 valid insts, and 39398 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.119095s wall, 17.937500s user + 0.062500s system = 18.000000s CPU (577.1%)

RUN-1004 : used memory is 517 MB, reserved memory is 488 MB, peak memory is 668 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250811_161353.log"
