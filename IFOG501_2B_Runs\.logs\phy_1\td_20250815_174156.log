============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Fri Aug 15 17:41:56 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1582 instances
RUN-0007 : 369 luts, 956 seqs, 136 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2131 nets
RUN-1001 : 1553 nets have 2 pins
RUN-1001 : 476 nets have [3 - 5] pins
RUN-1001 : 59 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     252     
RUN-1001 :   No   |  No   |  Yes  |     125     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1580 instances, 369 luts, 956 seqs, 206 slices, 21 macros(206 instances: 136 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7587, tnet num: 2129, tinst num: 1580, tnode num: 10760, tedge num: 12907.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2129 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.260945s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (101.8%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 540431
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1580.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 462001, overlap = 18
PHY-3002 : Step(2): len = 435226, overlap = 20.25
PHY-3002 : Step(3): len = 417907, overlap = 20.25
PHY-3002 : Step(4): len = 407422, overlap = 13.5
PHY-3002 : Step(5): len = 392089, overlap = 18
PHY-3002 : Step(6): len = 378223, overlap = 11.25
PHY-3002 : Step(7): len = 370977, overlap = 13.5
PHY-3002 : Step(8): len = 361870, overlap = 15.75
PHY-3002 : Step(9): len = 352368, overlap = 13.5
PHY-3002 : Step(10): len = 345460, overlap = 11.25
PHY-3002 : Step(11): len = 339462, overlap = 13.5
PHY-3002 : Step(12): len = 330403, overlap = 13.5
PHY-3002 : Step(13): len = 322696, overlap = 15.75
PHY-3002 : Step(14): len = 317651, overlap = 15.75
PHY-3002 : Step(15): len = 310137, overlap = 15.75
PHY-3002 : Step(16): len = 303496, overlap = 15.75
PHY-3002 : Step(17): len = 298618, overlap = 15.75
PHY-3002 : Step(18): len = 291099, overlap = 15.75
PHY-3002 : Step(19): len = 285087, overlap = 15.75
PHY-3002 : Step(20): len = 279420, overlap = 15.75
PHY-3002 : Step(21): len = 273603, overlap = 15.75
PHY-3002 : Step(22): len = 266290, overlap = 15.75
PHY-3002 : Step(23): len = 261153, overlap = 15.75
PHY-3002 : Step(24): len = 256604, overlap = 15.75
PHY-3002 : Step(25): len = 247410, overlap = 13.5
PHY-3002 : Step(26): len = 241605, overlap = 20.25
PHY-3002 : Step(27): len = 238107, overlap = 20.25
PHY-3002 : Step(28): len = 229102, overlap = 20.25
PHY-3002 : Step(29): len = 219695, overlap = 20.25
PHY-3002 : Step(30): len = 216577, overlap = 20.25
PHY-3002 : Step(31): len = 210271, overlap = 20.25
PHY-3002 : Step(32): len = 196727, overlap = 20.25
PHY-3002 : Step(33): len = 192778, overlap = 20.25
PHY-3002 : Step(34): len = 190392, overlap = 20.25
PHY-3002 : Step(35): len = 157816, overlap = 20.25
PHY-3002 : Step(36): len = 145084, overlap = 20.25
PHY-3002 : Step(37): len = 144110, overlap = 20.25
PHY-3002 : Step(38): len = 128922, overlap = 20.25
PHY-3002 : Step(39): len = 122179, overlap = 20.25
PHY-3002 : Step(40): len = 120381, overlap = 20.25
PHY-3002 : Step(41): len = 118155, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.36706e-05
PHY-3002 : Step(42): len = 118846, overlap = 18
PHY-3002 : Step(43): len = 117615, overlap = 15.75
PHY-3002 : Step(44): len = 116589, overlap = 15.75
PHY-3002 : Step(45): len = 114001, overlap = 15.75
PHY-3002 : Step(46): len = 109380, overlap = 11.25
PHY-3002 : Step(47): len = 106934, overlap = 13.5
PHY-3002 : Step(48): len = 105687, overlap = 11.25
PHY-3002 : Step(49): len = 103964, overlap = 11.25
PHY-3002 : Step(50): len = 100915, overlap = 11.25
PHY-3002 : Step(51): len = 97129.9, overlap = 15.75
PHY-3002 : Step(52): len = 96466.3, overlap = 15.75
PHY-3002 : Step(53): len = 94807.4, overlap = 15.75
PHY-3002 : Step(54): len = 91835.4, overlap = 15.75
PHY-3002 : Step(55): len = 91753.6, overlap = 15.75
PHY-3002 : Step(56): len = 89378, overlap = 13.5
PHY-3002 : Step(57): len = 87725.4, overlap = 11.25
PHY-3002 : Step(58): len = 86590.3, overlap = 13.5
PHY-3002 : Step(59): len = 85819, overlap = 15.75
PHY-3002 : Step(60): len = 83467.7, overlap = 15.75
PHY-3002 : Step(61): len = 81700.5, overlap = 16.5
PHY-3002 : Step(62): len = 79597.1, overlap = 17.8125
PHY-3002 : Step(63): len = 79543, overlap = 17.6875
PHY-3002 : Step(64): len = 77644.8, overlap = 17.6875
PHY-3002 : Step(65): len = 75611.9, overlap = 17.875
PHY-3002 : Step(66): len = 75835.7, overlap = 17.75
PHY-3002 : Step(67): len = 74462.8, overlap = 18.0625
PHY-3002 : Step(68): len = 73099.8, overlap = 18.1875
PHY-3002 : Step(69): len = 73025.6, overlap = 20.5
PHY-3002 : Step(70): len = 71521, overlap = 18.5
PHY-3002 : Step(71): len = 70267, overlap = 18.5625
PHY-3002 : Step(72): len = 69031.3, overlap = 16.25
PHY-3002 : Step(73): len = 68037.1, overlap = 16.25
PHY-3002 : Step(74): len = 67755, overlap = 16.25
PHY-3002 : Step(75): len = 67540.1, overlap = 18.4375
PHY-3002 : Step(76): len = 67464.5, overlap = 18.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000167341
PHY-3002 : Step(77): len = 67761.1, overlap = 18.25
PHY-3002 : Step(78): len = 67705, overlap = 16
PHY-3002 : Step(79): len = 67673.6, overlap = 16
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000334682
PHY-3002 : Step(80): len = 67894.2, overlap = 16
PHY-3002 : Step(81): len = 68013.9, overlap = 16
PHY-3001 : Before Legalized: Len = 68013.9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006681s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 72365.9, Over = 2.5
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2129 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.062828s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(82): len = 72224.5, overlap = 5.40625
PHY-3002 : Step(83): len = 71578.6, overlap = 6.46875
PHY-3002 : Step(84): len = 70352.9, overlap = 6.53125
PHY-3002 : Step(85): len = 69644.1, overlap = 6.71875
PHY-3002 : Step(86): len = 68753.7, overlap = 6.65625
PHY-3002 : Step(87): len = 68316.2, overlap = 6.0625
PHY-3002 : Step(88): len = 66392.4, overlap = 6.84375
PHY-3002 : Step(89): len = 65134.8, overlap = 8.34375
PHY-3002 : Step(90): len = 64596.4, overlap = 9.71875
PHY-3002 : Step(91): len = 63891.7, overlap = 9.59375
PHY-3002 : Step(92): len = 63374.5, overlap = 9.46875
PHY-3002 : Step(93): len = 62548.3, overlap = 9.46875
PHY-3002 : Step(94): len = 61928.5, overlap = 9.78125
PHY-3002 : Step(95): len = 61452.3, overlap = 9.78125
PHY-3002 : Step(96): len = 60840.2, overlap = 9.6875
PHY-3002 : Step(97): len = 60418.7, overlap = 9.75
PHY-3002 : Step(98): len = 59686.7, overlap = 8.65625
PHY-3002 : Step(99): len = 59646.6, overlap = 8.71875
PHY-3002 : Step(100): len = 59119.9, overlap = 8
PHY-3002 : Step(101): len = 58612.2, overlap = 7.71875
PHY-3002 : Step(102): len = 57256, overlap = 7.3125
PHY-3002 : Step(103): len = 56973.3, overlap = 6.46875
PHY-3002 : Step(104): len = 56143.9, overlap = 6.75
PHY-3002 : Step(105): len = 55559.1, overlap = 8.75
PHY-3002 : Step(106): len = 55119.7, overlap = 10.4375
PHY-3002 : Step(107): len = 54933.9, overlap = 10.5
PHY-3002 : Step(108): len = 54516.5, overlap = 10.9375
PHY-3002 : Step(109): len = 54222.9, overlap = 11
PHY-3002 : Step(110): len = 53692.1, overlap = 10.6875
PHY-3002 : Step(111): len = 53562.1, overlap = 10.6875
PHY-3002 : Step(112): len = 53438.4, overlap = 10.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000629115
PHY-3002 : Step(113): len = 53333.2, overlap = 11.0938
PHY-3002 : Step(114): len = 53376.2, overlap = 17.6562
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00125823
PHY-3002 : Step(115): len = 53288.5, overlap = 17.6562
PHY-3002 : Step(116): len = 53237, overlap = 15.625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2129 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.058183s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (107.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.93577e-05
PHY-3002 : Step(117): len = 53580, overlap = 44.5
PHY-3002 : Step(118): len = 54607, overlap = 37.9062
PHY-3002 : Step(119): len = 55310.7, overlap = 39.5938
PHY-3002 : Step(120): len = 55033.9, overlap = 38.8438
PHY-3002 : Step(121): len = 54594.3, overlap = 39.9375
PHY-3002 : Step(122): len = 54556.7, overlap = 38.2812
PHY-3002 : Step(123): len = 54394.4, overlap = 38.3125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000178715
PHY-3002 : Step(124): len = 54531.1, overlap = 37.5938
PHY-3002 : Step(125): len = 54716.8, overlap = 35.2188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000357431
PHY-3002 : Step(126): len = 55571.7, overlap = 27.5312
PHY-3002 : Step(127): len = 56555.3, overlap = 29.2812
PHY-3002 : Step(128): len = 56815.6, overlap = 26.6562
PHY-3002 : Step(129): len = 56831, overlap = 26.25
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7587, tnet num: 2129, tinst num: 1580, tnode num: 10760, tedge num: 12907.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 93.44 peak overflow 4.22
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2131.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 59592, over cnt = 233(0%), over = 1014, worst = 23
PHY-1001 : End global iterations;  0.065656s wall, 0.078125s user + 0.015625s system = 0.093750s CPU (142.8%)

PHY-1001 : Congestion index: top1 = 45.32, top5 = 25.98, top10 = 16.54, top15 = 11.84.
PHY-1001 : End incremental global routing;  0.116226s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (134.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2129 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.072518s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (86.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.216579s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (115.4%)

OPT-1001 : Current memory(MB): used = 209, reserve = 176, peak = 209.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1626/2131.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 59592, over cnt = 233(0%), over = 1014, worst = 23
PHY-1002 : len = 65000, over cnt = 190(0%), over = 530, worst = 20
PHY-1002 : len = 71408, over cnt = 42(0%), over = 51, worst = 4
PHY-1002 : len = 71816, over cnt = 17(0%), over = 21, worst = 2
PHY-1002 : len = 73112, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.107010s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (116.8%)

PHY-1001 : Congestion index: top1 = 38.77, top5 = 25.61, top10 = 18.37, top15 = 13.67.
OPT-1001 : End congestion update;  0.151376s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (113.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2129 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056377s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (110.9%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.211375s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (110.9%)

OPT-1001 : Current memory(MB): used = 212, reserve = 179, peak = 212.
OPT-1001 : End physical optimization;  0.669682s wall, 0.703125s user + 0.015625s system = 0.718750s CPU (107.3%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 369 LUT to BLE ...
SYN-4008 : Packed 369 LUT and 190 SEQ to BLE.
SYN-4003 : Packing 766 remaining SEQ's ...
SYN-4005 : Packed 90 SEQ with LUT/SLICE
SYN-4006 : 105 single LUT's are left
SYN-4006 : 676 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1045/1366 primitive instances ...
PHY-3001 : End packing;  0.048726s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (96.2%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 807 instances
RUN-1001 : 378 mslices, 378 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1949 nets
RUN-1001 : 1370 nets have 2 pins
RUN-1001 : 475 nets have [3 - 5] pins
RUN-1001 : 61 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 805 instances, 756 slices, 21 macros(206 instances: 136 mslices 70 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 56701, Over = 53
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6375, tnet num: 1947, tinst num: 805, tnode num: 8680, tedge num: 11304.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1947 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.278404s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (95.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.84145e-05
PHY-3002 : Step(130): len = 55982.8, overlap = 51.75
PHY-3002 : Step(131): len = 55573.2, overlap = 52.75
PHY-3002 : Step(132): len = 55361.8, overlap = 53.5
PHY-3002 : Step(133): len = 55415, overlap = 54.25
PHY-3002 : Step(134): len = 55673.6, overlap = 55.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.68289e-05
PHY-3002 : Step(135): len = 55768, overlap = 55.5
PHY-3002 : Step(136): len = 55920.4, overlap = 55.75
PHY-3002 : Step(137): len = 56542.6, overlap = 54.25
PHY-3002 : Step(138): len = 56934.5, overlap = 52
PHY-3002 : Step(139): len = 57127.3, overlap = 50.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000153658
PHY-3002 : Step(140): len = 57179.1, overlap = 49.25
PHY-3002 : Step(141): len = 57777.9, overlap = 44.75
PHY-3001 : Before Legalized: Len = 57777.9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.081987s wall, 0.078125s user + 0.156250s system = 0.234375s CPU (285.9%)

PHY-3001 : After Legalized: Len = 70004.3, Over = 0
PHY-3001 : Trial Legalized: Len = 70004.3
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1947 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.050032s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000849386
PHY-3002 : Step(142): len = 66214.1, overlap = 6.5
PHY-3002 : Step(143): len = 64268.6, overlap = 11
PHY-3002 : Step(144): len = 62539.7, overlap = 18.25
PHY-3002 : Step(145): len = 61771.1, overlap = 19.25
PHY-3002 : Step(146): len = 60912.6, overlap = 22
PHY-3002 : Step(147): len = 60515.6, overlap = 21.75
PHY-3002 : Step(148): len = 60182.3, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00163507
PHY-3002 : Step(149): len = 60409.4, overlap = 20
PHY-3002 : Step(150): len = 60487.4, overlap = 19.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00327014
PHY-3002 : Step(151): len = 60634.9, overlap = 19
PHY-3002 : Step(152): len = 60694, overlap = 18.75
PHY-3001 : Before Legalized: Len = 60694
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005085s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (307.3%)

PHY-3001 : After Legalized: Len = 64391.3, Over = 0
PHY-3001 : Legalized: Len = 64391.3, Over = 0
PHY-3001 : Spreading special nets. 7 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005663s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 14 instances has been re-located, deltaX = 6, deltaY = 10, maxDist = 2.
PHY-3001 : Final: Len = 64683.3, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6375, tnet num: 1947, tinst num: 805, tnode num: 8680, tedge num: 11304.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 85/1949.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70936, over cnt = 160(0%), over = 252, worst = 7
PHY-1002 : len = 71960, over cnt = 98(0%), over = 125, worst = 3
PHY-1002 : len = 73608, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 73672, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.107331s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (145.6%)

PHY-1001 : Congestion index: top1 = 32.56, top5 = 23.16, top10 = 17.84, top15 = 13.95.
PHY-1001 : End incremental global routing;  0.159734s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (127.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1947 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059657s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.249065s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (119.2%)

OPT-1001 : Current memory(MB): used = 214, reserve = 182, peak = 214.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1713/1949.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73672, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007206s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.56, top5 = 23.16, top10 = 17.84, top15 = 13.95.
OPT-1001 : End congestion update;  0.055671s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (84.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1947 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049404s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (126.5%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 765 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 805 instances, 756 slices, 21 macros(206 instances: 136 mslices 70 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 64657, Over = 0
PHY-3001 : End spreading;  0.006683s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 64657, Over = 0
PHY-3001 : End incremental legalization;  0.045526s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (171.6%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.165721s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (113.1%)

OPT-1001 : Current memory(MB): used = 219, reserve = 187, peak = 220.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1947 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053040s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (117.8%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1709/1949.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73648, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.009710s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.56, top5 = 23.17, top10 = 17.85, top15 = 13.95.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1947 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050338s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.1%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 32.103448
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.848938s wall, 0.906250s user + 0.015625s system = 0.921875s CPU (108.6%)

RUN-1003 : finish command "place" in  5.091322s wall, 7.625000s user + 2.875000s system = 10.500000s CPU (206.2%)

RUN-1004 : used memory is 196 MB, reserved memory is 163 MB, peak memory is 220 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 807 instances
RUN-1001 : 378 mslices, 378 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1949 nets
RUN-1001 : 1370 nets have 2 pins
RUN-1001 : 475 nets have [3 - 5] pins
RUN-1001 : 61 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6375, tnet num: 1947, tinst num: 805, tnode num: 8680, tedge num: 11304.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 378 mslices, 378 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1947 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69864, over cnt = 162(0%), over = 252, worst = 7
PHY-1002 : len = 71192, over cnt = 88(0%), over = 108, worst = 3
PHY-1002 : len = 72536, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 72648, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.124112s wall, 0.156250s user + 0.046875s system = 0.203125s CPU (163.7%)

PHY-1001 : Congestion index: top1 = 32.33, top5 = 22.96, top10 = 17.59, top15 = 13.72.
PHY-1001 : End global routing;  0.175537s wall, 0.203125s user + 0.046875s system = 0.250000s CPU (142.4%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 234, reserve = 203, peak = 235.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 494, reserve = 466, peak = 494.
PHY-1001 : End build detailed router design. 3.201057s wall, 3.156250s user + 0.031250s system = 3.187500s CPU (99.6%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 30648, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.107988s wall, 1.093750s user + 0.015625s system = 1.109375s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 526, reserve = 499, peak = 526.
PHY-1001 : End phase 1; 1.114971s wall, 1.093750s user + 0.015625s system = 1.109375s CPU (99.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 37% nets.
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 178336, over cnt = 42(0%), over = 42, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 529, reserve = 500, peak = 530.
PHY-1001 : End initial routed; 1.657133s wall, 3.062500s user + 0.187500s system = 3.250000s CPU (196.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1725(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.720   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.959   |  -45.422  |  22   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.347245s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (103.5%)

PHY-1001 : Current memory(MB): used = 531, reserve = 502, peak = 531.
PHY-1001 : End phase 2; 2.004472s wall, 3.421875s user + 0.187500s system = 3.609375s CPU (180.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 178336, over cnt = 42(0%), over = 42, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015880s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (98.4%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 178184, over cnt = 8(0%), over = 8, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.036143s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (43.2%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 178216, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.021254s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (147.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1725(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.720   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.959   |  -45.422  |  22   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.345406s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (99.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 7 feed throughs used by 6 nets
PHY-1001 : End commit to database; 0.170002s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (101.1%)

PHY-1001 : Current memory(MB): used = 545, reserve = 517, peak = 545.
PHY-1001 : End phase 3; 0.720437s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (97.6%)

PHY-1003 : Routed, final wirelength = 178216
PHY-1001 : Current memory(MB): used = 546, reserve = 517, peak = 546.
PHY-1001 : End export database. 0.011321s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (138.0%)

PHY-1001 : End detail routing;  7.233562s wall, 8.562500s user + 0.234375s system = 8.796875s CPU (121.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6375, tnet num: 1947, tinst num: 805, tnode num: 8680, tedge num: 11304.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : Too much hold violation, please check the src code.
RUN-1003 : finish command "route" in  8.082348s wall, 9.437500s user + 0.281250s system = 9.718750s CPU (120.2%)

RUN-1004 : used memory is 498 MB, reserved memory is 469 MB, peak memory is 546 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   15
  #output                  21
  #inout                    1

Utilization Statistics
#lut                      788   out of  19600    4.02%
#reg                     1058   out of  19600    5.40%
#le                      1464
  #lut only               406   out of   1464   27.73%
  #reg only               676   out of   1464   46.17%
  #lut&reg                382   out of   1464   26.09%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    13
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         484
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         99
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    42
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
  RxTransmit       INPUT        M14        LVCMOS33          N/A          PULLUP      IREG    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1464   |582     |206     |1092    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1055   |276     |142     |869     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |23     |19      |4       |20      |0       |0       |
|    demodu                  |Demodulation                                     |432    |80      |46      |351     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |54     |32      |6       |45      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |7       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |13     |7       |0       |13      |0       |0       |
|    integ                   |Integration                                      |139    |15      |15      |111     |0       |0       |
|    modu                    |Modulation                                       |105    |58      |23      |101     |0       |1       |
|    rs422                   |Rs422Output                                      |321    |77      |46      |265     |0       |4       |
|    trans                   |SquareWaveGenerator                              |35     |27      |8       |21      |0       |0       |
|  u_uart                    |UART_Control                                     |174    |128     |7       |117     |0       |0       |
|    U0                      |speed_select_Tx                                  |32     |25      |7       |14      |0       |0       |
|    U1                      |uart_tx                                          |24     |18      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |118    |85      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |211    |166     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1331  
    #2          2       339   
    #3          3       105   
    #4          4        31   
    #5        5-10       66   
    #6        11-50      32   
    #7       101-500     1    
  Average     1.98            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6375, tnet num: 1947, tinst num: 805, tnode num: 8680, tedge num: 11304.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1947 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 805
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1949, pip num: 14483
BIT-1002 : Init feedthrough completely, num: 7
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1337 valid insts, and 38091 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.227571s wall, 17.640625s user + 0.031250s system = 17.671875s CPU (547.5%)

RUN-1004 : used memory is 514 MB, reserved memory is 489 MB, peak memory is 662 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250815_174156.log"
