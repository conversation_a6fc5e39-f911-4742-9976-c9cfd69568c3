============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Tue Aug  5 11:14:33 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1572 instances
RUN-0007 : 365 luts, 953 seqs, 133 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2116 nets
RUN-1001 : 1542 nets have 2 pins
RUN-1001 : 474 nets have [3 - 5] pins
RUN-1001 : 58 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     251     
RUN-1001 :   No   |  No   |  Yes  |     123     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1570 instances, 365 luts, 953 seqs, 203 slices, 21 macros(203 instances: 133 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7529, tnet num: 2114, tinst num: 1570, tnode num: 10691, tedge num: 12794.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2114 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.264331s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (100.5%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 515473
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1570.
PHY-3001 : End clustering;  0.000020s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 442447, overlap = 20.25
PHY-3002 : Step(2): len = 427291, overlap = 18
PHY-3002 : Step(3): len = 413777, overlap = 18
PHY-3002 : Step(4): len = 403335, overlap = 15.75
PHY-3002 : Step(5): len = 387622, overlap = 15.75
PHY-3002 : Step(6): len = 378045, overlap = 13.5
PHY-3002 : Step(7): len = 369795, overlap = 13.5
PHY-3002 : Step(8): len = 359952, overlap = 13.5
PHY-3002 : Step(9): len = 353120, overlap = 13.5
PHY-3002 : Step(10): len = 346244, overlap = 13.5
PHY-3002 : Step(11): len = 338087, overlap = 11.25
PHY-3002 : Step(12): len = 330454, overlap = 11.25
PHY-3002 : Step(13): len = 324545, overlap = 11.25
PHY-3002 : Step(14): len = 316870, overlap = 11.25
PHY-3002 : Step(15): len = 309647, overlap = 13.5
PHY-3002 : Step(16): len = 304442, overlap = 13.5
PHY-3002 : Step(17): len = 298366, overlap = 13.5
PHY-3002 : Step(18): len = 290953, overlap = 13.5
PHY-3002 : Step(19): len = 285281, overlap = 13.5
PHY-3002 : Step(20): len = 280526, overlap = 13.5
PHY-3002 : Step(21): len = 273751, overlap = 13.5
PHY-3002 : Step(22): len = 266477, overlap = 13.5
PHY-3002 : Step(23): len = 261859, overlap = 13.5
PHY-3002 : Step(24): len = 256081, overlap = 15.75
PHY-3002 : Step(25): len = 250479, overlap = 20.25
PHY-3002 : Step(26): len = 245229, overlap = 20.25
PHY-3002 : Step(27): len = 240330, overlap = 20.25
PHY-3002 : Step(28): len = 234566, overlap = 20.25
PHY-3002 : Step(29): len = 230172, overlap = 20.25
PHY-3002 : Step(30): len = 224655, overlap = 20.25
PHY-3002 : Step(31): len = 218220, overlap = 20.25
PHY-3002 : Step(32): len = 214121, overlap = 20.25
PHY-3002 : Step(33): len = 209937, overlap = 20.25
PHY-3002 : Step(34): len = 203448, overlap = 20.25
PHY-3002 : Step(35): len = 198339, overlap = 20.25
PHY-3002 : Step(36): len = 193825, overlap = 20.25
PHY-3002 : Step(37): len = 189783, overlap = 20.25
PHY-3002 : Step(38): len = 184390, overlap = 20.25
PHY-3002 : Step(39): len = 180328, overlap = 20.25
PHY-3002 : Step(40): len = 176065, overlap = 20.25
PHY-3002 : Step(41): len = 170303, overlap = 20.25
PHY-3002 : Step(42): len = 165845, overlap = 20.25
PHY-3002 : Step(43): len = 162971, overlap = 20.25
PHY-3002 : Step(44): len = 157038, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000104188
PHY-3002 : Step(45): len = 158198, overlap = 18
PHY-3002 : Step(46): len = 156795, overlap = 13.5
PHY-3002 : Step(47): len = 154654, overlap = 13.5
PHY-3002 : Step(48): len = 149811, overlap = 6.75
PHY-3002 : Step(49): len = 145908, overlap = 6.75
PHY-3002 : Step(50): len = 144225, overlap = 9
PHY-3002 : Step(51): len = 139824, overlap = 9
PHY-3002 : Step(52): len = 137694, overlap = 9
PHY-3002 : Step(53): len = 135349, overlap = 11.25
PHY-3002 : Step(54): len = 132319, overlap = 9
PHY-3002 : Step(55): len = 130213, overlap = 13.5
PHY-3002 : Step(56): len = 128385, overlap = 11.25
PHY-3002 : Step(57): len = 126282, overlap = 11.25
PHY-3002 : Step(58): len = 123574, overlap = 9
PHY-3002 : Step(59): len = 118985, overlap = 11.25
PHY-3002 : Step(60): len = 117240, overlap = 13.5
PHY-3002 : Step(61): len = 115972, overlap = 13.5
PHY-3002 : Step(62): len = 110388, overlap = 13.5
PHY-3002 : Step(63): len = 104223, overlap = 15.75
PHY-3002 : Step(64): len = 103142, overlap = 15.75
PHY-3002 : Step(65): len = 100129, overlap = 11.25
PHY-3002 : Step(66): len = 98057.8, overlap = 13.5
PHY-3002 : Step(67): len = 96981.1, overlap = 13.5
PHY-3002 : Step(68): len = 96261.6, overlap = 13.5
PHY-3002 : Step(69): len = 92573.1, overlap = 13.5
PHY-3002 : Step(70): len = 91249.6, overlap = 15.75
PHY-3002 : Step(71): len = 86587.7, overlap = 13.75
PHY-3002 : Step(72): len = 85661.5, overlap = 14
PHY-3002 : Step(73): len = 84584.8, overlap = 14
PHY-3002 : Step(74): len = 82809.9, overlap = 13.9375
PHY-3002 : Step(75): len = 80071.4, overlap = 14.375
PHY-3002 : Step(76): len = 78285.3, overlap = 14.125
PHY-3002 : Step(77): len = 77583.7, overlap = 14.3125
PHY-3002 : Step(78): len = 76706.1, overlap = 14.1875
PHY-3002 : Step(79): len = 76377, overlap = 14.1875
PHY-3002 : Step(80): len = 76090.4, overlap = 12.0625
PHY-3002 : Step(81): len = 75812.4, overlap = 14.375
PHY-3002 : Step(82): len = 74636.7, overlap = 12.125
PHY-3002 : Step(83): len = 73964.5, overlap = 14.375
PHY-3002 : Step(84): len = 73695.6, overlap = 14.375
PHY-3002 : Step(85): len = 72587.1, overlap = 14.5
PHY-3002 : Step(86): len = 71736.9, overlap = 14.5
PHY-3002 : Step(87): len = 69958.4, overlap = 11.75
PHY-3002 : Step(88): len = 68628.1, overlap = 9.1875
PHY-3002 : Step(89): len = 67692.7, overlap = 9.1875
PHY-3002 : Step(90): len = 67584.1, overlap = 9.1875
PHY-3002 : Step(91): len = 66551.2, overlap = 9.1875
PHY-3002 : Step(92): len = 64519.6, overlap = 9.6875
PHY-3002 : Step(93): len = 63449, overlap = 10.875
PHY-3002 : Step(94): len = 62449.8, overlap = 8.25
PHY-3002 : Step(95): len = 61766.8, overlap = 8.25
PHY-3002 : Step(96): len = 61762.4, overlap = 6
PHY-3002 : Step(97): len = 61447.2, overlap = 5.875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000208376
PHY-3002 : Step(98): len = 61917.3, overlap = 7.9375
PHY-3002 : Step(99): len = 62203.4, overlap = 7.9375
PHY-3002 : Step(100): len = 62299.7, overlap = 7.9375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000416753
PHY-3002 : Step(101): len = 62389.6, overlap = 7.9375
PHY-3002 : Step(102): len = 62436.2, overlap = 7.9375
PHY-3001 : Before Legalized: Len = 62436.2
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007876s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (198.4%)

PHY-3001 : After Legalized: Len = 64483.6, Over = 1.1875
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2114 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.060914s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00650644
PHY-3002 : Step(103): len = 64742.5, overlap = 10.0625
PHY-3002 : Step(104): len = 63484.8, overlap = 6.84375
PHY-3002 : Step(105): len = 63471.3, overlap = 6.875
PHY-3002 : Step(106): len = 62321.7, overlap = 7
PHY-3002 : Step(107): len = 61172.1, overlap = 7.40625
PHY-3002 : Step(108): len = 60722.6, overlap = 9.5625
PHY-3002 : Step(109): len = 59899, overlap = 10.375
PHY-3002 : Step(110): len = 58537.7, overlap = 10.5
PHY-3002 : Step(111): len = 56835.2, overlap = 10.3125
PHY-3002 : Step(112): len = 56219.8, overlap = 10.4375
PHY-3002 : Step(113): len = 55194.8, overlap = 10.75
PHY-3002 : Step(114): len = 54579, overlap = 11.3125
PHY-3002 : Step(115): len = 54111.3, overlap = 12.625
PHY-3002 : Step(116): len = 53464.2, overlap = 15.75
PHY-3002 : Step(117): len = 53129.2, overlap = 18.5625
PHY-3002 : Step(118): len = 53055.9, overlap = 18.625
PHY-3002 : Step(119): len = 51981.3, overlap = 18.375
PHY-3002 : Step(120): len = 51154.2, overlap = 18.3125
PHY-3002 : Step(121): len = 50606.6, overlap = 18.5
PHY-3002 : Step(122): len = 50072.1, overlap = 18.875
PHY-3002 : Step(123): len = 49267.2, overlap = 19.7812
PHY-3002 : Step(124): len = 48153.6, overlap = 22.0938
PHY-3002 : Step(125): len = 47756.5, overlap = 21.5938
PHY-3002 : Step(126): len = 47146, overlap = 21.4062
PHY-3002 : Step(127): len = 46852, overlap = 21.6875
PHY-3002 : Step(128): len = 46370.8, overlap = 22.4688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0130129
PHY-3002 : Step(129): len = 46327.1, overlap = 22.4062
PHY-3002 : Step(130): len = 46327.1, overlap = 22.4062
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2114 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.061326s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.51443e-05
PHY-3002 : Step(131): len = 47118.6, overlap = 81.625
PHY-3002 : Step(132): len = 47545.2, overlap = 81.875
PHY-3002 : Step(133): len = 48255.5, overlap = 76.0312
PHY-3002 : Step(134): len = 48607.8, overlap = 68.875
PHY-3002 : Step(135): len = 48414.9, overlap = 67.625
PHY-3002 : Step(136): len = 48490.7, overlap = 60.2812
PHY-3002 : Step(137): len = 48607, overlap = 57.9375
PHY-3002 : Step(138): len = 48682.6, overlap = 55.5312
PHY-3002 : Step(139): len = 48530.2, overlap = 53.6875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000170289
PHY-3002 : Step(140): len = 48317.4, overlap = 53.25
PHY-3002 : Step(141): len = 48523.4, overlap = 52.5625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000340577
PHY-3002 : Step(142): len = 48988.5, overlap = 51.5625
PHY-3002 : Step(143): len = 50215.9, overlap = 48.7812
PHY-3002 : Step(144): len = 51060.8, overlap = 46.25
PHY-3002 : Step(145): len = 51791.9, overlap = 40.5625
PHY-3002 : Step(146): len = 51874.3, overlap = 39.6875
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7529, tnet num: 2114, tinst num: 1570, tnode num: 10691, tedge num: 12794.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 106.25 peak overflow 3.28
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2116.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54648, over cnt = 213(0%), over = 1088, worst = 27
PHY-1001 : End global iterations;  0.089765s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (174.1%)

PHY-1001 : Congestion index: top1 = 52.09, top5 = 25.80, top10 = 16.24, top15 = 11.62.
PHY-1001 : End incremental global routing;  0.140591s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (144.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2114 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067432s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (92.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.237091s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (125.2%)

OPT-1001 : Current memory(MB): used = 208, reserve = 175, peak = 208.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1651/2116.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54648, over cnt = 213(0%), over = 1088, worst = 27
PHY-1002 : len = 62664, over cnt = 204(0%), over = 625, worst = 19
PHY-1002 : len = 69456, over cnt = 60(0%), over = 96, worst = 8
PHY-1002 : len = 71368, over cnt = 14(0%), over = 14, worst = 1
PHY-1002 : len = 72472, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.111543s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (112.1%)

PHY-1001 : Congestion index: top1 = 45.22, top5 = 26.14, top10 = 18.68, top15 = 13.95.
OPT-1001 : End congestion update;  0.154502s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (111.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2114 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.054826s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (114.0%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.212670s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (110.2%)

OPT-1001 : Current memory(MB): used = 212, reserve = 179, peak = 212.
OPT-1001 : End physical optimization;  0.691000s wall, 0.750000s user + 0.015625s system = 0.765625s CPU (110.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 365 LUT to BLE ...
SYN-4008 : Packed 365 LUT and 188 SEQ to BLE.
SYN-4003 : Packing 765 remaining SEQ's ...
SYN-4005 : Packed 88 SEQ with LUT/SLICE
SYN-4006 : 103 single LUT's are left
SYN-4006 : 677 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1042/1360 primitive instances ...
PHY-3001 : End packing;  0.046677s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.4%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 804 instances
RUN-1001 : 377 mslices, 376 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1936 nets
RUN-1001 : 1367 nets have 2 pins
RUN-1001 : 463 nets have [3 - 5] pins
RUN-1001 : 64 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 802 instances, 753 slices, 21 macros(203 instances: 133 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 51859, Over = 61.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6323, tnet num: 1934, tinst num: 802, tnode num: 8619, tedge num: 11197.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.282214s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.61355e-05
PHY-3002 : Step(147): len = 51132.3, overlap = 60.5
PHY-3002 : Step(148): len = 50346.8, overlap = 63.5
PHY-3002 : Step(149): len = 49650.5, overlap = 64.25
PHY-3002 : Step(150): len = 49183.5, overlap = 64.75
PHY-3002 : Step(151): len = 48843.9, overlap = 65
PHY-3002 : Step(152): len = 48684.9, overlap = 68.25
PHY-3002 : Step(153): len = 48324.1, overlap = 67.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.22709e-05
PHY-3002 : Step(154): len = 48556.1, overlap = 63
PHY-3002 : Step(155): len = 49930.6, overlap = 58.5
PHY-3002 : Step(156): len = 50856.1, overlap = 52.75
PHY-3002 : Step(157): len = 51367.2, overlap = 51.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000144542
PHY-3002 : Step(158): len = 51872.7, overlap = 49.25
PHY-3002 : Step(159): len = 53043.4, overlap = 48.75
PHY-3002 : Step(160): len = 53399.3, overlap = 47.75
PHY-3002 : Step(161): len = 52500.7, overlap = 47.5
PHY-3002 : Step(162): len = 51958.3, overlap = 48
PHY-3002 : Step(163): len = 51706.9, overlap = 49.75
PHY-3001 : Before Legalized: Len = 51706.9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.080171s wall, 0.046875s user + 0.156250s system = 0.203125s CPU (253.4%)

PHY-3001 : After Legalized: Len = 65600.7, Over = 0
PHY-3001 : Trial Legalized: Len = 65600.7
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.048772s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (96.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000749579
PHY-3002 : Step(164): len = 61829.1, overlap = 8.75
PHY-3002 : Step(165): len = 60001.2, overlap = 12.75
PHY-3002 : Step(166): len = 57650.4, overlap = 16.75
PHY-3002 : Step(167): len = 56634.9, overlap = 20.5
PHY-3002 : Step(168): len = 56013.5, overlap = 23.75
PHY-3002 : Step(169): len = 55344.9, overlap = 22.75
PHY-3002 : Step(170): len = 54849.3, overlap = 22.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00149916
PHY-3002 : Step(171): len = 55249.8, overlap = 21.25
PHY-3002 : Step(172): len = 55277.3, overlap = 22.25
PHY-3002 : Step(173): len = 55160.7, overlap = 21.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00299832
PHY-3002 : Step(174): len = 55309.4, overlap = 21.5
PHY-3002 : Step(175): len = 55341.3, overlap = 21.5
PHY-3001 : Before Legalized: Len = 55341.3
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005367s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 59605.8, Over = 0
PHY-3001 : Legalized: Len = 59605.8, Over = 0
PHY-3001 : Spreading special nets. 15 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005828s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 24 instances has been re-located, deltaX = 8, deltaY = 14, maxDist = 1.
PHY-3001 : Final: Len = 59893.8, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6323, tnet num: 1934, tinst num: 802, tnode num: 8619, tedge num: 11197.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 53/1936.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 66112, over cnt = 155(0%), over = 232, worst = 4
PHY-1002 : len = 67128, over cnt = 72(0%), over = 83, worst = 3
PHY-1002 : len = 67896, over cnt = 20(0%), over = 24, worst = 3
PHY-1002 : len = 68224, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 68320, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.116376s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (120.8%)

PHY-1001 : Congestion index: top1 = 31.27, top5 = 22.63, top10 = 17.47, top15 = 13.44.
PHY-1001 : End incremental global routing;  0.167835s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (121.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058222s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (80.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.255364s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (110.1%)

OPT-1001 : Current memory(MB): used = 215, reserve = 183, peak = 215.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1714/1936.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68320, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005970s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.27, top5 = 22.63, top10 = 17.47, top15 = 13.44.
OPT-1001 : End congestion update;  0.053139s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (117.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049552s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.6%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 762 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 802 instances, 753 slices, 21 macros(203 instances: 133 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 59866, Over = 0
PHY-3001 : End spreading;  0.004935s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 59866, Over = 0
PHY-3001 : End incremental legalization;  0.033232s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (423.2%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.149468s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (177.7%)

OPT-1001 : Current memory(MB): used = 221, reserve = 189, peak = 221.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058499s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (106.8%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1705/1936.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68264, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.009588s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.34, top5 = 22.59, top10 = 17.47, top15 = 13.43.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048589s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (128.6%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.000000
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.841262s wall, 0.921875s user + 0.031250s system = 0.953125s CPU (113.3%)

RUN-1003 : finish command "place" in  5.444927s wall, 7.750000s user + 2.906250s system = 10.656250s CPU (195.7%)

RUN-1004 : used memory is 196 MB, reserved memory is 163 MB, peak memory is 221 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 804 instances
RUN-1001 : 377 mslices, 376 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1936 nets
RUN-1001 : 1367 nets have 2 pins
RUN-1001 : 463 nets have [3 - 5] pins
RUN-1001 : 64 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6323, tnet num: 1934, tinst num: 802, tnode num: 8619, tedge num: 11197.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 377 mslices, 376 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 65464, over cnt = 148(0%), over = 211, worst = 4
PHY-1002 : len = 66376, over cnt = 80(0%), over = 92, worst = 3
PHY-1002 : len = 67568, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 67648, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.130491s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (143.7%)

PHY-1001 : Congestion index: top1 = 30.56, top5 = 22.47, top10 = 17.33, top15 = 13.34.
PHY-1001 : End global routing;  0.180918s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (129.5%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 236, reserve = 205, peak = 236.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 496, reserve = 468, peak = 496.
PHY-1001 : End build detailed router design. 3.206547s wall, 3.140625s user + 0.062500s system = 3.203125s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 32592, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.080633s wall, 1.078125s user + 0.000000s system = 1.078125s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 528, reserve = 501, peak = 528.
PHY-1001 : End phase 1; 1.087351s wall, 1.078125s user + 0.000000s system = 1.078125s CPU (99.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 28% nets.
PHY-1001 : Routed 35% nets.
PHY-1001 : Routed 45% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 176120, over cnt = 36(0%), over = 36, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 531, reserve = 502, peak = 531.
PHY-1001 : End initial routed; 1.347235s wall, 1.921875s user + 0.109375s system = 2.031250s CPU (150.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1715(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.283   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.961   |  -45.442  |  21   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.372989s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (100.5%)

PHY-1001 : Current memory(MB): used = 533, reserve = 504, peak = 533.
PHY-1001 : End phase 2; 1.720314s wall, 2.296875s user + 0.109375s system = 2.406250s CPU (139.9%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 176120, over cnt = 36(0%), over = 36, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015467s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (101.0%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 176016, over cnt = 5(0%), over = 5, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.027900s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (112.0%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 176040, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.022602s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (69.1%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 176000, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.020510s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (76.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1715(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.283   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.961   |  -45.418  |  21   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.354871s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (101.3%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 7 feed throughs used by 6 nets
PHY-1001 : End commit to database; 0.173043s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (99.3%)

PHY-1001 : Current memory(MB): used = 546, reserve = 517, peak = 546.
PHY-1001 : End phase 3; 0.745678s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (100.6%)

PHY-1003 : Routed, final wirelength = 176000
PHY-1001 : Current memory(MB): used = 546, reserve = 518, peak = 546.
PHY-1001 : End export database. 0.009476s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (164.9%)

PHY-1001 : End detail routing;  6.969943s wall, 7.468750s user + 0.171875s system = 7.640625s CPU (109.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6323, tnet num: 1934, tinst num: 802, tnode num: 8619, tedge num: 11197.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[27] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[32] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dib[5] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[33] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dib[6] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[34] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dib[7] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[36] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[0] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[40] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[4] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[7] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[5] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[53] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[8] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[54] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_66.dia[2] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[55] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_66.dia[5] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_63.mi[0] slack -2697ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_63.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_66.mi[0] slack -2590ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_66.mi[1] slack -2949ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_69.mi[0] slack -2829ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_69.mi[1] slack -2949ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_72.mi[0] slack -2829ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_72.mi[1] slack -2721ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_75.mi[0] slack -2829ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_75.mi[1] slack -2961ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_78.mi[0] slack -2803ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_78.mi[1] slack -2817ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_81.mi[0] slack -2803ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_81.mi[1] slack -2829ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_83.mi[0] slack -2817ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/tx_data_dy_b[3]_syn_24.mi[0] slack -2829ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6445, tnet num: 1995, tinst num: 863, tnode num: 8741, tedge num: 11319.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_63_mi[0] slack -561ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_63_mi[1] slack -654ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_66_mi[0] slack -293ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_66_mi[1] slack -156ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/tx_data_dy_b[3]_syn_24_mi[0] slack -399ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_69_mi[1] slack -418ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_69_mi[0] slack -743ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_72_mi[1] slack -171ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_75_mi[1] slack -465ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_78_mi[0] slack -763ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_75_mi[0] slack -397ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_78_mi[1] slack -728ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_72_mi[0] slack -605ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_83_mi[0] slack -638ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_81_mi[0] slack -752ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_81_mi[1] slack -371ps
RUN-1001 : End hold fix;  3.268369s wall, 3.343750s user + 0.265625s system = 3.609375s CPU (110.4%)

RUN-1003 : finish command "route" in  10.768514s wall, 11.375000s user + 0.453125s system = 11.828125s CPU (109.8%)

RUN-1004 : used memory is 521 MB, reserved memory is 493 MB, peak memory is 546 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      900   out of  19600    4.59%
#reg                     1053   out of  19600    5.37%
#le                      1577
  #lut only               524   out of   1577   33.23%
  #reg only               677   out of   1577   42.93%
  #lut&reg                376   out of   1577   23.84%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         481
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         100
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    41
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1577   |697     |203     |1086    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1075   |293     |140     |863     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |21     |16      |5       |18      |0       |0       |
|    demodu                  |Demodulation                                     |456    |102     |45      |350     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |55     |28      |6       |45      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |5       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |13     |6       |0       |13      |0       |0       |
|    integ                   |Integration                                      |144    |26      |15      |116     |0       |0       |
|    modu                    |Modulation                                       |105    |58      |21      |101     |0       |1       |
|    rs422                   |Rs422Output                                      |325    |75      |46      |258     |0       |4       |
|    trans                   |SquareWaveGenerator                              |24     |16      |8       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |173    |131     |7       |116     |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |25     |18      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |111    |85      |0       |84      |0       |0       |
|  wendu                     |DS18B20                                          |307    |262     |45      |73      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1389  
    #2          2       331   
    #3          3       105   
    #4          4        27   
    #5        5-10       69   
    #6        11-50      31   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6445, tnet num: 1995, tinst num: 863, tnode num: 8741, tedge num: 11319.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1995 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 863
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1997, pip num: 14509
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 12
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1276 valid insts, and 39178 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.303990s wall, 17.343750s user + 0.062500s system = 17.406250s CPU (526.8%)

RUN-1004 : used memory is 520 MB, reserved memory is 495 MB, peak memory is 670 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250805_111432.log"
