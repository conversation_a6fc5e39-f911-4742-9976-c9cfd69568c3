============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Mon Aug  4 15:32:50 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1579 instances
RUN-0007 : 375 luts, 954 seqs, 129 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2123 nets
RUN-1001 : 1543 nets have 2 pins
RUN-1001 : 476 nets have [3 - 5] pins
RUN-1001 : 62 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     251     
RUN-1001 :   No   |  No   |  Yes  |     124     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1577 instances, 375 luts, 954 seqs, 199 slices, 20 macros(199 instances: 129 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7558, tnet num: 2121, tinst num: 1577, tnode num: 10722, tedge num: 12837.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2121 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.252274s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (99.1%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 520169
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1577.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 444982, overlap = 20.25
PHY-3002 : Step(2): len = 419124, overlap = 18
PHY-3002 : Step(3): len = 405254, overlap = 20.25
PHY-3002 : Step(4): len = 396032, overlap = 13.5
PHY-3002 : Step(5): len = 382526, overlap = 11.25
PHY-3002 : Step(6): len = 372132, overlap = 13.5
PHY-3002 : Step(7): len = 363375, overlap = 13.5
PHY-3002 : Step(8): len = 355459, overlap = 15.75
PHY-3002 : Step(9): len = 345641, overlap = 15.75
PHY-3002 : Step(10): len = 340221, overlap = 15.75
PHY-3002 : Step(11): len = 330608, overlap = 13.5
PHY-3002 : Step(12): len = 325062, overlap = 13.5
PHY-3002 : Step(13): len = 317457, overlap = 13.5
PHY-3002 : Step(14): len = 310600, overlap = 13.5
PHY-3002 : Step(15): len = 302603, overlap = 15.75
PHY-3002 : Step(16): len = 298162, overlap = 15.75
PHY-3002 : Step(17): len = 289646, overlap = 15.75
PHY-3002 : Step(18): len = 283129, overlap = 15.75
PHY-3002 : Step(19): len = 276857, overlap = 15.75
PHY-3002 : Step(20): len = 272892, overlap = 15.75
PHY-3002 : Step(21): len = 262443, overlap = 13.5
PHY-3002 : Step(22): len = 256954, overlap = 13.5
PHY-3002 : Step(23): len = 252699, overlap = 13.5
PHY-3002 : Step(24): len = 246909, overlap = 20.25
PHY-3002 : Step(25): len = 236843, overlap = 20.25
PHY-3002 : Step(26): len = 233089, overlap = 20.25
PHY-3002 : Step(27): len = 227841, overlap = 20.25
PHY-3002 : Step(28): len = 223639, overlap = 20.25
PHY-3002 : Step(29): len = 217391, overlap = 20.25
PHY-3002 : Step(30): len = 213778, overlap = 20.25
PHY-3002 : Step(31): len = 205587, overlap = 20.25
PHY-3002 : Step(32): len = 201258, overlap = 20.25
PHY-3002 : Step(33): len = 197531, overlap = 20.25
PHY-3002 : Step(34): len = 192107, overlap = 20.25
PHY-3002 : Step(35): len = 183065, overlap = 20.25
PHY-3002 : Step(36): len = 179434, overlap = 20.25
PHY-3002 : Step(37): len = 176306, overlap = 20.25
PHY-3002 : Step(38): len = 172039, overlap = 20.25
PHY-3002 : Step(39): len = 155530, overlap = 18
PHY-3002 : Step(40): len = 151353, overlap = 20.25
PHY-3002 : Step(41): len = 149568, overlap = 20.25
PHY-3002 : Step(42): len = 135362, overlap = 20.25
PHY-3002 : Step(43): len = 108257, overlap = 18
PHY-3002 : Step(44): len = 107207, overlap = 20.25
PHY-3002 : Step(45): len = 104372, overlap = 18
PHY-3002 : Step(46): len = 95183.1, overlap = 18
PHY-3002 : Step(47): len = 93481.2, overlap = 20.25
PHY-3002 : Step(48): len = 91375.4, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.13201e-05
PHY-3002 : Step(49): len = 92071.5, overlap = 13.5
PHY-3002 : Step(50): len = 91580.2, overlap = 15.75
PHY-3002 : Step(51): len = 90630.1, overlap = 15.75
PHY-3002 : Step(52): len = 90371.5, overlap = 18
PHY-3002 : Step(53): len = 89195.6, overlap = 15.75
PHY-3002 : Step(54): len = 87159.5, overlap = 15.75
PHY-3002 : Step(55): len = 86737.1, overlap = 13.5
PHY-3002 : Step(56): len = 85454.3, overlap = 13.5
PHY-3002 : Step(57): len = 83540.5, overlap = 15.75
PHY-3002 : Step(58): len = 81658.5, overlap = 15.75
PHY-3002 : Step(59): len = 81070, overlap = 15.75
PHY-3002 : Step(60): len = 80012.8, overlap = 15.75
PHY-3002 : Step(61): len = 78042.9, overlap = 13.5
PHY-3002 : Step(62): len = 77817.3, overlap = 13.5
PHY-3002 : Step(63): len = 76278.4, overlap = 15.75
PHY-3002 : Step(64): len = 75615.4, overlap = 15.75
PHY-3002 : Step(65): len = 75370.3, overlap = 18
PHY-3002 : Step(66): len = 73399, overlap = 18
PHY-3002 : Step(67): len = 70663, overlap = 16.0625
PHY-3002 : Step(68): len = 70254.8, overlap = 16.125
PHY-3002 : Step(69): len = 69166.9, overlap = 16.125
PHY-3002 : Step(70): len = 68101.1, overlap = 13.9375
PHY-3002 : Step(71): len = 67852.1, overlap = 13.9375
PHY-3002 : Step(72): len = 66991.3, overlap = 11.875
PHY-3002 : Step(73): len = 66122.2, overlap = 12
PHY-3002 : Step(74): len = 65020.9, overlap = 17.8125
PHY-3002 : Step(75): len = 63874, overlap = 17.875
PHY-3002 : Step(76): len = 63474.4, overlap = 17.5625
PHY-3002 : Step(77): len = 61483.3, overlap = 15.625
PHY-3002 : Step(78): len = 60895.1, overlap = 15.5
PHY-3002 : Step(79): len = 60601.7, overlap = 15.1875
PHY-3002 : Step(80): len = 60240.2, overlap = 14.8125
PHY-3002 : Step(81): len = 59807.8, overlap = 14.8125
PHY-3002 : Step(82): len = 59250.1, overlap = 14.6875
PHY-3002 : Step(83): len = 58792.6, overlap = 12.375
PHY-3002 : Step(84): len = 57367.3, overlap = 16.6875
PHY-3002 : Step(85): len = 57287.2, overlap = 14.4375
PHY-3002 : Step(86): len = 56830.1, overlap = 17.3125
PHY-3002 : Step(87): len = 56592.5, overlap = 15
PHY-3002 : Step(88): len = 56724.9, overlap = 12.8125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00016264
PHY-3002 : Step(89): len = 56766, overlap = 12.75
PHY-3002 : Step(90): len = 56717.1, overlap = 14.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00032528
PHY-3002 : Step(91): len = 56699.9, overlap = 14.5
PHY-3002 : Step(92): len = 56650.1, overlap = 16.625
PHY-3001 : Before Legalized: Len = 56650.1
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008007s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (195.2%)

PHY-3001 : After Legalized: Len = 59546.7, Over = 3.125
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2121 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.061747s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(93): len = 59669.6, overlap = 15.8125
PHY-3002 : Step(94): len = 60158.1, overlap = 15.125
PHY-3002 : Step(95): len = 59056.6, overlap = 15.0938
PHY-3002 : Step(96): len = 58416.5, overlap = 15.0938
PHY-3002 : Step(97): len = 57779.6, overlap = 15.2188
PHY-3002 : Step(98): len = 56724.6, overlap = 15.2812
PHY-3002 : Step(99): len = 56494.8, overlap = 14.0938
PHY-3002 : Step(100): len = 55840.5, overlap = 14.9375
PHY-3002 : Step(101): len = 55250.7, overlap = 15.0625
PHY-3002 : Step(102): len = 54458.1, overlap = 17.3438
PHY-3002 : Step(103): len = 53851.2, overlap = 18.125
PHY-3002 : Step(104): len = 53262.1, overlap = 18.125
PHY-3002 : Step(105): len = 53083.7, overlap = 17.9688
PHY-3002 : Step(106): len = 52674.4, overlap = 16.8438
PHY-3002 : Step(107): len = 52630.7, overlap = 16.375
PHY-3002 : Step(108): len = 52097.9, overlap = 16.9688
PHY-3002 : Step(109): len = 51907.1, overlap = 18.5938
PHY-3002 : Step(110): len = 51642.6, overlap = 18.7188
PHY-3002 : Step(111): len = 51305.2, overlap = 19.125
PHY-3002 : Step(112): len = 51247.1, overlap = 21.625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000359
PHY-3002 : Step(113): len = 51082.5, overlap = 21.5625
PHY-3002 : Step(114): len = 51068.9, overlap = 18.5938
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000718
PHY-3002 : Step(115): len = 50973.3, overlap = 18.8438
PHY-3002 : Step(116): len = 50991.6, overlap = 18.8438
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2121 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.068280s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (114.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.95758e-05
PHY-3002 : Step(117): len = 51278.6, overlap = 54.4062
PHY-3002 : Step(118): len = 52302.5, overlap = 52.7812
PHY-3002 : Step(119): len = 53077.6, overlap = 45.5312
PHY-3002 : Step(120): len = 52710.3, overlap = 41.3438
PHY-3002 : Step(121): len = 52274.4, overlap = 42.2812
PHY-3002 : Step(122): len = 52217.4, overlap = 42.375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000179152
PHY-3002 : Step(123): len = 52281.3, overlap = 41.9688
PHY-3002 : Step(124): len = 52472.5, overlap = 41.9375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000321711
PHY-3002 : Step(125): len = 52701.2, overlap = 41.3438
PHY-3002 : Step(126): len = 53305.5, overlap = 40.25
PHY-3002 : Step(127): len = 55143.1, overlap = 29
PHY-3002 : Step(128): len = 55014.6, overlap = 29.1562
PHY-3002 : Step(129): len = 55062.1, overlap = 29.0938
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7558, tnet num: 2121, tinst num: 1577, tnode num: 10722, tedge num: 12837.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 86.19 peak overflow 2.62
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2123.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58120, over cnt = 233(0%), over = 1033, worst = 21
PHY-1001 : End global iterations;  0.063211s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (148.3%)

PHY-1001 : Congestion index: top1 = 44.14, top5 = 25.37, top10 = 16.59, top15 = 11.86.
PHY-1001 : End incremental global routing;  0.117122s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (120.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2121 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.066484s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (117.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.212794s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (110.1%)

OPT-1001 : Current memory(MB): used = 209, reserve = 176, peak = 209.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1668/2123.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58120, over cnt = 233(0%), over = 1033, worst = 21
PHY-1002 : len = 65960, over cnt = 147(0%), over = 314, worst = 11
PHY-1002 : len = 69792, over cnt = 33(0%), over = 38, worst = 4
PHY-1002 : len = 70304, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 70712, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.096317s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (129.8%)

PHY-1001 : Congestion index: top1 = 36.55, top5 = 24.91, top10 = 18.24, top15 = 13.57.
OPT-1001 : End congestion update;  0.139830s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (122.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2121 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.055869s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (111.9%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.199406s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (117.5%)

OPT-1001 : Current memory(MB): used = 212, reserve = 179, peak = 212.
OPT-1001 : End physical optimization;  0.666088s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (119.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 375 LUT to BLE ...
SYN-4008 : Packed 375 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 765 remaining SEQ's ...
SYN-4005 : Packed 76 SEQ with LUT/SLICE
SYN-4006 : 126 single LUT's are left
SYN-4006 : 689 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1064/1378 primitive instances ...
PHY-3001 : End packing;  0.049572s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (126.1%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 810 instances
RUN-1001 : 379 mslices, 380 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1942 nets
RUN-1001 : 1361 nets have 2 pins
RUN-1001 : 476 nets have [3 - 5] pins
RUN-1001 : 60 nets have [6 - 10] pins
RUN-1001 : 24 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 808 instances, 759 slices, 20 macros(199 instances: 129 mslices 70 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 54915.8, Over = 50.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6357, tnet num: 1940, tinst num: 808, tnode num: 8654, tedge num: 11254.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1940 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.301049s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (98.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.01857e-05
PHY-3002 : Step(130): len = 54437.4, overlap = 51.75
PHY-3002 : Step(131): len = 53902.4, overlap = 55.25
PHY-3002 : Step(132): len = 53392.2, overlap = 56
PHY-3002 : Step(133): len = 53145, overlap = 56.75
PHY-3002 : Step(134): len = 53232.6, overlap = 57.5
PHY-3002 : Step(135): len = 53213.8, overlap = 56.75
PHY-3002 : Step(136): len = 52911.6, overlap = 57
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.03714e-05
PHY-3002 : Step(137): len = 52983.4, overlap = 57
PHY-3002 : Step(138): len = 53771.8, overlap = 55.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000160743
PHY-3002 : Step(139): len = 54255.9, overlap = 54.25
PHY-3002 : Step(140): len = 54592.8, overlap = 51.75
PHY-3002 : Step(141): len = 55009.2, overlap = 50.75
PHY-3001 : Before Legalized: Len = 55009.2
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.086066s wall, 0.078125s user + 0.125000s system = 0.203125s CPU (236.0%)

PHY-3001 : After Legalized: Len = 67174.9, Over = 0
PHY-3001 : Trial Legalized: Len = 67174.9
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1940 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.048769s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (128.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000588584
PHY-3002 : Step(142): len = 62211.3, overlap = 11.25
PHY-3002 : Step(143): len = 60938.2, overlap = 15
PHY-3002 : Step(144): len = 59498.8, overlap = 16.25
PHY-3002 : Step(145): len = 58667.1, overlap = 20.75
PHY-3002 : Step(146): len = 58322.3, overlap = 20.75
PHY-3002 : Step(147): len = 58112, overlap = 22.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00117717
PHY-3002 : Step(148): len = 58446.5, overlap = 22
PHY-3002 : Step(149): len = 58697, overlap = 20.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00235433
PHY-3002 : Step(150): len = 58886.4, overlap = 20.5
PHY-3002 : Step(151): len = 59024.9, overlap = 20.75
PHY-3001 : Before Legalized: Len = 59024.9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005085s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 63342.9, Over = 0
PHY-3001 : Legalized: Len = 63342.9, Over = 0
PHY-3001 : Spreading special nets. 6 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005400s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 7 instances has been re-located, deltaX = 2, deltaY = 6, maxDist = 2.
PHY-3001 : Final: Len = 63568.9, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6357, tnet num: 1940, tinst num: 808, tnode num: 8654, tedge num: 11254.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 107/1942.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70672, over cnt = 157(0%), over = 257, worst = 6
PHY-1002 : len = 71704, over cnt = 84(0%), over = 121, worst = 6
PHY-1002 : len = 73000, over cnt = 13(0%), over = 16, worst = 3
PHY-1002 : len = 73272, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.124910s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (175.1%)

PHY-1001 : Congestion index: top1 = 31.44, top5 = 23.28, top10 = 18.08, top15 = 14.15.
PHY-1001 : End incremental global routing;  0.178096s wall, 0.234375s user + 0.031250s system = 0.265625s CPU (149.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1940 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058125s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (107.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.264530s wall, 0.328125s user + 0.031250s system = 0.359375s CPU (135.9%)

OPT-1001 : Current memory(MB): used = 216, reserve = 183, peak = 216.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1725/1942.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73272, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006421s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (243.3%)

PHY-1001 : Congestion index: top1 = 31.44, top5 = 23.28, top10 = 18.08, top15 = 14.15.
OPT-1001 : End congestion update;  0.052806s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1940 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058242s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (107.3%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 768 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 808 instances, 759 slices, 20 macros(199 instances: 129 mslices 70 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 63525.8, Over = 0
PHY-3001 : Spreading special nets. 1 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.004986s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 1 instances has been re-located, deltaX = 1, deltaY = 0, maxDist = 1.
PHY-3001 : Final: Len = 63557.8, Over = 0
PHY-3001 : End incremental legalization;  0.034328s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (182.1%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 3 cells processed and 150 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.158827s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (118.1%)

OPT-1001 : Current memory(MB): used = 220, reserve = 188, peak = 220.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1940 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049445s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.8%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1717/1942.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73280, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008057s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.53, top5 = 23.36, top10 = 18.10, top15 = 14.16.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1940 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050697s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (123.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.137931
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.849018s wall, 0.921875s user + 0.046875s system = 0.968750s CPU (114.1%)

RUN-1003 : finish command "place" in  4.982622s wall, 7.562500s user + 2.593750s system = 10.156250s CPU (203.8%)

RUN-1004 : used memory is 198 MB, reserved memory is 165 MB, peak memory is 220 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 810 instances
RUN-1001 : 379 mslices, 380 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1942 nets
RUN-1001 : 1361 nets have 2 pins
RUN-1001 : 476 nets have [3 - 5] pins
RUN-1001 : 60 nets have [6 - 10] pins
RUN-1001 : 24 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6357, tnet num: 1940, tinst num: 808, tnode num: 8654, tedge num: 11254.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 379 mslices, 380 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1940 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69392, over cnt = 157(0%), over = 255, worst = 6
PHY-1002 : len = 70640, over cnt = 85(0%), over = 114, worst = 4
PHY-1002 : len = 71904, over cnt = 15(0%), over = 18, worst = 2
PHY-1002 : len = 72288, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.116038s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (134.7%)

PHY-1001 : Congestion index: top1 = 31.06, top5 = 23.15, top10 = 17.93, top15 = 14.01.
PHY-1001 : End global routing;  0.166178s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (122.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 230, reserve = 199, peak = 233.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 492, reserve = 464, peak = 492.
PHY-1001 : End build detailed router design. 3.095238s wall, 3.015625s user + 0.062500s system = 3.078125s CPU (99.4%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 30600, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.069462s wall, 1.031250s user + 0.031250s system = 1.062500s CPU (99.3%)

PHY-1001 : Current memory(MB): used = 524, reserve = 498, peak = 524.
PHY-1001 : End phase 1; 1.076303s wall, 1.046875s user + 0.031250s system = 1.078125s CPU (100.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 178456, over cnt = 32(0%), over = 32, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 526, reserve = 499, peak = 527.
PHY-1001 : End initial routed; 1.473965s wall, 2.265625s user + 0.093750s system = 2.359375s CPU (160.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1724(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.786   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -46.354  |  18   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.338813s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (101.5%)

PHY-1001 : Current memory(MB): used = 528, reserve = 500, peak = 528.
PHY-1001 : End phase 2; 1.812868s wall, 2.609375s user + 0.093750s system = 2.703125s CPU (149.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 178456, over cnt = 32(0%), over = 32, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015752s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (99.2%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 178144, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.031474s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (99.3%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 178168, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.033612s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (139.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1724(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.786   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -46.354  |  18   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.346904s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (103.6%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 9 feed throughs used by 8 nets
PHY-1001 : End commit to database; 0.169751s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (92.0%)

PHY-1001 : Current memory(MB): used = 543, reserve = 515, peak = 543.
PHY-1001 : End phase 3; 0.726000s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (101.2%)

PHY-1003 : Routed, final wirelength = 178168
PHY-1001 : Current memory(MB): used = 543, reserve = 515, peak = 543.
PHY-1001 : End export database. 0.010321s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  6.894623s wall, 7.609375s user + 0.187500s system = 7.796875s CPU (113.1%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6357, tnet num: 1940, tinst num: 808, tnode num: 8654, tedge num: 11254.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[6] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[13] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[4] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[16] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_52.mi[0] slack -2897ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_52.mi[1] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_55.mi[0] slack -2897ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_55.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_58.mi[0] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_58.mi[1] slack -2679ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_61.mi[0] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_61.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_64.mi[0] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_64.mi[1] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_67.mi[0] slack -2897ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_67.mi[1] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_70.mi[0] slack -2897ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_70.mi[1] slack -2897ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_73.mi[0] slack -2684ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_73.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6467, tnet num: 1995, tinst num: 863, tnode num: 8764, tedge num: 11364.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_73_mi[1] slack -264ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_73_mi[0] slack -771ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_70_mi[1] slack -783ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_70_mi[0] slack -992ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_52_mi[1] slack -277ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_52_mi[0] slack -567ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_55_mi[1] slack -221ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_55_mi[0] slack -110ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_58_mi[1] slack -863ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_58_mi[0] slack -763ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_64_mi[1] slack -378ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_61_mi[1] slack -335ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_64_mi[0] slack -617ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_67_mi[1] slack -951ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg2_syn_67_mi[0] slack -351ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_61_mi[0] slack -768ps
RUN-1001 : End hold fix;  3.043873s wall, 3.046875s user + 0.281250s system = 3.328125s CPU (109.3%)

RUN-1003 : finish command "route" in  10.425876s wall, 11.187500s user + 0.484375s system = 11.671875s CPU (112.0%)

RUN-1004 : used memory is 535 MB, reserved memory is 509 MB, peak memory is 543 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      892   out of  19600    4.55%
#reg                     1053   out of  19600    5.37%
#le                      1581
  #lut only               528   out of   1581   33.40%
  #reg only               689   out of   1581   43.58%
  #lut&reg                364   out of   1581   23.02%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                                               Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0                                477
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3                                102
#3        wendu/clk_us                    GCLK               lslice             signal_process/ctrl_signal/modulate_reg_syn_33.q0    42
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4                                1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di                                      1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1581   |693     |199     |1086    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1063   |286     |137     |866     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |37     |31      |6       |25      |0       |0       |
|    demodu                  |Demodulation                                     |433    |93      |41      |348     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |53     |34      |6       |45      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |14     |7       |0       |14      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |13     |8       |0       |13      |0       |0       |
|    integ                   |Integration                                      |141    |29      |15      |113     |0       |0       |
|    modu                    |Modulation                                       |103    |41      |21      |99      |0       |1       |
|    rs422                   |Rs422Output                                      |313    |64      |46      |258     |0       |4       |
|    trans                   |SquareWaveGenerator                              |36     |28      |8       |23      |0       |0       |
|  u_uart                    |UART_Control                                     |189    |133     |7       |118     |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |26     |19      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |126    |86      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |309    |264     |45      |69      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1377  
    #2          2       337   
    #3          3       111   
    #4          4        28   
    #5        5-10       67   
    #6        11-50      32   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6467, tnet num: 1995, tinst num: 863, tnode num: 8764, tedge num: 11364.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1995 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 863
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1997, pip num: 14723
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 13
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1348 valid insts, and 39445 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.874236s wall, 22.062500s user + 0.031250s system = 22.093750s CPU (570.3%)

RUN-1004 : used memory is 550 MB, reserved memory is 522 MB, peak memory is 680 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250804_153250.log"
