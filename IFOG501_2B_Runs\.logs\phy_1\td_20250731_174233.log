============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Thu Jul 31 17:42:33 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1579 instances
RUN-0007 : 367 luts, 965 seqs, 126 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2112 nets
RUN-1001 : 1537 nets have 2 pins
RUN-1001 : 474 nets have [3 - 5] pins
RUN-1001 : 59 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     263     
RUN-1001 :   No   |  No   |  Yes  |     123     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1577 instances, 367 luts, 965 seqs, 196 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7526, tnet num: 2110, tinst num: 1577, tnode num: 10693, tedge num: 12759.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2110 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.260736s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (101.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 509753
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1577.
PHY-3001 : End clustering;  0.000021s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 434806, overlap = 20.25
PHY-3002 : Step(2): len = 419275, overlap = 18
PHY-3002 : Step(3): len = 405696, overlap = 20.25
PHY-3002 : Step(4): len = 396369, overlap = 15.75
PHY-3002 : Step(5): len = 381255, overlap = 18
PHY-3002 : Step(6): len = 370759, overlap = 13.5
PHY-3002 : Step(7): len = 360677, overlap = 13.5
PHY-3002 : Step(8): len = 354293, overlap = 13.5
PHY-3002 : Step(9): len = 341228, overlap = 13.5
PHY-3002 : Step(10): len = 332956, overlap = 11.25
PHY-3002 : Step(11): len = 326249, overlap = 11.25
PHY-3002 : Step(12): len = 318268, overlap = 11.25
PHY-3002 : Step(13): len = 308932, overlap = 11.25
PHY-3002 : Step(14): len = 305033, overlap = 13.5
PHY-3002 : Step(15): len = 293910, overlap = 13.5
PHY-3002 : Step(16): len = 286198, overlap = 13.5
PHY-3002 : Step(17): len = 281484, overlap = 13.5
PHY-3002 : Step(18): len = 276620, overlap = 13.5
PHY-3002 : Step(19): len = 265240, overlap = 13.5
PHY-3002 : Step(20): len = 260637, overlap = 13.5
PHY-3002 : Step(21): len = 256492, overlap = 13.5
PHY-3002 : Step(22): len = 251857, overlap = 13.5
PHY-3002 : Step(23): len = 241067, overlap = 18
PHY-3002 : Step(24): len = 237029, overlap = 20.25
PHY-3002 : Step(25): len = 233285, overlap = 20.25
PHY-3002 : Step(26): len = 227860, overlap = 20.25
PHY-3002 : Step(27): len = 218329, overlap = 20.25
PHY-3002 : Step(28): len = 215517, overlap = 20.25
PHY-3002 : Step(29): len = 210952, overlap = 20.25
PHY-3002 : Step(30): len = 204772, overlap = 20.25
PHY-3002 : Step(31): len = 197562, overlap = 20.25
PHY-3002 : Step(32): len = 195347, overlap = 20.25
PHY-3002 : Step(33): len = 188056, overlap = 20.25
PHY-3002 : Step(34): len = 177238, overlap = 20.25
PHY-3002 : Step(35): len = 173469, overlap = 20.25
PHY-3002 : Step(36): len = 171570, overlap = 20.25
PHY-3002 : Step(37): len = 119798, overlap = 15.75
PHY-3002 : Step(38): len = 117417, overlap = 20.25
PHY-3002 : Step(39): len = 116054, overlap = 20.25
PHY-3002 : Step(40): len = 112851, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.79527e-05
PHY-3002 : Step(41): len = 113921, overlap = 13.5
PHY-3002 : Step(42): len = 111928, overlap = 20.25
PHY-3002 : Step(43): len = 110791, overlap = 18
PHY-3002 : Step(44): len = 109498, overlap = 13.5
PHY-3002 : Step(45): len = 105484, overlap = 13.5
PHY-3002 : Step(46): len = 103582, overlap = 11.25
PHY-3002 : Step(47): len = 101168, overlap = 11.25
PHY-3002 : Step(48): len = 100897, overlap = 11.25
PHY-3002 : Step(49): len = 98763.7, overlap = 11.25
PHY-3002 : Step(50): len = 95783.6, overlap = 15.75
PHY-3002 : Step(51): len = 93621.8, overlap = 11.25
PHY-3002 : Step(52): len = 92756.5, overlap = 15.75
PHY-3002 : Step(53): len = 91218.8, overlap = 15.75
PHY-3002 : Step(54): len = 90216.1, overlap = 15.75
PHY-3002 : Step(55): len = 87631.5, overlap = 15.75
PHY-3002 : Step(56): len = 87015, overlap = 13.5
PHY-3002 : Step(57): len = 85189, overlap = 13.5
PHY-3002 : Step(58): len = 83121.1, overlap = 15.75
PHY-3002 : Step(59): len = 81363.4, overlap = 15.75
PHY-3002 : Step(60): len = 80937.9, overlap = 16.1875
PHY-3002 : Step(61): len = 79177.4, overlap = 14.375
PHY-3002 : Step(62): len = 77468.1, overlap = 16.6875
PHY-3002 : Step(63): len = 75876.1, overlap = 15.6875
PHY-3002 : Step(64): len = 75375.7, overlap = 15.8125
PHY-3002 : Step(65): len = 73681, overlap = 15.9375
PHY-3002 : Step(66): len = 72563.9, overlap = 15.875
PHY-3002 : Step(67): len = 72004.7, overlap = 15.8125
PHY-3002 : Step(68): len = 71070.1, overlap = 15.6875
PHY-3002 : Step(69): len = 70150.2, overlap = 15.125
PHY-3002 : Step(70): len = 68800.7, overlap = 15.0625
PHY-3002 : Step(71): len = 68071.1, overlap = 15.0625
PHY-3002 : Step(72): len = 66645.7, overlap = 14.8125
PHY-3002 : Step(73): len = 66152.6, overlap = 14.875
PHY-3002 : Step(74): len = 65431.2, overlap = 14.6875
PHY-3002 : Step(75): len = 63515.5, overlap = 13
PHY-3002 : Step(76): len = 63543.2, overlap = 12.75
PHY-3002 : Step(77): len = 62957.2, overlap = 12.4375
PHY-3002 : Step(78): len = 62459.4, overlap = 12.1875
PHY-3002 : Step(79): len = 62528.8, overlap = 14.4375
PHY-3002 : Step(80): len = 62279, overlap = 14.3125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000175905
PHY-3002 : Step(81): len = 62344.5, overlap = 14.3125
PHY-3002 : Step(82): len = 62335.5, overlap = 14.3125
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000351811
PHY-3002 : Step(83): len = 62448.5, overlap = 12.125
PHY-3002 : Step(84): len = 62604.6, overlap = 14.25
PHY-3001 : Before Legalized: Len = 62604.6
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.010230s wall, 0.015625s user + 0.015625s system = 0.031250s CPU (305.5%)

PHY-3001 : After Legalized: Len = 66724.3, Over = 0.75
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2110 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.092104s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (101.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(85): len = 66902.3, overlap = 8.125
PHY-3002 : Step(86): len = 65683.3, overlap = 7.53125
PHY-3002 : Step(87): len = 64873.7, overlap = 7.71875
PHY-3002 : Step(88): len = 64282.5, overlap = 7.90625
PHY-3002 : Step(89): len = 63328.6, overlap = 8.09375
PHY-3002 : Step(90): len = 62230.2, overlap = 7.78125
PHY-3002 : Step(91): len = 60907.1, overlap = 9.03125
PHY-3002 : Step(92): len = 59854.4, overlap = 10.0312
PHY-3002 : Step(93): len = 59047.5, overlap = 10.9688
PHY-3002 : Step(94): len = 58571, overlap = 10.9062
PHY-3002 : Step(95): len = 57495.4, overlap = 11.4688
PHY-3002 : Step(96): len = 56871.8, overlap = 11.8438
PHY-3002 : Step(97): len = 56542.6, overlap = 10.6562
PHY-3002 : Step(98): len = 56095.5, overlap = 10.7188
PHY-3002 : Step(99): len = 55246.2, overlap = 11.75
PHY-3002 : Step(100): len = 54841.8, overlap = 13.5
PHY-3002 : Step(101): len = 54622.4, overlap = 14.4688
PHY-3002 : Step(102): len = 54222.9, overlap = 14.0625
PHY-3002 : Step(103): len = 53179.7, overlap = 16.75
PHY-3002 : Step(104): len = 52485.4, overlap = 19.1875
PHY-3002 : Step(105): len = 51827.6, overlap = 15.2812
PHY-3002 : Step(106): len = 51405.5, overlap = 14.75
PHY-3002 : Step(107): len = 51027.1, overlap = 14.8125
PHY-3002 : Step(108): len = 50428.2, overlap = 14.875
PHY-3002 : Step(109): len = 50238.5, overlap = 15.0625
PHY-3002 : Step(110): len = 50301.4, overlap = 15.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00028041
PHY-3002 : Step(111): len = 50190.5, overlap = 15.25
PHY-3002 : Step(112): len = 50205.1, overlap = 15.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00056082
PHY-3002 : Step(113): len = 50043.5, overlap = 15.3125
PHY-3002 : Step(114): len = 50129.3, overlap = 14.9688
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2110 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.108191s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (101.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.93354e-05
PHY-3002 : Step(115): len = 50052.5, overlap = 50.375
PHY-3002 : Step(116): len = 50840.6, overlap = 50.4062
PHY-3002 : Step(117): len = 51192.9, overlap = 47.9062
PHY-3002 : Step(118): len = 51286.2, overlap = 47.5
PHY-3002 : Step(119): len = 51425.4, overlap = 42.8438
PHY-3002 : Step(120): len = 51357.8, overlap = 42.6562
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000158671
PHY-3002 : Step(121): len = 51527.6, overlap = 39.8125
PHY-3002 : Step(122): len = 52438.3, overlap = 37.3438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000317342
PHY-3002 : Step(123): len = 52305.4, overlap = 37.1562
PHY-3002 : Step(124): len = 52388.3, overlap = 37.5312
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000513459
PHY-3002 : Step(125): len = 53181.1, overlap = 34.9375
PHY-3002 : Step(126): len = 53485.9, overlap = 34.5312
PHY-3002 : Step(127): len = 54755.6, overlap = 31.0625
PHY-3002 : Step(128): len = 54992.9, overlap = 30.375
PHY-3002 : Step(129): len = 54880.5, overlap = 29.9688
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7526, tnet num: 2110, tinst num: 1577, tnode num: 10693, tedge num: 12759.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 83.44 peak overflow 2.12
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2112.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58896, over cnt = 240(0%), over = 1016, worst = 17
PHY-1001 : End global iterations;  0.097975s wall, 0.093750s user + 0.046875s system = 0.140625s CPU (143.5%)

PHY-1001 : Congestion index: top1 = 43.81, top5 = 25.77, top10 = 16.57, top15 = 11.84.
PHY-1001 : End incremental global routing;  0.163529s wall, 0.156250s user + 0.046875s system = 0.203125s CPU (124.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2110 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.084846s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (92.1%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.286142s wall, 0.265625s user + 0.062500s system = 0.328125s CPU (114.7%)

OPT-1001 : Current memory(MB): used = 208, reserve = 175, peak = 208.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1650/2112.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58896, over cnt = 240(0%), over = 1016, worst = 17
PHY-1002 : len = 64320, over cnt = 171(0%), over = 514, worst = 14
PHY-1002 : len = 70776, over cnt = 30(0%), over = 31, worst = 2
PHY-1002 : len = 71264, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 71632, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.129831s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (108.3%)

PHY-1001 : Congestion index: top1 = 36.81, top5 = 25.26, top10 = 18.32, top15 = 13.59.
OPT-1001 : End congestion update;  0.183657s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (110.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2110 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.070439s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (88.7%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.257985s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (103.0%)

OPT-1001 : Current memory(MB): used = 211, reserve = 178, peak = 211.
OPT-1001 : End physical optimization;  0.894269s wall, 0.890625s user + 0.093750s system = 0.984375s CPU (110.1%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 367 LUT to BLE ...
SYN-4008 : Packed 367 LUT and 190 SEQ to BLE.
SYN-4003 : Packing 775 remaining SEQ's ...
SYN-4005 : Packed 94 SEQ with LUT/SLICE
SYN-4006 : 99 single LUT's are left
SYN-4006 : 681 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1048/1359 primitive instances ...
PHY-3001 : End packing;  0.063231s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.8%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 794 instances
RUN-1001 : 372 mslices, 371 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1930 nets
RUN-1001 : 1358 nets have 2 pins
RUN-1001 : 470 nets have [3 - 5] pins
RUN-1001 : 61 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 792 instances, 743 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 54966, Over = 53.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6293, tnet num: 1928, tinst num: 792, tnode num: 8583, tedge num: 11122.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.370806s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (96.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.76006e-05
PHY-3002 : Step(130): len = 54488.8, overlap = 56.5
PHY-3002 : Step(131): len = 53778, overlap = 58
PHY-3002 : Step(132): len = 53285, overlap = 59.25
PHY-3002 : Step(133): len = 53558.3, overlap = 58
PHY-3002 : Step(134): len = 53599.6, overlap = 59.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.52011e-05
PHY-3002 : Step(135): len = 53987.5, overlap = 58.75
PHY-3002 : Step(136): len = 54198.8, overlap = 58.5
PHY-3002 : Step(137): len = 54610.3, overlap = 57.25
PHY-3002 : Step(138): len = 54773, overlap = 56
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000190402
PHY-3002 : Step(139): len = 55319.2, overlap = 52.75
PHY-3002 : Step(140): len = 55913.2, overlap = 48.25
PHY-3001 : Before Legalized: Len = 55913.2
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.159085s wall, 0.125000s user + 0.171875s system = 0.296875s CPU (186.6%)

PHY-3001 : After Legalized: Len = 69145.7, Over = 0
PHY-3001 : Trial Legalized: Len = 69145.7
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.069278s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (90.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00137002
PHY-3002 : Step(141): len = 66352.9, overlap = 5.5
PHY-3002 : Step(142): len = 64697, overlap = 9.25
PHY-3002 : Step(143): len = 63124.1, overlap = 13.75
PHY-3002 : Step(144): len = 61815.6, overlap = 15.5
PHY-3002 : Step(145): len = 61101.8, overlap = 20.75
PHY-3002 : Step(146): len = 60533.9, overlap = 22.25
PHY-3002 : Step(147): len = 60200.1, overlap = 24.5
PHY-3002 : Step(148): len = 59956.2, overlap = 25.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00274004
PHY-3002 : Step(149): len = 60138.2, overlap = 25
PHY-3002 : Step(150): len = 60154.5, overlap = 24.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00548007
PHY-3002 : Step(151): len = 60274.5, overlap = 25.25
PHY-3002 : Step(152): len = 60345.5, overlap = 24.75
PHY-3001 : Before Legalized: Len = 60345.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008448s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (185.0%)

PHY-3001 : After Legalized: Len = 63911.2, Over = 0
PHY-3001 : Legalized: Len = 63911.2, Over = 0
PHY-3001 : Spreading special nets. 11 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.008040s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 17 instances has been re-located, deltaX = 2, deltaY = 16, maxDist = 2.
PHY-3001 : Final: Len = 64099.2, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6293, tnet num: 1928, tinst num: 792, tnode num: 8583, tedge num: 11122.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 30/1930.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70320, over cnt = 160(0%), over = 236, worst = 6
PHY-1002 : len = 71280, over cnt = 91(0%), over = 116, worst = 4
PHY-1002 : len = 72728, over cnt = 6(0%), over = 7, worst = 2
PHY-1002 : len = 72792, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 72856, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.191567s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (122.3%)

PHY-1001 : Congestion index: top1 = 32.16, top5 = 23.18, top10 = 17.79, top15 = 13.93.
PHY-1001 : End incremental global routing;  0.255049s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (116.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.073330s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (106.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.365109s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (111.3%)

OPT-1001 : Current memory(MB): used = 214, reserve = 182, peak = 214.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1709/1930.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72856, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008276s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (188.8%)

PHY-1001 : Congestion index: top1 = 32.16, top5 = 23.18, top10 = 17.79, top15 = 13.93.
OPT-1001 : End congestion update;  0.063543s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059390s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.2%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.125529s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (99.6%)

OPT-1001 : Current memory(MB): used = 216, reserve = 184, peak = 216.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057802s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1709/1930.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72856, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007305s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.16, top5 = 23.18, top10 = 17.79, top15 = 13.93.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056820s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (110.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.793103
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  1.046365s wall, 1.187500s user + 0.000000s system = 1.187500s CPU (113.5%)

RUN-1003 : finish command "place" in  6.680798s wall, 9.406250s user + 4.031250s system = 13.437500s CPU (201.1%)

RUN-1004 : used memory is 197 MB, reserved memory is 163 MB, peak memory is 217 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 794 instances
RUN-1001 : 372 mslices, 371 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1930 nets
RUN-1001 : 1358 nets have 2 pins
RUN-1001 : 470 nets have [3 - 5] pins
RUN-1001 : 61 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6293, tnet num: 1928, tinst num: 792, tnode num: 8583, tedge num: 11122.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 372 mslices, 371 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69784, over cnt = 157(0%), over = 237, worst = 6
PHY-1002 : len = 70752, over cnt = 85(0%), over = 111, worst = 4
PHY-1002 : len = 72184, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 72312, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.153777s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (121.9%)

PHY-1001 : Congestion index: top1 = 31.85, top5 = 23.11, top10 = 17.76, top15 = 13.88.
PHY-1001 : End global routing;  0.215777s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (108.6%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 233, reserve = 202, peak = 233.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 495, reserve = 466, peak = 495.
PHY-1001 : End build detailed router design. 3.852235s wall, 3.828125s user + 0.031250s system = 3.859375s CPU (100.2%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 30800, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.161789s wall, 1.156250s user + 0.000000s system = 1.156250s CPU (99.5%)

PHY-1001 : Current memory(MB): used = 527, reserve = 499, peak = 527.
PHY-1001 : End phase 1; 1.168204s wall, 1.156250s user + 0.000000s system = 1.156250s CPU (99.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 30% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 176632, over cnt = 31(0%), over = 31, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 528, reserve = 500, peak = 528.
PHY-1001 : End initial routed; 1.901199s wall, 2.890625s user + 0.218750s system = 3.109375s CPU (163.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1715(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -46.924  |  19   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.402886s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (100.8%)

PHY-1001 : Current memory(MB): used = 529, reserve = 501, peak = 529.
PHY-1001 : End phase 2; 2.304194s wall, 3.296875s user + 0.218750s system = 3.515625s CPU (152.6%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 176632, over cnt = 31(0%), over = 31, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.018109s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (86.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 176528, over cnt = 8(0%), over = 8, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.030175s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (155.3%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 176512, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.031016s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (100.8%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 176536, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.025617s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (122.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1715(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -46.924  |  19   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.406290s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (100.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 8 feed throughs used by 8 nets
PHY-1001 : End commit to database; 0.197657s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (94.9%)

PHY-1001 : Current memory(MB): used = 544, reserve = 516, peak = 544.
PHY-1001 : End phase 3; 0.864677s wall, 0.859375s user + 0.015625s system = 0.875000s CPU (101.2%)

PHY-1003 : Routed, final wirelength = 176536
PHY-1001 : Current memory(MB): used = 544, reserve = 516, peak = 544.
PHY-1001 : End export database. 0.010932s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (142.9%)

PHY-1001 : End detail routing;  8.404834s wall, 9.359375s user + 0.265625s system = 9.625000s CPU (114.5%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6293, tnet num: 1928, tinst num: 792, tnode num: 8583, tedge num: 11122.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[41] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[5] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[54] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_66.dia[2] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[55] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_66.dia[5] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[1] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[13] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[4] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[16] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_60.mi[0] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_60.mi[1] slack -2897ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_63.mi[0] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_63.mi[1] slack -2543ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_66.mi[0] slack -2911ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_66.mi[1] slack -2909ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_69.mi[0] slack -2897ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_69.mi[1] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_72.mi[0] slack -2897ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_72.mi[1] slack -2911ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_75.mi[0] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_75.mi[1] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_78.mi[0] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_78.mi[1] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_81.mi[0] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_81.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6401, tnet num: 1982, tinst num: 846, tnode num: 8691, tedge num: 11230.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_75_mi[1] slack -464ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_75_mi[0] slack -694ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg2_syn_60_mi[1] slack -1017ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_60_mi[0] slack -410ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_63_mi[0] slack -438ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_63_mi[1] slack -500ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_69_mi[1] slack -631ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_66_mi[0] slack -677ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_66_mi[1] slack -465ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_72_mi[1] slack -488ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_72_mi[0] slack -671ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_78_mi[1] slack -585ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_81_mi[0] slack -679ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_81_mi[1] slack -719ps
RUN-1001 : End hold fix;  3.735169s wall, 3.953125s user + 0.406250s system = 4.359375s CPU (116.7%)

RUN-1003 : finish command "route" in  12.773956s wall, 13.968750s user + 0.687500s system = 14.656250s CPU (114.7%)

RUN-1004 : used memory is 521 MB, reserved memory is 492 MB, peak memory is 544 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      871   out of  19600    4.44%
#reg                     1053   out of  19600    5.37%
#le                      1552
  #lut only               499   out of   1552   32.15%
  #reg only               681   out of   1552   43.88%
  #lut&reg                372   out of   1552   23.97%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         474
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         101
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q0    42
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1552   |675     |196     |1086    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1050   |275     |133     |863     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |21     |16      |5       |18      |0       |0       |
|    demodu                  |Demodulation                                     |441    |88      |45      |351     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |55     |27      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |5       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |14     |6       |0       |14      |0       |0       |
|    integ                   |Integration                                      |138    |34      |15      |110     |0       |0       |
|    modu                    |Modulation                                       |104    |47      |14      |102     |0       |1       |
|    rs422                   |Rs422Output                                      |315    |67      |46      |260     |0       |4       |
|    trans                   |SquareWaveGenerator                              |31     |23      |8       |22      |0       |0       |
|  u_uart                    |UART_Control                                     |175    |129     |7       |118     |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |26      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |29     |21      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |111    |82      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |305    |260     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1373  
    #2          2       336   
    #3          3       105   
    #4          4        29   
    #5        5-10       65   
    #6        11-50      31   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6401, tnet num: 1982, tinst num: 846, tnode num: 8691, tedge num: 11230.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1982 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 846
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1984, pip num: 14491
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 13
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1286 valid insts, and 38812 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  5.020672s wall, 28.328125s user + 0.109375s system = 28.437500s CPU (566.4%)

RUN-1004 : used memory is 517 MB, reserved memory is 489 MB, peak memory is 667 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250731_174233.log"
