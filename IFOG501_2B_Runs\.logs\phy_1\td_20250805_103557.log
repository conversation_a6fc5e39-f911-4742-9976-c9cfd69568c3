============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Tue Aug  5 10:35:57 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1574 instances
RUN-0007 : 370 luts, 954 seqs, 129 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2117 nets
RUN-1001 : 1544 nets have 2 pins
RUN-1001 : 469 nets have [3 - 5] pins
RUN-1001 : 60 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     251     
RUN-1001 :   No   |  No   |  Yes  |     124     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1572 instances, 370 luts, 954 seqs, 199 slices, 20 macros(199 instances: 129 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7544, tnet num: 2115, tinst num: 1572, tnode num: 10707, tedge num: 12818.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2115 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.257943s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (96.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 522690
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1572.
PHY-3001 : End clustering;  0.000020s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 454751, overlap = 20.25
PHY-3002 : Step(2): len = 429849, overlap = 13.5
PHY-3002 : Step(3): len = 417630, overlap = 20.25
PHY-3002 : Step(4): len = 405858, overlap = 13.5
PHY-3002 : Step(5): len = 393672, overlap = 18
PHY-3002 : Step(6): len = 378677, overlap = 13.5
PHY-3002 : Step(7): len = 369167, overlap = 13.5
PHY-3002 : Step(8): len = 361378, overlap = 13.5
PHY-3002 : Step(9): len = 350421, overlap = 15.75
PHY-3002 : Step(10): len = 341437, overlap = 15.75
PHY-3002 : Step(11): len = 335748, overlap = 15.75
PHY-3002 : Step(12): len = 326436, overlap = 13.5
PHY-3002 : Step(13): len = 317785, overlap = 13.5
PHY-3002 : Step(14): len = 312610, overlap = 13.5
PHY-3002 : Step(15): len = 304693, overlap = 13.5
PHY-3002 : Step(16): len = 298050, overlap = 15.75
PHY-3002 : Step(17): len = 292294, overlap = 15.75
PHY-3002 : Step(18): len = 285884, overlap = 15.75
PHY-3002 : Step(19): len = 279698, overlap = 15.75
PHY-3002 : Step(20): len = 273501, overlap = 15.75
PHY-3002 : Step(21): len = 266874, overlap = 13.5
PHY-3002 : Step(22): len = 261697, overlap = 13.5
PHY-3002 : Step(23): len = 257207, overlap = 13.5
PHY-3002 : Step(24): len = 245827, overlap = 13.5
PHY-3002 : Step(25): len = 238886, overlap = 13.5
PHY-3002 : Step(26): len = 236097, overlap = 13.5
PHY-3002 : Step(27): len = 226417, overlap = 20.25
PHY-3002 : Step(28): len = 213987, overlap = 20.25
PHY-3002 : Step(29): len = 210835, overlap = 20.25
PHY-3002 : Step(30): len = 206101, overlap = 20.25
PHY-3002 : Step(31): len = 159255, overlap = 20.25
PHY-3002 : Step(32): len = 153483, overlap = 20.25
PHY-3002 : Step(33): len = 151992, overlap = 20.25
PHY-3002 : Step(34): len = 139493, overlap = 20.25
PHY-3002 : Step(35): len = 133318, overlap = 20.25
PHY-3002 : Step(36): len = 130978, overlap = 20.25
PHY-3002 : Step(37): len = 129254, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000113979
PHY-3002 : Step(38): len = 130054, overlap = 18
PHY-3002 : Step(39): len = 128970, overlap = 18
PHY-3002 : Step(40): len = 128427, overlap = 15.75
PHY-3002 : Step(41): len = 126625, overlap = 13.5
PHY-3002 : Step(42): len = 122870, overlap = 11.25
PHY-3002 : Step(43): len = 121597, overlap = 11.25
PHY-3002 : Step(44): len = 116620, overlap = 11.25
PHY-3002 : Step(45): len = 113304, overlap = 11.25
PHY-3002 : Step(46): len = 112552, overlap = 11.25
PHY-3002 : Step(47): len = 111251, overlap = 9
PHY-3002 : Step(48): len = 108082, overlap = 13.5
PHY-3002 : Step(49): len = 105896, overlap = 13.5
PHY-3002 : Step(50): len = 104176, overlap = 13.5
PHY-3002 : Step(51): len = 102647, overlap = 13.5
PHY-3002 : Step(52): len = 99800.2, overlap = 11.25
PHY-3002 : Step(53): len = 97647.7, overlap = 13.5
PHY-3002 : Step(54): len = 96829.6, overlap = 13.5
PHY-3002 : Step(55): len = 95403.7, overlap = 13.5
PHY-3002 : Step(56): len = 94374.8, overlap = 15.75
PHY-3002 : Step(57): len = 92571.4, overlap = 13.5
PHY-3002 : Step(58): len = 91625.4, overlap = 13.5
PHY-3002 : Step(59): len = 88002.3, overlap = 11.25
PHY-3002 : Step(60): len = 86372.6, overlap = 15.75
PHY-3002 : Step(61): len = 84662.7, overlap = 15.75
PHY-3002 : Step(62): len = 84089.7, overlap = 15.75
PHY-3002 : Step(63): len = 80787.8, overlap = 13.5
PHY-3002 : Step(64): len = 80531.9, overlap = 13.5
PHY-3002 : Step(65): len = 76649.9, overlap = 13.5
PHY-3002 : Step(66): len = 76031.1, overlap = 13.5
PHY-3002 : Step(67): len = 74715.1, overlap = 13.9375
PHY-3002 : Step(68): len = 74147.9, overlap = 19.25
PHY-3002 : Step(69): len = 74086.2, overlap = 17
PHY-3002 : Step(70): len = 73403.3, overlap = 19.3125
PHY-3002 : Step(71): len = 72552.8, overlap = 19.375
PHY-3002 : Step(72): len = 71759.2, overlap = 17.3125
PHY-3002 : Step(73): len = 71459.2, overlap = 19.5625
PHY-3002 : Step(74): len = 70981.3, overlap = 17.5
PHY-3002 : Step(75): len = 69896.2, overlap = 17.5
PHY-3002 : Step(76): len = 68943.3, overlap = 15.25
PHY-3002 : Step(77): len = 67941.8, overlap = 15.5
PHY-3002 : Step(78): len = 67012.4, overlap = 18.0625
PHY-3002 : Step(79): len = 65873.3, overlap = 15.8125
PHY-3002 : Step(80): len = 65345.6, overlap = 15.875
PHY-3002 : Step(81): len = 65339.7, overlap = 16.0625
PHY-3002 : Step(82): len = 65300.1, overlap = 16.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000227958
PHY-3002 : Step(83): len = 65559.8, overlap = 16.125
PHY-3002 : Step(84): len = 65640.9, overlap = 16.125
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000455915
PHY-3002 : Step(85): len = 65723.9, overlap = 16.1875
PHY-3002 : Step(86): len = 65919, overlap = 16.25
PHY-3001 : Before Legalized: Len = 65919
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007296s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 70136.6, Over = 2.75
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2115 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.063482s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(87): len = 70035.1, overlap = 6.875
PHY-3002 : Step(88): len = 68362.9, overlap = 6.625
PHY-3002 : Step(89): len = 67117.3, overlap = 6.1875
PHY-3002 : Step(90): len = 65890.4, overlap = 6.125
PHY-3002 : Step(91): len = 65102.7, overlap = 6.25
PHY-3002 : Step(92): len = 62762.1, overlap = 7
PHY-3002 : Step(93): len = 62017.1, overlap = 6.6875
PHY-3002 : Step(94): len = 60710.3, overlap = 6.875
PHY-3002 : Step(95): len = 60275.2, overlap = 7.4375
PHY-3002 : Step(96): len = 59435.6, overlap = 7.375
PHY-3002 : Step(97): len = 59271.5, overlap = 7.4375
PHY-3002 : Step(98): len = 58614.8, overlap = 7.75
PHY-3002 : Step(99): len = 58272.6, overlap = 7.875
PHY-3002 : Step(100): len = 57551.6, overlap = 7.75
PHY-3002 : Step(101): len = 57126.7, overlap = 8.3125
PHY-3002 : Step(102): len = 56440.6, overlap = 8.9375
PHY-3002 : Step(103): len = 56073.7, overlap = 9.5
PHY-3002 : Step(104): len = 55718.2, overlap = 9.625
PHY-3002 : Step(105): len = 55452.4, overlap = 9.8125
PHY-3002 : Step(106): len = 55486.3, overlap = 10.125
PHY-3002 : Step(107): len = 55344.6, overlap = 10.5
PHY-3002 : Step(108): len = 54238.1, overlap = 11.4062
PHY-3002 : Step(109): len = 53241.7, overlap = 15.3438
PHY-3002 : Step(110): len = 52816.1, overlap = 15.7188
PHY-3002 : Step(111): len = 52332.4, overlap = 16.4062
PHY-3002 : Step(112): len = 52202.9, overlap = 16.4688
PHY-3002 : Step(113): len = 52101.8, overlap = 16.7188
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000454098
PHY-3002 : Step(114): len = 51803.1, overlap = 16.5312
PHY-3002 : Step(115): len = 51908.4, overlap = 16.3438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000908197
PHY-3002 : Step(116): len = 51794.8, overlap = 16.0312
PHY-3002 : Step(117): len = 51835.1, overlap = 13.2812
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2115 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.066167s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (118.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000109094
PHY-3002 : Step(118): len = 52243, overlap = 55.2812
PHY-3002 : Step(119): len = 53190.4, overlap = 48.5938
PHY-3002 : Step(120): len = 53371.4, overlap = 47.3438
PHY-3002 : Step(121): len = 52814.4, overlap = 47.6562
PHY-3002 : Step(122): len = 52671.2, overlap = 48.2188
PHY-3002 : Step(123): len = 52600.9, overlap = 47.1875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000218188
PHY-3002 : Step(124): len = 52796.6, overlap = 46.9375
PHY-3002 : Step(125): len = 52981.7, overlap = 46.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000371762
PHY-3002 : Step(126): len = 53400.6, overlap = 40.25
PHY-3002 : Step(127): len = 54029.8, overlap = 38.4062
PHY-3002 : Step(128): len = 55168, overlap = 33.5312
PHY-3002 : Step(129): len = 55084.7, overlap = 33
PHY-3002 : Step(130): len = 55178.8, overlap = 32.75
PHY-3002 : Step(131): len = 55274.9, overlap = 32.25
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7544, tnet num: 2115, tinst num: 1572, tnode num: 10707, tedge num: 12818.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 92.22 peak overflow 3.09
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2117.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58144, over cnt = 235(0%), over = 1006, worst = 26
PHY-1001 : End global iterations;  0.058807s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (159.4%)

PHY-1001 : Congestion index: top1 = 47.05, top5 = 25.89, top10 = 16.37, top15 = 11.70.
PHY-1001 : End incremental global routing;  0.109982s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (127.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2115 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067327s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (92.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.209639s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (111.8%)

OPT-1001 : Current memory(MB): used = 210, reserve = 177, peak = 210.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1620/2117.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58144, over cnt = 235(0%), over = 1006, worst = 26
PHY-1002 : len = 64848, over cnt = 203(0%), over = 554, worst = 26
PHY-1002 : len = 70096, over cnt = 93(0%), over = 168, worst = 7
PHY-1002 : len = 71576, over cnt = 13(0%), over = 14, worst = 2
PHY-1002 : len = 72296, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.094009s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (133.0%)

PHY-1001 : Congestion index: top1 = 39.78, top5 = 25.94, top10 = 18.64, top15 = 13.75.
OPT-1001 : End congestion update;  0.138644s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (124.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2115 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059141s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.7%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.201504s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (116.3%)

OPT-1001 : Current memory(MB): used = 214, reserve = 181, peak = 214.
OPT-1001 : End physical optimization;  0.669976s wall, 0.796875s user + 0.015625s system = 0.812500s CPU (121.3%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 370 LUT to BLE ...
SYN-4008 : Packed 370 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 765 remaining SEQ's ...
SYN-4005 : Packed 82 SEQ with LUT/SLICE
SYN-4006 : 115 single LUT's are left
SYN-4006 : 683 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1053/1367 primitive instances ...
PHY-3001 : End packing;  0.063163s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.0%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 803 instances
RUN-1001 : 376 mslices, 376 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1936 nets
RUN-1001 : 1368 nets have 2 pins
RUN-1001 : 465 nets have [3 - 5] pins
RUN-1001 : 59 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 801 instances, 752 slices, 20 macros(199 instances: 129 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 55351.6, Over = 56.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6322, tnet num: 1934, tinst num: 801, tnode num: 8617, tedge num: 11194.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.279148s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (95.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.30371e-05
PHY-3002 : Step(132): len = 54368.5, overlap = 55.25
PHY-3002 : Step(133): len = 53875.4, overlap = 58
PHY-3002 : Step(134): len = 53662.7, overlap = 57.5
PHY-3002 : Step(135): len = 53628.7, overlap = 56.75
PHY-3002 : Step(136): len = 53668.6, overlap = 56.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.60742e-05
PHY-3002 : Step(137): len = 53915.1, overlap = 55
PHY-3002 : Step(138): len = 54284.2, overlap = 56.5
PHY-3002 : Step(139): len = 54837.9, overlap = 55.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000132148
PHY-3002 : Step(140): len = 55051.3, overlap = 55.25
PHY-3002 : Step(141): len = 55732.8, overlap = 53.75
PHY-3002 : Step(142): len = 56326.5, overlap = 51.75
PHY-3002 : Step(143): len = 56578.2, overlap = 49.75
PHY-3001 : Before Legalized: Len = 56578.2
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.097428s wall, 0.046875s user + 0.109375s system = 0.156250s CPU (160.4%)

PHY-3001 : After Legalized: Len = 70230.2, Over = 0
PHY-3001 : Trial Legalized: Len = 70230.2
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.050667s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00200812
PHY-3002 : Step(144): len = 67290.4, overlap = 3.25
PHY-3002 : Step(145): len = 65536.4, overlap = 8.5
PHY-3002 : Step(146): len = 63801.2, overlap = 13.25
PHY-3002 : Step(147): len = 62945.5, overlap = 14.25
PHY-3002 : Step(148): len = 62006.8, overlap = 15.25
PHY-3002 : Step(149): len = 61308.7, overlap = 16
PHY-3002 : Step(150): len = 60666.2, overlap = 17
PHY-3002 : Step(151): len = 60283.2, overlap = 18.5
PHY-3002 : Step(152): len = 59786.3, overlap = 20.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0034258
PHY-3002 : Step(153): len = 59957.6, overlap = 20.25
PHY-3002 : Step(154): len = 59974.6, overlap = 21
PHY-3002 : Step(155): len = 59974.6, overlap = 21
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00685159
PHY-3002 : Step(156): len = 60017.6, overlap = 20.5
PHY-3002 : Step(157): len = 60001.8, overlap = 20.25
PHY-3001 : Before Legalized: Len = 60001.8
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005115s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (305.5%)

PHY-3001 : After Legalized: Len = 63746.7, Over = 0
PHY-3001 : Legalized: Len = 63746.7, Over = 0
PHY-3001 : Spreading special nets. 3 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005328s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 5 instances has been re-located, deltaX = 2, deltaY = 4, maxDist = 2.
PHY-3001 : Final: Len = 63826.7, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6322, tnet num: 1934, tinst num: 801, tnode num: 8617, tedge num: 11194.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 67/1936.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70760, over cnt = 133(0%), over = 208, worst = 6
PHY-1002 : len = 71768, over cnt = 80(0%), over = 99, worst = 4
PHY-1002 : len = 73072, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 73088, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 73120, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.121703s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (141.2%)

PHY-1001 : Congestion index: top1 = 32.61, top5 = 23.39, top10 = 17.95, top15 = 13.99.
PHY-1001 : End incremental global routing;  0.173273s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (126.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061676s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.263115s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (118.8%)

OPT-1001 : Current memory(MB): used = 215, reserve = 183, peak = 217.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1705/1936.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73120, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007062s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.61, top5 = 23.39, top10 = 17.95, top15 = 13.99.
OPT-1001 : End congestion update;  0.054488s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (114.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051350s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.3%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 761 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 801 instances, 752 slices, 20 macros(199 instances: 129 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 63834.6, Over = 0
PHY-3001 : End spreading;  0.004712s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 63834.6, Over = 0
PHY-3001 : End incremental legalization;  0.035715s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (87.5%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.154550s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (171.9%)

OPT-1001 : Current memory(MB): used = 220, reserve = 188, peak = 221.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048238s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.2%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1701/1936.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73120, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008200s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.65, top5 = 23.35, top10 = 17.93, top15 = 13.98.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048740s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (96.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 32.206897
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.834009s wall, 0.984375s user + 0.015625s system = 1.000000s CPU (119.9%)

RUN-1003 : finish command "place" in  5.221703s wall, 7.593750s user + 2.781250s system = 10.375000s CPU (198.7%)

RUN-1004 : used memory is 197 MB, reserved memory is 164 MB, peak memory is 221 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 803 instances
RUN-1001 : 376 mslices, 376 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1936 nets
RUN-1001 : 1368 nets have 2 pins
RUN-1001 : 465 nets have [3 - 5] pins
RUN-1001 : 59 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6322, tnet num: 1934, tinst num: 801, tnode num: 8617, tedge num: 11194.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 376 mslices, 376 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69848, over cnt = 143(0%), over = 222, worst = 6
PHY-1002 : len = 70784, over cnt = 95(0%), over = 123, worst = 4
PHY-1002 : len = 72344, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 72408, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.121241s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (128.9%)

PHY-1001 : Congestion index: top1 = 32.63, top5 = 22.94, top10 = 17.79, top15 = 13.86.
PHY-1001 : End global routing;  0.170666s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (119.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 236, reserve = 204, peak = 236.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 495, reserve = 468, peak = 495.
PHY-1001 : End build detailed router design. 3.153295s wall, 3.062500s user + 0.078125s system = 3.140625s CPU (99.6%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 31128, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.075266s wall, 1.046875s user + 0.015625s system = 1.062500s CPU (98.8%)

PHY-1001 : Current memory(MB): used = 527, reserve = 502, peak = 527.
PHY-1001 : End phase 1; 1.081540s wall, 1.062500s user + 0.015625s system = 1.078125s CPU (99.7%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 38% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 181136, over cnt = 29(0%), over = 29, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 530, reserve = 502, peak = 531.
PHY-1001 : End initial routed; 1.667299s wall, 2.437500s user + 0.109375s system = 2.546875s CPU (152.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1718(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.931   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.867   |  -43.984  |  19   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.348701s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (98.6%)

PHY-1001 : Current memory(MB): used = 531, reserve = 503, peak = 531.
PHY-1001 : End phase 2; 2.016093s wall, 2.781250s user + 0.109375s system = 2.890625s CPU (143.4%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 181136, over cnt = 29(0%), over = 29, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014350s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (108.9%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 181048, over cnt = 11(0%), over = 11, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.027329s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (114.3%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 181120, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.023134s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (67.5%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 181152, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.018812s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (166.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1718(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.931   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.867   |  -43.984  |  19   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.338392s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (97.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 9 feed throughs used by 7 nets
PHY-1001 : End commit to database; 0.236367s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (105.8%)

PHY-1001 : Current memory(MB): used = 545, reserve = 517, peak = 545.
PHY-1001 : End phase 3; 0.791038s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (102.7%)

PHY-1003 : Routed, final wirelength = 181152
PHY-1001 : Current memory(MB): used = 545, reserve = 517, peak = 545.
PHY-1001 : End export database. 0.009766s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  7.227720s wall, 7.906250s user + 0.203125s system = 8.109375s CPU (112.2%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6322, tnet num: 1934, tinst num: 801, tnode num: 8617, tedge num: 11194.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[4] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[6] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[0] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[0] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[2] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[0] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_52.mi[0] slack -2735ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_52.mi[1] slack -2735ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_55.mi[0] slack -2735ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_55.mi[1] slack -2627ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_58.mi[0] slack -2867ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_58.mi[1] slack -2867ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_61.mi[0] slack -2855ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_61.mi[1] slack -2709ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_64.mi[0] slack -2627ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_64.mi[1] slack -2491ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_67.mi[0] slack -2735ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_67.mi[1] slack -2723ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_70.mi[0] slack -2855ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_70.mi[1] slack -2855ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_73.mi[0] slack -2855ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_73.mi[1] slack -2627ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6432, tnet num: 1989, tinst num: 856, tnode num: 8727, tedge num: 11304.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_55_mi[1] slack -678ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_52_mi[0] slack -426ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_55_mi[0] slack -804ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_61_mi[0] slack -311ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_61_mi[1] slack -680ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_58_mi[1] slack -240ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_58_mi[0] slack -572ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_67_mi[1] slack -242ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_64_mi[1] slack -253ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg2_syn_64_mi[0] slack -444ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_67_mi[0] slack -641ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_73_mi[1] slack -383ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_73_mi[0] slack -109ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_70_mi[1] slack -62ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_70_mi[0] slack -332ps
RUN-1001 : End hold fix;  3.069378s wall, 3.031250s user + 0.187500s system = 3.218750s CPU (104.9%)

RUN-1003 : finish command "route" in  10.809606s wall, 11.453125s user + 0.406250s system = 11.859375s CPU (109.7%)

RUN-1004 : used memory is 529 MB, reserved memory is 501 MB, peak memory is 545 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      882   out of  19600    4.50%
#reg                     1052   out of  19600    5.37%
#le                      1565
  #lut only               513   out of   1565   32.78%
  #reg only               683   out of   1565   43.64%
  #lut&reg                369   out of   1565   23.58%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         478
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         100
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q0    42
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1565   |683     |199     |1085    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1054   |281     |135     |861     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |26     |22      |4       |20      |0       |0       |
|    demodu                  |Demodulation                                     |439    |92      |41      |354     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |54     |32      |6       |45      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |5       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |14     |10      |0       |14      |0       |0       |
|    integ                   |Integration                                      |140    |29      |15      |112     |0       |0       |
|    modu                    |Modulation                                       |100    |44      |21      |96      |0       |1       |
|    rs422                   |Rs422Output                                      |315    |68      |46      |260     |0       |4       |
|    trans                   |SquareWaveGenerator                              |34     |26      |8       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |182    |130     |7       |118     |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |26     |19      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |119    |83      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |305    |260     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1384  
    #2          2       328   
    #3          3       106   
    #4          4        31   
    #5        5-10       66   
    #6        11-50      31   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6432, tnet num: 1989, tinst num: 856, tnode num: 8727, tedge num: 11304.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1989 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 856
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1991, pip num: 14734
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 11
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1327 valid insts, and 39303 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.261690s wall, 18.343750s user + 0.140625s system = 18.484375s CPU (566.7%)

RUN-1004 : used memory is 545 MB, reserved memory is 516 MB, peak memory is 673 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250805_103557.log"
