============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Tue Aug  5 10:27:38 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1590 instances
RUN-0007 : 375 luts, 967 seqs, 127 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2123 nets
RUN-1001 : 1545 nets have 2 pins
RUN-1001 : 470 nets have [3 - 5] pins
RUN-1001 : 66 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     263     
RUN-1001 :   No   |  No   |  Yes  |     125     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1588 instances, 375 luts, 967 seqs, 197 slices, 20 macros(197 instances: 127 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7573, tnet num: 2121, tinst num: 1588, tnode num: 10742, tedge num: 12830.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2121 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.422220s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (99.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 554283
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1588.
PHY-3001 : End clustering;  0.000031s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 480569, overlap = 15.75
PHY-3002 : Step(2): len = 444928, overlap = 20.25
PHY-3002 : Step(3): len = 430771, overlap = 18
PHY-3002 : Step(4): len = 414781, overlap = 15.75
PHY-3002 : Step(5): len = 406495, overlap = 11.25
PHY-3002 : Step(6): len = 372163, overlap = 15.75
PHY-3002 : Step(7): len = 359113, overlap = 4.5
PHY-3002 : Step(8): len = 351706, overlap = 13.5
PHY-3002 : Step(9): len = 342361, overlap = 13.5
PHY-3002 : Step(10): len = 333113, overlap = 11.25
PHY-3002 : Step(11): len = 327936, overlap = 11.25
PHY-3002 : Step(12): len = 314861, overlap = 13.5
PHY-3002 : Step(13): len = 307023, overlap = 13.5
PHY-3002 : Step(14): len = 300872, overlap = 15.75
PHY-3002 : Step(15): len = 293675, overlap = 15.75
PHY-3002 : Step(16): len = 286649, overlap = 15.75
PHY-3002 : Step(17): len = 281468, overlap = 15.75
PHY-3002 : Step(18): len = 276442, overlap = 13.5
PHY-3002 : Step(19): len = 269148, overlap = 13.5
PHY-3002 : Step(20): len = 263038, overlap = 13.5
PHY-3002 : Step(21): len = 258074, overlap = 13.5
PHY-3002 : Step(22): len = 252667, overlap = 13.5
PHY-3002 : Step(23): len = 246732, overlap = 13.5
PHY-3002 : Step(24): len = 242375, overlap = 18
PHY-3002 : Step(25): len = 236629, overlap = 20.25
PHY-3002 : Step(26): len = 232295, overlap = 20.25
PHY-3002 : Step(27): len = 227729, overlap = 20.25
PHY-3002 : Step(28): len = 222525, overlap = 20.25
PHY-3002 : Step(29): len = 216869, overlap = 20.25
PHY-3002 : Step(30): len = 211734, overlap = 20.25
PHY-3002 : Step(31): len = 207647, overlap = 20.25
PHY-3002 : Step(32): len = 202738, overlap = 20.25
PHY-3002 : Step(33): len = 197031, overlap = 20.25
PHY-3002 : Step(34): len = 193976, overlap = 20.25
PHY-3002 : Step(35): len = 188706, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0001231
PHY-3002 : Step(36): len = 190429, overlap = 6.75
PHY-3002 : Step(37): len = 188734, overlap = 9
PHY-3002 : Step(38): len = 186339, overlap = 13.5
PHY-3002 : Step(39): len = 183268, overlap = 4.5
PHY-3002 : Step(40): len = 177570, overlap = 6.75
PHY-3002 : Step(41): len = 174148, overlap = 2.25
PHY-3002 : Step(42): len = 169786, overlap = 4.5
PHY-3002 : Step(43): len = 165991, overlap = 4.5
PHY-3002 : Step(44): len = 162024, overlap = 9
PHY-3002 : Step(45): len = 159891, overlap = 6.75
PHY-3002 : Step(46): len = 158534, overlap = 6.75
PHY-3002 : Step(47): len = 153933, overlap = 6.75
PHY-3002 : Step(48): len = 147236, overlap = 6.75
PHY-3002 : Step(49): len = 145508, overlap = 6.75
PHY-3002 : Step(50): len = 143070, overlap = 9
PHY-3002 : Step(51): len = 139300, overlap = 9
PHY-3002 : Step(52): len = 136965, overlap = 9
PHY-3002 : Step(53): len = 134653, overlap = 6.75
PHY-3002 : Step(54): len = 132532, overlap = 6.75
PHY-3002 : Step(55): len = 130643, overlap = 6.75
PHY-3002 : Step(56): len = 128378, overlap = 6.75
PHY-3002 : Step(57): len = 125901, overlap = 6.75
PHY-3002 : Step(58): len = 121893, overlap = 6.75
PHY-3002 : Step(59): len = 119576, overlap = 4.5
PHY-3002 : Step(60): len = 118153, overlap = 6.75
PHY-3002 : Step(61): len = 115691, overlap = 9
PHY-3002 : Step(62): len = 106564, overlap = 11.25
PHY-3002 : Step(63): len = 104749, overlap = 9
PHY-3002 : Step(64): len = 103328, overlap = 9
PHY-3002 : Step(65): len = 102490, overlap = 6.75
PHY-3002 : Step(66): len = 101339, overlap = 6.75
PHY-3002 : Step(67): len = 99179.9, overlap = 9
PHY-3002 : Step(68): len = 97819.5, overlap = 9
PHY-3002 : Step(69): len = 96670.7, overlap = 4.5
PHY-3002 : Step(70): len = 94932.7, overlap = 4.5
PHY-3002 : Step(71): len = 93654.4, overlap = 9
PHY-3002 : Step(72): len = 91956.8, overlap = 9
PHY-3002 : Step(73): len = 90581.8, overlap = 9
PHY-3002 : Step(74): len = 88134, overlap = 6.75
PHY-3002 : Step(75): len = 87708.9, overlap = 6.75
PHY-3002 : Step(76): len = 85850.1, overlap = 6.75
PHY-3002 : Step(77): len = 84069.3, overlap = 11.25
PHY-3002 : Step(78): len = 82201.6, overlap = 6.75
PHY-3002 : Step(79): len = 81853.9, overlap = 9
PHY-3002 : Step(80): len = 80096.1, overlap = 6.75
PHY-3002 : Step(81): len = 78958.4, overlap = 6.75
PHY-3002 : Step(82): len = 78592.8, overlap = 6.75
PHY-3002 : Step(83): len = 77898.6, overlap = 6.75
PHY-3002 : Step(84): len = 77250.2, overlap = 6.75
PHY-3002 : Step(85): len = 76786.8, overlap = 6.75
PHY-3002 : Step(86): len = 75399, overlap = 9
PHY-3002 : Step(87): len = 74538.2, overlap = 9
PHY-3002 : Step(88): len = 73438.9, overlap = 9
PHY-3002 : Step(89): len = 72842.9, overlap = 9
PHY-3002 : Step(90): len = 71942.2, overlap = 6.75
PHY-3002 : Step(91): len = 70700.4, overlap = 11.25
PHY-3002 : Step(92): len = 69412.9, overlap = 6.75
PHY-3002 : Step(93): len = 69331.3, overlap = 9
PHY-3002 : Step(94): len = 68940.9, overlap = 11.25
PHY-3002 : Step(95): len = 68162.3, overlap = 9
PHY-3002 : Step(96): len = 68063.1, overlap = 6.75
PHY-3002 : Step(97): len = 67735.5, overlap = 9
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0002462
PHY-3002 : Step(98): len = 67916.8, overlap = 9
PHY-3002 : Step(99): len = 67961.1, overlap = 9
PHY-3002 : Step(100): len = 67953.6, overlap = 9
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000492401
PHY-3002 : Step(101): len = 68028.8, overlap = 9
PHY-3002 : Step(102): len = 68142.8, overlap = 9
PHY-3001 : Before Legalized: Len = 68142.8
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.030519s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (51.2%)

PHY-3001 : After Legalized: Len = 70197.9, Over = 0
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2121 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.098688s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (95.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(103): len = 70322.3, overlap = 3.59375
PHY-3002 : Step(104): len = 69148.3, overlap = 3.46875
PHY-3002 : Step(105): len = 68014.8, overlap = 3.625
PHY-3002 : Step(106): len = 67130.6, overlap = 4
PHY-3002 : Step(107): len = 65906.4, overlap = 4
PHY-3002 : Step(108): len = 64683.3, overlap = 4.59375
PHY-3002 : Step(109): len = 62325.4, overlap = 8.4375
PHY-3002 : Step(110): len = 59964, overlap = 8.4375
PHY-3002 : Step(111): len = 58913.2, overlap = 9.90625
PHY-3002 : Step(112): len = 57918.3, overlap = 10.3438
PHY-3002 : Step(113): len = 57199.6, overlap = 11.3125
PHY-3002 : Step(114): len = 55900.2, overlap = 13.3438
PHY-3002 : Step(115): len = 55126.3, overlap = 12.5
PHY-3002 : Step(116): len = 54397.4, overlap = 13.375
PHY-3002 : Step(117): len = 53727.3, overlap = 13.4375
PHY-3002 : Step(118): len = 52680.8, overlap = 14.8438
PHY-3002 : Step(119): len = 51655.6, overlap = 14.625
PHY-3002 : Step(120): len = 50433, overlap = 13.3438
PHY-3002 : Step(121): len = 49709.9, overlap = 13.2188
PHY-3002 : Step(122): len = 48794.5, overlap = 13.5625
PHY-3002 : Step(123): len = 48627.7, overlap = 13.875
PHY-3002 : Step(124): len = 47768.6, overlap = 14
PHY-3002 : Step(125): len = 47338.1, overlap = 13.9375
PHY-3002 : Step(126): len = 47202, overlap = 13.6875
PHY-3002 : Step(127): len = 46802.5, overlap = 12.6562
PHY-3002 : Step(128): len = 46809.5, overlap = 13.5625
PHY-3002 : Step(129): len = 46371.6, overlap = 14.75
PHY-3002 : Step(130): len = 45793.4, overlap = 15.3125
PHY-3002 : Step(131): len = 45311, overlap = 16.5
PHY-3002 : Step(132): len = 45055.4, overlap = 17.125
PHY-3002 : Step(133): len = 45007.4, overlap = 16.6875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000115029
PHY-3002 : Step(134): len = 44876.1, overlap = 16.6875
PHY-3002 : Step(135): len = 44853.7, overlap = 16.8125
PHY-3002 : Step(136): len = 44860.4, overlap = 16.5938
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000230058
PHY-3002 : Step(137): len = 44718.5, overlap = 16.5938
PHY-3002 : Step(138): len = 44701.7, overlap = 16.6875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2121 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.111105s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (98.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.11814e-05
PHY-3002 : Step(139): len = 44736.2, overlap = 56.5312
PHY-3002 : Step(140): len = 45251, overlap = 55.8125
PHY-3002 : Step(141): len = 46084.5, overlap = 51.1562
PHY-3002 : Step(142): len = 45899, overlap = 43.5312
PHY-3002 : Step(143): len = 45648.2, overlap = 43.25
PHY-3002 : Step(144): len = 45309.4, overlap = 46.2812
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000142363
PHY-3002 : Step(145): len = 45666.8, overlap = 41.1875
PHY-3002 : Step(146): len = 46668.3, overlap = 40.5312
PHY-3002 : Step(147): len = 47302.8, overlap = 37.9062
PHY-3002 : Step(148): len = 47555.8, overlap = 36.0625
PHY-3002 : Step(149): len = 47322.2, overlap = 35.7812
PHY-3002 : Step(150): len = 47380.3, overlap = 35.4375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000284726
PHY-3002 : Step(151): len = 47360.2, overlap = 34.6875
PHY-3002 : Step(152): len = 47660.1, overlap = 35.4688
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000480007
PHY-3002 : Step(153): len = 47970.2, overlap = 35.125
PHY-3002 : Step(154): len = 48998.2, overlap = 32.125
PHY-3002 : Step(155): len = 49397, overlap = 30.9375
PHY-3002 : Step(156): len = 50202.3, overlap = 27.6875
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7573, tnet num: 2121, tinst num: 1588, tnode num: 10742, tedge num: 12830.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 91.81 peak overflow 2.75
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2123.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52080, over cnt = 235(0%), over = 1053, worst = 21
PHY-1001 : End global iterations;  0.107791s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (116.0%)

PHY-1001 : Congestion index: top1 = 43.53, top5 = 25.53, top10 = 15.92, top15 = 11.26.
PHY-1001 : End incremental global routing;  0.181238s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (103.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2121 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.095796s wall, 0.078125s user + 0.015625s system = 0.093750s CPU (97.9%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.320172s wall, 0.312500s user + 0.015625s system = 0.328125s CPU (102.5%)

OPT-1001 : Current memory(MB): used = 209, reserve = 176, peak = 209.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1627/2123.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52080, over cnt = 235(0%), over = 1053, worst = 21
PHY-1002 : len = 58072, over cnt = 164(0%), over = 463, worst = 17
PHY-1002 : len = 63888, over cnt = 32(0%), over = 34, worst = 2
PHY-1002 : len = 64496, over cnt = 16(0%), over = 16, worst = 1
PHY-1002 : len = 65216, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.141486s wall, 0.171875s user + 0.031250s system = 0.203125s CPU (143.6%)

PHY-1001 : Congestion index: top1 = 38.00, top5 = 25.23, top10 = 17.78, top15 = 13.14.
OPT-1001 : End congestion update;  0.199676s wall, 0.234375s user + 0.031250s system = 0.265625s CPU (133.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2121 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.076911s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (101.6%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.281421s wall, 0.312500s user + 0.031250s system = 0.343750s CPU (122.1%)

OPT-1001 : Current memory(MB): used = 212, reserve = 179, peak = 212.
OPT-1001 : End physical optimization;  1.003271s wall, 1.125000s user + 0.062500s system = 1.187500s CPU (118.4%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 375 LUT to BLE ...
SYN-4008 : Packed 375 LUT and 192 SEQ to BLE.
SYN-4003 : Packing 775 remaining SEQ's ...
SYN-4005 : Packed 106 SEQ with LUT/SLICE
SYN-4006 : 90 single LUT's are left
SYN-4006 : 669 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1044/1356 primitive instances ...
PHY-3001 : End packing;  0.068307s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (68.6%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 797 instances
RUN-1001 : 373 mslices, 373 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1939 nets
RUN-1001 : 1363 nets have 2 pins
RUN-1001 : 466 nets have [3 - 5] pins
RUN-1001 : 67 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 795 instances, 746 slices, 20 macros(197 instances: 127 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 50372.6, Over = 54.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6333, tnet num: 1937, tinst num: 795, tnode num: 8622, tedge num: 11183.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1937 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.397019s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (102.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.66699e-05
PHY-3002 : Step(157): len = 49802.9, overlap = 54.75
PHY-3002 : Step(158): len = 49131.8, overlap = 55.5
PHY-3002 : Step(159): len = 48657.3, overlap = 57.25
PHY-3002 : Step(160): len = 48757.4, overlap = 59
PHY-3002 : Step(161): len = 48326.3, overlap = 59.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.33398e-05
PHY-3002 : Step(162): len = 48665.9, overlap = 58
PHY-3002 : Step(163): len = 48892.7, overlap = 55
PHY-3002 : Step(164): len = 49488.6, overlap = 53.25
PHY-3002 : Step(165): len = 49813.7, overlap = 52.25
PHY-3002 : Step(166): len = 49658.7, overlap = 50.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00014668
PHY-3002 : Step(167): len = 50091.8, overlap = 49.5
PHY-3002 : Step(168): len = 50452.7, overlap = 49.75
PHY-3001 : Before Legalized: Len = 50452.7
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.121588s wall, 0.093750s user + 0.187500s system = 0.281250s CPU (231.3%)

PHY-3001 : After Legalized: Len = 63441.8, Over = 0
PHY-3001 : Trial Legalized: Len = 63441.8
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1937 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.072637s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (86.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000913265
PHY-3002 : Step(169): len = 59432, overlap = 9.5
PHY-3002 : Step(170): len = 58294.4, overlap = 10.5
PHY-3002 : Step(171): len = 56306.2, overlap = 13
PHY-3002 : Step(172): len = 55674.4, overlap = 15.25
PHY-3002 : Step(173): len = 54966.9, overlap = 19
PHY-3002 : Step(174): len = 54498.1, overlap = 19.25
PHY-3002 : Step(175): len = 54231.2, overlap = 20.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00182653
PHY-3002 : Step(176): len = 54456, overlap = 19.75
PHY-3002 : Step(177): len = 54487.1, overlap = 18.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00365306
PHY-3002 : Step(178): len = 54613.3, overlap = 18.5
PHY-3002 : Step(179): len = 54639.7, overlap = 18.25
PHY-3001 : Before Legalized: Len = 54639.7
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008796s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (177.6%)

PHY-3001 : After Legalized: Len = 59195.6, Over = 0
PHY-3001 : Legalized: Len = 59195.6, Over = 0
PHY-3001 : Spreading special nets. 11 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.009442s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (165.5%)

PHY-3001 : 19 instances has been re-located, deltaX = 5, deltaY = 15, maxDist = 2.
PHY-3001 : Final: Len = 59579.6, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6333, tnet num: 1937, tinst num: 795, tnode num: 8622, tedge num: 11183.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 57/1939.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 65432, over cnt = 152(0%), over = 238, worst = 6
PHY-1002 : len = 66368, over cnt = 100(0%), over = 126, worst = 3
PHY-1002 : len = 67952, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 67968, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 68064, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.195098s wall, 0.218750s user + 0.031250s system = 0.250000s CPU (128.1%)

PHY-1001 : Congestion index: top1 = 31.12, top5 = 22.99, top10 = 17.63, top15 = 13.70.
PHY-1001 : End incremental global routing;  0.263799s wall, 0.296875s user + 0.031250s system = 0.328125s CPU (124.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1937 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.097927s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (95.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.407358s wall, 0.421875s user + 0.031250s system = 0.453125s CPU (111.2%)

OPT-1001 : Current memory(MB): used = 213, reserve = 181, peak = 214.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1699/1939.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68064, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.009146s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.12, top5 = 22.99, top10 = 17.63, top15 = 13.70.
OPT-1001 : End congestion update;  0.082451s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (113.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1937 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.074353s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (84.1%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.159824s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (107.5%)

OPT-1001 : Current memory(MB): used = 215, reserve = 183, peak = 215.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1937 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.072442s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (86.3%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1699/1939.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68064, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008216s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.12, top5 = 22.99, top10 = 17.63, top15 = 13.70.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1937 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.068366s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (91.4%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.689655
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  1.180714s wall, 1.187500s user + 0.046875s system = 1.234375s CPU (104.5%)

RUN-1003 : finish command "place" in  9.484795s wall, 13.468750s user + 5.937500s system = 19.406250s CPU (204.6%)

RUN-1004 : used memory is 192 MB, reserved memory is 158 MB, peak memory is 216 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 797 instances
RUN-1001 : 373 mslices, 373 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1939 nets
RUN-1001 : 1363 nets have 2 pins
RUN-1001 : 466 nets have [3 - 5] pins
RUN-1001 : 67 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6333, tnet num: 1937, tinst num: 795, tnode num: 8622, tedge num: 11183.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 373 mslices, 373 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1937 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 64856, over cnt = 156(0%), over = 238, worst = 7
PHY-1002 : len = 65872, over cnt = 101(0%), over = 124, worst = 3
PHY-1002 : len = 67400, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 67504, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.173900s wall, 0.187500s user + 0.062500s system = 0.250000s CPU (143.8%)

PHY-1001 : Congestion index: top1 = 31.21, top5 = 22.85, top10 = 17.53, top15 = 13.59.
PHY-1001 : End global routing;  0.239972s wall, 0.250000s user + 0.078125s system = 0.328125s CPU (136.7%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 234, reserve = 202, peak = 234.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 496, reserve = 466, peak = 496.
PHY-1001 : End build detailed router design. 4.191217s wall, 4.156250s user + 0.031250s system = 4.187500s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 32824, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.412306s wall, 1.406250s user + 0.000000s system = 1.406250s CPU (99.6%)

PHY-1001 : Current memory(MB): used = 528, reserve = 499, peak = 528.
PHY-1001 : End phase 1; 1.419901s wall, 1.421875s user + 0.000000s system = 1.421875s CPU (100.1%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 28% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 180536, over cnt = 25(0%), over = 25, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 529, reserve = 500, peak = 529.
PHY-1001 : End initial routed; 1.839356s wall, 2.593750s user + 0.171875s system = 2.765625s CPU (150.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1723(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.629   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -46.931  |  22   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.443274s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (102.2%)

PHY-1001 : Current memory(MB): used = 530, reserve = 502, peak = 530.
PHY-1001 : End phase 2; 2.282753s wall, 3.046875s user + 0.171875s system = 3.218750s CPU (141.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 180536, over cnt = 25(0%), over = 25, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.018428s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (84.8%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 180416, over cnt = 10(0%), over = 10, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.029886s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (104.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 180432, over cnt = 4(0%), over = 4, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.025034s wall, 0.046875s user + 0.031250s system = 0.078125s CPU (312.1%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 180480, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.022912s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (68.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1723(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.629   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -46.931  |  22   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.435692s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (96.8%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 7 feed throughs used by 7 nets
PHY-1001 : End commit to database; 0.222484s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (105.3%)

PHY-1001 : Current memory(MB): used = 547, reserve = 519, peak = 547.
PHY-1001 : End phase 3; 0.902304s wall, 0.921875s user + 0.031250s system = 0.953125s CPU (105.6%)

PHY-1003 : Routed, final wirelength = 180480
PHY-1001 : Current memory(MB): used = 547, reserve = 519, peak = 547.
PHY-1001 : End export database. 0.010502s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  9.033378s wall, 9.781250s user + 0.234375s system = 10.015625s CPU (110.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6333, tnet num: 1937, tinst num: 795, tnode num: 8622, tedge num: 11183.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[34] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dib[7] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[3] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[0] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[47] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[2] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[5] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[52] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[7] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[54] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_66.dia[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[55] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_66.dia[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[7] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[15] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg1_syn_185.mi[0] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_100.mi[0] slack -2756ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_100.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_103.mi[0] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_82.mi[0] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_82.mi[1] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_85.mi[0] slack -2897ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_85.mi[1] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_88.mi[0] slack -2911ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_88.mi[1] slack -2911ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_91.mi[0] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_91.mi[1] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_94.mi[0] slack -2897ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_94.mi[1] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_97.mi[0] slack -2791ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_97.mi[1] slack -2897ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6453, tnet num: 1997, tinst num: 855, tnode num: 8742, tedge num: 11303.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_103_mi[0] slack -33ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_88_mi[1] slack -229ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg2_syn_88_mi[0] slack -688ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_85_mi[1] slack -304ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_85_mi[0] slack -149ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_91_mi[1] slack -134ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_91_mi[0] slack -746ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_94_mi[0] slack -714ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_94_mi[1] slack -746ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_97_mi[1] slack -715ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_97_mi[0] slack -224ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_100_mi[0] slack -465ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_100_mi[1] slack -539ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg1_syn_185_mi[0] slack -49ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_82_mi[1] slack -757ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_82_mi[0] slack -502ps
RUN-1001 : End hold fix;  4.089965s wall, 4.046875s user + 0.453125s system = 4.500000s CPU (110.0%)

RUN-1003 : finish command "route" in  13.807697s wall, 14.515625s user + 0.765625s system = 15.281250s CPU (110.7%)

RUN-1004 : used memory is 500 MB, reserved memory is 476 MB, peak memory is 547 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      893   out of  19600    4.56%
#reg                     1053   out of  19600    5.37%
#le                      1562
  #lut only               509   out of   1562   32.59%
  #reg only               669   out of   1562   42.83%
  #lut&reg                384   out of   1562   24.58%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         478
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         102
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q0    38
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1562   |696     |197     |1086    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1072   |290     |133     |864     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |25     |21      |4       |22      |0       |0       |
|    demodu                  |Demodulation                                     |454    |100     |46      |350     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |54     |28      |6       |45      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |6       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |13     |6       |0       |13      |0       |0       |
|    integ                   |Integration                                      |144    |30      |15      |116     |0       |0       |
|    modu                    |Modulation                                       |99     |48      |14      |97      |0       |1       |
|    rs422                   |Rs422Output                                      |326    |75      |46      |259     |0       |4       |
|    trans                   |SquareWaveGenerator                              |24     |16      |8       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |160    |133     |7       |115     |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |23     |23      |0       |14      |0       |0       |
|    U2                      |Ctrl_Data                                        |100    |82      |0       |85      |0       |0       |
|  wendu                     |DS18B20                                          |306    |261     |45      |73      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1384  
    #2          2       339   
    #3          3       105   
    #4          4        22   
    #5        5-10       73   
    #6        11-50      31   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6453, tnet num: 1997, tinst num: 855, tnode num: 8742, tedge num: 11303.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1997 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 855
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1999, pip num: 14644
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 10
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1277 valid insts, and 39243 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  6.005202s wall, 31.859375s user + 0.125000s system = 31.984375s CPU (532.6%)

RUN-1004 : used memory is 520 MB, reserved memory is 491 MB, peak memory is 668 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250805_102738.log"
