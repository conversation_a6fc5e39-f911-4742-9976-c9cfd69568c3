============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Fri Aug 15 14:26:41 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1552 instances
RUN-0007 : 366 luts, 938 seqs, 127 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2079 nets
RUN-1001 : 1535 nets have 2 pins
RUN-1001 : 443 nets have [3 - 5] pins
RUN-1001 : 59 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     237     
RUN-1001 :   No   |  No   |  Yes  |     122     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1550 instances, 366 luts, 938 seqs, 197 slices, 21 macros(197 instances: 127 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7407, tnet num: 2077, tinst num: 1550, tnode num: 10504, tedge num: 12544.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2077 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.255391s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (97.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 518488
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1550.
PHY-3001 : End clustering;  0.000058s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 448839, overlap = 20.25
PHY-3002 : Step(2): len = 432812, overlap = 13.5
PHY-3002 : Step(3): len = 421474, overlap = 20.25
PHY-3002 : Step(4): len = 412169, overlap = 13.5
PHY-3002 : Step(5): len = 399170, overlap = 18
PHY-3002 : Step(6): len = 380083, overlap = 11.25
PHY-3002 : Step(7): len = 372320, overlap = 13.5
PHY-3002 : Step(8): len = 362555, overlap = 13.5
PHY-3002 : Step(9): len = 349923, overlap = 11.25
PHY-3002 : Step(10): len = 344682, overlap = 11.25
PHY-3002 : Step(11): len = 336164, overlap = 11.25
PHY-3002 : Step(12): len = 325368, overlap = 11.25
PHY-3002 : Step(13): len = 319760, overlap = 11.25
PHY-3002 : Step(14): len = 314372, overlap = 13.5
PHY-3002 : Step(15): len = 304707, overlap = 13.5
PHY-3002 : Step(16): len = 297591, overlap = 13.5
PHY-3002 : Step(17): len = 293676, overlap = 13.5
PHY-3002 : Step(18): len = 285732, overlap = 13.5
PHY-3002 : Step(19): len = 278687, overlap = 13.5
PHY-3002 : Step(20): len = 274510, overlap = 13.5
PHY-3002 : Step(21): len = 269002, overlap = 13.5
PHY-3002 : Step(22): len = 261594, overlap = 13.5
PHY-3002 : Step(23): len = 256166, overlap = 13.5
PHY-3002 : Step(24): len = 252422, overlap = 18
PHY-3002 : Step(25): len = 244011, overlap = 20.25
PHY-3002 : Step(26): len = 236284, overlap = 20.25
PHY-3002 : Step(27): len = 233283, overlap = 20.25
PHY-3002 : Step(28): len = 228515, overlap = 20.25
PHY-3002 : Step(29): len = 201090, overlap = 20.25
PHY-3002 : Step(30): len = 194126, overlap = 20.25
PHY-3002 : Step(31): len = 193015, overlap = 20.25
PHY-3002 : Step(32): len = 183255, overlap = 20.25
PHY-3002 : Step(33): len = 156136, overlap = 20.25
PHY-3002 : Step(34): len = 150082, overlap = 20.25
PHY-3002 : Step(35): len = 149288, overlap = 20.25
PHY-3002 : Step(36): len = 140605, overlap = 20.25
PHY-3002 : Step(37): len = 125752, overlap = 18
PHY-3002 : Step(38): len = 124199, overlap = 20.25
PHY-3002 : Step(39): len = 120177, overlap = 18
PHY-3002 : Step(40): len = 117136, overlap = 18
PHY-3002 : Step(41): len = 115082, overlap = 20.25
PHY-3002 : Step(42): len = 113382, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.96595e-05
PHY-3002 : Step(43): len = 113602, overlap = 13.5
PHY-3002 : Step(44): len = 112734, overlap = 13.5
PHY-3002 : Step(45): len = 111535, overlap = 15.75
PHY-3002 : Step(46): len = 110800, overlap = 13.5
PHY-3002 : Step(47): len = 108924, overlap = 13.5
PHY-3002 : Step(48): len = 103818, overlap = 13.5
PHY-3002 : Step(49): len = 101137, overlap = 11.375
PHY-3002 : Step(50): len = 100118, overlap = 11.5
PHY-3002 : Step(51): len = 97398.6, overlap = 13.8125
PHY-3002 : Step(52): len = 95507.4, overlap = 13.6875
PHY-3002 : Step(53): len = 94100.3, overlap = 13.8125
PHY-3002 : Step(54): len = 92915.4, overlap = 14.0625
PHY-3002 : Step(55): len = 90822.7, overlap = 14.3125
PHY-3002 : Step(56): len = 90376.2, overlap = 14.3125
PHY-3002 : Step(57): len = 88311.3, overlap = 13.5
PHY-3002 : Step(58): len = 84872.3, overlap = 13.5
PHY-3002 : Step(59): len = 82297.9, overlap = 15.75
PHY-3002 : Step(60): len = 82135.1, overlap = 15.75
PHY-3002 : Step(61): len = 80515.5, overlap = 15.75
PHY-3002 : Step(62): len = 79423.5, overlap = 16.5
PHY-3002 : Step(63): len = 77076.3, overlap = 19.25
PHY-3002 : Step(64): len = 76140.6, overlap = 19.25
PHY-3002 : Step(65): len = 73453.8, overlap = 17.3125
PHY-3002 : Step(66): len = 72279.2, overlap = 17.375
PHY-3002 : Step(67): len = 71582.9, overlap = 17.5
PHY-3002 : Step(68): len = 70537, overlap = 17.5
PHY-3002 : Step(69): len = 69002.9, overlap = 17.1875
PHY-3002 : Step(70): len = 67540.1, overlap = 16.875
PHY-3002 : Step(71): len = 66465.2, overlap = 16.5
PHY-3002 : Step(72): len = 65829.9, overlap = 16.5
PHY-3002 : Step(73): len = 65206.2, overlap = 16.4375
PHY-3002 : Step(74): len = 64664.3, overlap = 14.1875
PHY-3002 : Step(75): len = 64208.6, overlap = 14.0625
PHY-3002 : Step(76): len = 63749.1, overlap = 16.3125
PHY-3002 : Step(77): len = 63426.7, overlap = 14.0625
PHY-3002 : Step(78): len = 62100.1, overlap = 14
PHY-3002 : Step(79): len = 61781.7, overlap = 16.25
PHY-3002 : Step(80): len = 61174.5, overlap = 14
PHY-3002 : Step(81): len = 60598.2, overlap = 14
PHY-3002 : Step(82): len = 60479, overlap = 13.875
PHY-3002 : Step(83): len = 60385.7, overlap = 13.5625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000179319
PHY-3002 : Step(84): len = 60642.9, overlap = 13.5625
PHY-3002 : Step(85): len = 60636.4, overlap = 13.5625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000358638
PHY-3002 : Step(86): len = 60936.2, overlap = 13.5625
PHY-3002 : Step(87): len = 61042.9, overlap = 13.5625
PHY-3001 : Before Legalized: Len = 61042.9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006580s wall, 0.015625s user + 0.031250s system = 0.046875s CPU (712.4%)

PHY-3001 : After Legalized: Len = 64767.7, Over = 0.0625
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2077 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.062167s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(88): len = 64914.3, overlap = 10.0312
PHY-3002 : Step(89): len = 65168.4, overlap = 9.6875
PHY-3002 : Step(90): len = 64507.2, overlap = 8.4375
PHY-3002 : Step(91): len = 64631.4, overlap = 8.5625
PHY-3002 : Step(92): len = 64147.7, overlap = 9.9375
PHY-3002 : Step(93): len = 63501.2, overlap = 10.125
PHY-3002 : Step(94): len = 63137.1, overlap = 10
PHY-3002 : Step(95): len = 62217.7, overlap = 9.875
PHY-3002 : Step(96): len = 60716.5, overlap = 9.75
PHY-3002 : Step(97): len = 60202.1, overlap = 10.0625
PHY-3002 : Step(98): len = 59616.9, overlap = 11.3438
PHY-3002 : Step(99): len = 58790.5, overlap = 11.3125
PHY-3002 : Step(100): len = 57890.2, overlap = 11.3125
PHY-3002 : Step(101): len = 57538.8, overlap = 12.0938
PHY-3002 : Step(102): len = 56981.3, overlap = 10.9375
PHY-3002 : Step(103): len = 56687.2, overlap = 14.4688
PHY-3002 : Step(104): len = 56263.5, overlap = 15.5312
PHY-3002 : Step(105): len = 55251.9, overlap = 15.4688
PHY-3002 : Step(106): len = 54675.3, overlap = 15.875
PHY-3002 : Step(107): len = 54371.5, overlap = 16.125
PHY-3002 : Step(108): len = 54297.2, overlap = 16.375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00203668
PHY-3002 : Step(109): len = 54156.9, overlap = 16.375
PHY-3002 : Step(110): len = 53990.4, overlap = 16.6562
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00407336
PHY-3002 : Step(111): len = 53804.6, overlap = 16.6562
PHY-3002 : Step(112): len = 53813.1, overlap = 16.6562
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2077 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.061611s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.17449e-05
PHY-3002 : Step(113): len = 54389.2, overlap = 58.5312
PHY-3002 : Step(114): len = 55271.2, overlap = 54.7812
PHY-3002 : Step(115): len = 55249.8, overlap = 54.5
PHY-3002 : Step(116): len = 54886.1, overlap = 52.9062
PHY-3002 : Step(117): len = 55013.7, overlap = 53.1562
PHY-3002 : Step(118): len = 55038.2, overlap = 52.9375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00016349
PHY-3002 : Step(119): len = 55111.6, overlap = 52.1875
PHY-3002 : Step(120): len = 55215.3, overlap = 51.625
PHY-3002 : Step(121): len = 55647.5, overlap = 49.8125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00032698
PHY-3002 : Step(122): len = 55833, overlap = 45.5625
PHY-3002 : Step(123): len = 56829.7, overlap = 35.5938
PHY-3002 : Step(124): len = 57081.7, overlap = 40.8125
PHY-3002 : Step(125): len = 56881.9, overlap = 37.8438
PHY-3002 : Step(126): len = 56943.9, overlap = 36.8438
PHY-3002 : Step(127): len = 56968.6, overlap = 36.3438
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000653959
PHY-3002 : Step(128): len = 56908.3, overlap = 34.8438
PHY-3002 : Step(129): len = 57130.1, overlap = 35.75
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7407, tnet num: 2077, tinst num: 1550, tnode num: 10504, tedge num: 12544.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 96.50 peak overflow 2.56
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2079.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 59840, over cnt = 208(0%), over = 947, worst = 25
PHY-1001 : End global iterations;  0.070660s wall, 0.125000s user + 0.031250s system = 0.156250s CPU (221.1%)

PHY-1001 : Congestion index: top1 = 44.31, top5 = 25.34, top10 = 16.27, top15 = 11.69.
PHY-1001 : End incremental global routing;  0.121441s wall, 0.156250s user + 0.046875s system = 0.203125s CPU (167.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2077 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067043s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (93.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.216213s wall, 0.250000s user + 0.046875s system = 0.296875s CPU (137.3%)

OPT-1001 : Current memory(MB): used = 207, reserve = 174, peak = 207.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1615/2079.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 59840, over cnt = 208(0%), over = 947, worst = 25
PHY-1002 : len = 65832, over cnt = 179(0%), over = 532, worst = 18
PHY-1002 : len = 71008, over cnt = 69(0%), over = 139, worst = 9
PHY-1002 : len = 72088, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 72224, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.082075s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (171.3%)

PHY-1001 : Congestion index: top1 = 37.89, top5 = 24.59, top10 = 18.01, top15 = 13.50.
OPT-1001 : End congestion update;  0.125620s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (136.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2077 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.055040s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (85.2%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.184201s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (118.8%)

OPT-1001 : Current memory(MB): used = 209, reserve = 176, peak = 209.
OPT-1001 : End physical optimization;  0.633411s wall, 0.703125s user + 0.062500s system = 0.765625s CPU (120.9%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 366 LUT to BLE ...
SYN-4008 : Packed 366 LUT and 187 SEQ to BLE.
SYN-4003 : Packing 751 remaining SEQ's ...
SYN-4005 : Packed 94 SEQ with LUT/SLICE
SYN-4006 : 99 single LUT's are left
SYN-4006 : 657 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1023/1335 primitive instances ...
PHY-3001 : End packing;  0.043121s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (72.5%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 784 instances
RUN-1001 : 367 mslices, 366 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1900 nets
RUN-1001 : 1361 nets have 2 pins
RUN-1001 : 439 nets have [3 - 5] pins
RUN-1001 : 59 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 782 instances, 733 slices, 21 macros(197 instances: 127 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 57235, Over = 56
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6182, tnet num: 1898, tinst num: 782, tnode num: 8412, tedge num: 10903.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1898 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.269905s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (98.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.35532e-05
PHY-3002 : Step(130): len = 56500.5, overlap = 54.5
PHY-3002 : Step(131): len = 55899.2, overlap = 58.75
PHY-3002 : Step(132): len = 55643, overlap = 58.75
PHY-3002 : Step(133): len = 55604.9, overlap = 56.5
PHY-3002 : Step(134): len = 55618.1, overlap = 56.75
PHY-3002 : Step(135): len = 55414, overlap = 56.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.71064e-05
PHY-3002 : Step(136): len = 55474.1, overlap = 55.5
PHY-3002 : Step(137): len = 55849.6, overlap = 55.25
PHY-3002 : Step(138): len = 56093.5, overlap = 53.5
PHY-3002 : Step(139): len = 56401.6, overlap = 52.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000174213
PHY-3002 : Step(140): len = 56692.3, overlap = 48.75
PHY-3002 : Step(141): len = 57518.3, overlap = 48.25
PHY-3001 : Before Legalized: Len = 57518.3
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.078590s wall, 0.062500s user + 0.109375s system = 0.171875s CPU (218.7%)

PHY-3001 : After Legalized: Len = 70128.2, Over = 0
PHY-3001 : Trial Legalized: Len = 70128.2
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1898 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.046481s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00137185
PHY-3002 : Step(142): len = 66518.9, overlap = 4.5
PHY-3002 : Step(143): len = 64952.4, overlap = 10
PHY-3002 : Step(144): len = 62834.5, overlap = 13
PHY-3002 : Step(145): len = 61545.4, overlap = 14.75
PHY-3002 : Step(146): len = 60909.2, overlap = 16
PHY-3002 : Step(147): len = 60135.6, overlap = 18.25
PHY-3002 : Step(148): len = 59654.1, overlap = 20
PHY-3002 : Step(149): len = 59505.1, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00274371
PHY-3002 : Step(150): len = 59584.5, overlap = 20
PHY-3002 : Step(151): len = 59629.3, overlap = 20.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00548741
PHY-3002 : Step(152): len = 59672.8, overlap = 21.25
PHY-3002 : Step(153): len = 59685.1, overlap = 20.75
PHY-3001 : Before Legalized: Len = 59685.1
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005012s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 64022.3, Over = 0
PHY-3001 : Legalized: Len = 64022.3, Over = 0
PHY-3001 : Spreading special nets. 12 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005377s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 21 instances has been re-located, deltaX = 2, deltaY = 19, maxDist = 1.
PHY-3001 : Final: Len = 64188.3, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6182, tnet num: 1898, tinst num: 782, tnode num: 8412, tedge num: 10903.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 68/1900.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70872, over cnt = 152(0%), over = 232, worst = 6
PHY-1002 : len = 71960, over cnt = 74(0%), over = 86, worst = 3
PHY-1002 : len = 72832, over cnt = 6(0%), over = 8, worst = 2
PHY-1002 : len = 72928, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72960, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.126723s wall, 0.140625s user + 0.031250s system = 0.171875s CPU (135.6%)

PHY-1001 : Congestion index: top1 = 31.34, top5 = 22.65, top10 = 17.44, top15 = 13.59.
PHY-1001 : End incremental global routing;  0.175858s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (124.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1898 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057110s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (109.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.260884s wall, 0.281250s user + 0.031250s system = 0.312500s CPU (119.8%)

OPT-1001 : Current memory(MB): used = 211, reserve = 179, peak = 212.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1697/1900.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72960, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006221s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (251.2%)

PHY-1001 : Congestion index: top1 = 31.34, top5 = 22.65, top10 = 17.44, top15 = 13.59.
OPT-1001 : End congestion update;  0.056513s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (82.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1898 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052154s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (89.9%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.111027s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (98.5%)

OPT-1001 : Current memory(MB): used = 213, reserve = 181, peak = 213.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1898 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047984s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.7%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1697/1900.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72960, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006949s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.34, top5 = 22.65, top10 = 17.44, top15 = 13.59.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1898 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046913s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (99.9%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.896552
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.769685s wall, 0.781250s user + 0.031250s system = 0.812500s CPU (105.6%)

RUN-1003 : finish command "place" in  4.777249s wall, 6.546875s user + 2.968750s system = 9.515625s CPU (199.2%)

RUN-1004 : used memory is 193 MB, reserved memory is 160 MB, peak memory is 214 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 784 instances
RUN-1001 : 367 mslices, 366 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1900 nets
RUN-1001 : 1361 nets have 2 pins
RUN-1001 : 439 nets have [3 - 5] pins
RUN-1001 : 59 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6182, tnet num: 1898, tinst num: 782, tnode num: 8412, tedge num: 10903.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 367 mslices, 366 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1898 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69632, over cnt = 153(0%), over = 238, worst = 6
PHY-1002 : len = 70744, over cnt = 92(0%), over = 114, worst = 4
PHY-1002 : len = 72040, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72072, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.111638s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (126.0%)

PHY-1001 : Congestion index: top1 = 31.47, top5 = 22.42, top10 = 17.26, top15 = 13.44.
PHY-1001 : End global routing;  0.162334s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (125.1%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 232, reserve = 200, peak = 232.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 490, reserve = 462, peak = 490.
PHY-1001 : End build detailed router design. 3.040771s wall, 2.937500s user + 0.078125s system = 3.015625s CPU (99.2%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 31816, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.042603s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (98.9%)

PHY-1001 : Current memory(MB): used = 522, reserve = 495, peak = 522.
PHY-1001 : End phase 1; 1.048396s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (98.4%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 62% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 180176, over cnt = 42(0%), over = 42, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 525, reserve = 496, peak = 525.
PHY-1001 : End initial routed; 1.540699s wall, 2.078125s user + 0.125000s system = 2.203125s CPU (143.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1685(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.931   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.870   |  -44.017  |  21   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.321878s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (97.1%)

PHY-1001 : Current memory(MB): used = 527, reserve = 498, peak = 527.
PHY-1001 : End phase 2; 1.862665s wall, 2.390625s user + 0.125000s system = 2.515625s CPU (135.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 180176, over cnt = 42(0%), over = 42, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.013539s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (115.4%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 179712, over cnt = 9(0%), over = 9, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.037154s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (126.2%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 179808, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.025403s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (123.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1685(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.931   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.870   |  -44.017  |  21   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.316651s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (98.7%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 7 feed throughs used by 7 nets
PHY-1001 : End commit to database; 0.165534s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (94.4%)

PHY-1001 : Current memory(MB): used = 541, reserve = 513, peak = 541.
PHY-1001 : End phase 3; 0.676359s wall, 0.671875s user + 0.000000s system = 0.671875s CPU (99.3%)

PHY-1003 : Routed, final wirelength = 179808
PHY-1001 : Current memory(MB): used = 542, reserve = 514, peak = 542.
PHY-1001 : End export database. 0.009401s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  6.810911s wall, 7.203125s user + 0.203125s system = 7.406250s CPU (108.7%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6182, tnet num: 1898, tinst num: 782, tnode num: 8412, tedge num: 10903.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[7] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[4] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[6] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[53] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[8] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[0] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[3] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[15] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[16] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[17] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[8] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/asy_w_rst1 endpin signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_22.sr slack -73ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_72.mi[0] slack -2630ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_72.mi[1] slack -2726ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_75.mi[0] slack -2630ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_75.mi[1] slack -2494ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_78.mi[0] slack -2870ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_78.mi[1] slack -2630ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_81.mi[0] slack -2858ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_81.mi[1] slack -2858ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_84.mi[0] slack -2630ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_84.mi[1] slack -2726ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_87.mi[0] slack -2712ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_87.mi[1] slack -2858ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_90.mi[0] slack -2630ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_90.mi[1] slack -2738ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_93.mi[0] slack -2870ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_93.mi[1] slack -2738ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6300, tnet num: 1957, tinst num: 841, tnode num: 8530, tedge num: 11021.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_72_mi[1] slack -251ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_72_mi[0] slack -405ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_75_mi[1] slack -434ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_75_mi[0] slack -560ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_81_mi[0] slack -24ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_81_mi[1] slack -561ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_84_mi[1] slack -302ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg2_syn_84_mi[0] slack -345ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_87_mi[1] slack -675ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_87_mi[0] slack -810ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_90_mi[1] slack -522ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_90_mi[0] slack -103ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_93_mi[1] slack -661ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_93_mi[0] slack -524ps
RUN-1001 : End hold fix;  2.907502s wall, 2.984375s user + 0.187500s system = 3.171875s CPU (109.1%)

RUN-1003 : finish command "route" in  10.188323s wall, 10.687500s user + 0.406250s system = 11.093750s CPU (108.9%)

RUN-1004 : used memory is 519 MB, reserved memory is 491 MB, peak memory is 542 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   15
  #output                  21
  #inout                    1

Utilization Statistics
#lut                      880   out of  19600    4.49%
#reg                     1021   out of  19600    5.21%
#le                      1537
  #lut only               516   out of   1537   33.57%
  #reg only               657   out of   1537   42.75%
  #lut&reg                364   out of   1537   23.68%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    13
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         460
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         102
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q0    40
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
  RxTransmit       INPUT        M14        LVCMOS33          N/A          PULLUP      IREG    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1537   |683     |197     |1055    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1040   |284     |133     |832     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |27     |21      |6       |24      |0       |0       |
|    demodu                  |Demodulation                                     |455    |104     |46      |351     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |57     |35      |6       |45      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |18     |8       |0       |18      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |12     |9       |0       |12      |0       |0       |
|    integ                   |Integration                                      |140    |19      |15      |112     |0       |0       |
|    modu                    |Modulation                                       |68     |45      |14      |64      |0       |1       |
|    rs422                   |Rs422Output                                      |321    |72      |46      |265     |0       |4       |
|    trans                   |SquareWaveGenerator                              |29     |23      |6       |16      |0       |0       |
|  u_uart                    |UART_Control                                     |171    |130     |7       |115     |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |27      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |23     |21      |0       |14      |0       |0       |
|    U2                      |Ctrl_Data                                        |113    |82      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |302    |257     |45      |74      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1381  
    #2          2       302   
    #3          3        97   
    #4          4        40   
    #5        5-10       63   
    #6        11-50      31   
    #7       101-500     1    
  Average     1.93            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6300, tnet num: 1957, tinst num: 841, tnode num: 8530, tedge num: 11021.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1957 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 841
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1959, pip num: 14530
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 10
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1308 valid insts, and 38950 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  2.973679s wall, 16.921875s user + 0.062500s system = 16.984375s CPU (571.2%)

RUN-1004 : used memory is 516 MB, reserved memory is 487 MB, peak memory is 667 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250815_142641.log"
