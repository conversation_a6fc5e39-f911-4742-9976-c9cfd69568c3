============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Fri Aug 15 14:55:38 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1584 instances
RUN-0007 : 375 luts, 957 seqs, 131 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2132 nets
RUN-1001 : 1550 nets have 2 pins
RUN-1001 : 478 nets have [3 - 5] pins
RUN-1001 : 61 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     253     
RUN-1001 :   No   |  No   |  Yes  |     125     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1582 instances, 375 luts, 957 seqs, 201 slices, 20 macros(201 instances: 131 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7592, tnet num: 2130, tinst num: 1582, tnode num: 10765, tedge num: 12914.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2130 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.263808s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (100.7%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 541218
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1582.
PHY-3001 : End clustering;  0.000020s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 468252, overlap = 20.25
PHY-3002 : Step(2): len = 444365, overlap = 18
PHY-3002 : Step(3): len = 430096, overlap = 20.25
PHY-3002 : Step(4): len = 420726, overlap = 13.5
PHY-3002 : Step(5): len = 404927, overlap = 15.75
PHY-3002 : Step(6): len = 391676, overlap = 13.5
PHY-3002 : Step(7): len = 382799, overlap = 13.5
PHY-3002 : Step(8): len = 376047, overlap = 18
PHY-3002 : Step(9): len = 361985, overlap = 15.75
PHY-3002 : Step(10): len = 355564, overlap = 13.5
PHY-3002 : Step(11): len = 347638, overlap = 13.5
PHY-3002 : Step(12): len = 339565, overlap = 13.5
PHY-3002 : Step(13): len = 331742, overlap = 13.5
PHY-3002 : Step(14): len = 327719, overlap = 13.5
PHY-3002 : Step(15): len = 313851, overlap = 15.75
PHY-3002 : Step(16): len = 308381, overlap = 15.75
PHY-3002 : Step(17): len = 302888, overlap = 15.75
PHY-3002 : Step(18): len = 292457, overlap = 13.5
PHY-3002 : Step(19): len = 284294, overlap = 13.5
PHY-3002 : Step(20): len = 281890, overlap = 13.5
PHY-3002 : Step(21): len = 265426, overlap = 20.25
PHY-3002 : Step(22): len = 253814, overlap = 20.25
PHY-3002 : Step(23): len = 249989, overlap = 20.25
PHY-3002 : Step(24): len = 245590, overlap = 20.25
PHY-3002 : Step(25): len = 196270, overlap = 20.25
PHY-3002 : Step(26): len = 191493, overlap = 20.25
PHY-3002 : Step(27): len = 189232, overlap = 20.25
PHY-3002 : Step(28): len = 185277, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000118867
PHY-3002 : Step(29): len = 186438, overlap = 18
PHY-3002 : Step(30): len = 184591, overlap = 15.75
PHY-3002 : Step(31): len = 182907, overlap = 18
PHY-3002 : Step(32): len = 180323, overlap = 15.75
PHY-3002 : Step(33): len = 175112, overlap = 13.5
PHY-3002 : Step(34): len = 172928, overlap = 11.25
PHY-3002 : Step(35): len = 166831, overlap = 13.5
PHY-3002 : Step(36): len = 161661, overlap = 13.5
PHY-3002 : Step(37): len = 160025, overlap = 13.5
PHY-3002 : Step(38): len = 157754, overlap = 9
PHY-3002 : Step(39): len = 155259, overlap = 11.25
PHY-3002 : Step(40): len = 150150, overlap = 15.75
PHY-3002 : Step(41): len = 147398, overlap = 15.75
PHY-3002 : Step(42): len = 145725, overlap = 13.5
PHY-3002 : Step(43): len = 140110, overlap = 9
PHY-3002 : Step(44): len = 136169, overlap = 9
PHY-3002 : Step(45): len = 134842, overlap = 11.25
PHY-3002 : Step(46): len = 133853, overlap = 11.25
PHY-3002 : Step(47): len = 129813, overlap = 11.25
PHY-3002 : Step(48): len = 127522, overlap = 9
PHY-3002 : Step(49): len = 124317, overlap = 9
PHY-3002 : Step(50): len = 121956, overlap = 11.25
PHY-3002 : Step(51): len = 120556, overlap = 11.25
PHY-3002 : Step(52): len = 118899, overlap = 13.5
PHY-3002 : Step(53): len = 115669, overlap = 13.5
PHY-3002 : Step(54): len = 113134, overlap = 13.5
PHY-3002 : Step(55): len = 111381, overlap = 11.25
PHY-3002 : Step(56): len = 110020, overlap = 11.25
PHY-3002 : Step(57): len = 106276, overlap = 13.5
PHY-3002 : Step(58): len = 104634, overlap = 9
PHY-3002 : Step(59): len = 101317, overlap = 6.75
PHY-3002 : Step(60): len = 99870.5, overlap = 9
PHY-3002 : Step(61): len = 98425.4, overlap = 11.25
PHY-3002 : Step(62): len = 96604.8, overlap = 11.25
PHY-3002 : Step(63): len = 92865.7, overlap = 11.25
PHY-3002 : Step(64): len = 91172.7, overlap = 11.25
PHY-3002 : Step(65): len = 90567.1, overlap = 9
PHY-3002 : Step(66): len = 89936.3, overlap = 9
PHY-3002 : Step(67): len = 88995.4, overlap = 6.9375
PHY-3002 : Step(68): len = 88019.9, overlap = 9.3125
PHY-3002 : Step(69): len = 86173.2, overlap = 9.4375
PHY-3002 : Step(70): len = 84074.1, overlap = 9.9375
PHY-3002 : Step(71): len = 83142.7, overlap = 10.1875
PHY-3002 : Step(72): len = 82233.1, overlap = 10.25
PHY-3002 : Step(73): len = 81507, overlap = 10.1875
PHY-3002 : Step(74): len = 80698.4, overlap = 10.3125
PHY-3002 : Step(75): len = 76966.6, overlap = 8.5625
PHY-3002 : Step(76): len = 75184.1, overlap = 9.8125
PHY-3002 : Step(77): len = 74271.2, overlap = 7.5625
PHY-3002 : Step(78): len = 73238.4, overlap = 7.5625
PHY-3002 : Step(79): len = 72449.5, overlap = 12.125
PHY-3002 : Step(80): len = 71889.5, overlap = 12.125
PHY-3002 : Step(81): len = 71688.6, overlap = 9.875
PHY-3002 : Step(82): len = 71487.2, overlap = 7.5
PHY-3002 : Step(83): len = 71093.7, overlap = 7.625
PHY-3002 : Step(84): len = 70753.3, overlap = 7.6875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000237735
PHY-3002 : Step(85): len = 71088.8, overlap = 7.5
PHY-3002 : Step(86): len = 71039.8, overlap = 9.75
PHY-3002 : Step(87): len = 70772.4, overlap = 9.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000475469
PHY-3002 : Step(88): len = 71138.9, overlap = 9.75
PHY-3002 : Step(89): len = 71220.6, overlap = 9.75
PHY-3002 : Step(90): len = 71244.6, overlap = 9.6875
PHY-3001 : Before Legalized: Len = 71244.6
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007532s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 73610.9, Over = 2.9375
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2130 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.061810s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(91): len = 73592.1, overlap = 8.21875
PHY-3002 : Step(92): len = 71426.6, overlap = 7.5625
PHY-3002 : Step(93): len = 70077.8, overlap = 7.46875
PHY-3002 : Step(94): len = 68934.4, overlap = 6.71875
PHY-3002 : Step(95): len = 67496.5, overlap = 6.625
PHY-3002 : Step(96): len = 65690.6, overlap = 6.875
PHY-3002 : Step(97): len = 63607.1, overlap = 11.6562
PHY-3002 : Step(98): len = 62557.5, overlap = 13.875
PHY-3002 : Step(99): len = 61836.5, overlap = 14.4688
PHY-3002 : Step(100): len = 59812.3, overlap = 13.9688
PHY-3002 : Step(101): len = 58072.5, overlap = 14.4688
PHY-3002 : Step(102): len = 56882.1, overlap = 15.9688
PHY-3002 : Step(103): len = 55994.2, overlap = 17.0312
PHY-3002 : Step(104): len = 55025.7, overlap = 15.3125
PHY-3002 : Step(105): len = 53413.4, overlap = 16.25
PHY-3002 : Step(106): len = 51832.4, overlap = 16.25
PHY-3002 : Step(107): len = 50867.6, overlap = 16.4062
PHY-3002 : Step(108): len = 50197.5, overlap = 16.0938
PHY-3002 : Step(109): len = 49180.3, overlap = 16.375
PHY-3002 : Step(110): len = 48313.1, overlap = 16.5938
PHY-3002 : Step(111): len = 47869.8, overlap = 16.9062
PHY-3002 : Step(112): len = 47286, overlap = 17.7812
PHY-3002 : Step(113): len = 46729, overlap = 17.75
PHY-3002 : Step(114): len = 46188.4, overlap = 17.8438
PHY-3002 : Step(115): len = 45657.8, overlap = 17.75
PHY-3002 : Step(116): len = 45385.6, overlap = 19.4688
PHY-3002 : Step(117): len = 45278.8, overlap = 22.2188
PHY-3002 : Step(118): len = 44914.5, overlap = 22.7812
PHY-3002 : Step(119): len = 44439.5, overlap = 23.2188
PHY-3002 : Step(120): len = 43826.7, overlap = 23.0938
PHY-3002 : Step(121): len = 43653.5, overlap = 23.3125
PHY-3002 : Step(122): len = 43559.5, overlap = 23.4688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.59641e-05
PHY-3002 : Step(123): len = 43452.7, overlap = 23.5312
PHY-3002 : Step(124): len = 43404.2, overlap = 23.2188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000171928
PHY-3002 : Step(125): len = 43554.2, overlap = 22.0312
PHY-3002 : Step(126): len = 43635.2, overlap = 20.4062
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2130 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.072378s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (86.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.57308e-05
PHY-3002 : Step(127): len = 43656.4, overlap = 61.6875
PHY-3002 : Step(128): len = 44212.7, overlap = 60.875
PHY-3002 : Step(129): len = 44808.4, overlap = 56.3438
PHY-3002 : Step(130): len = 44740.2, overlap = 59.3438
PHY-3002 : Step(131): len = 44787.4, overlap = 59.4062
PHY-3002 : Step(132): len = 44869.2, overlap = 59.125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000131462
PHY-3002 : Step(133): len = 45173.6, overlap = 53.0625
PHY-3002 : Step(134): len = 45324.1, overlap = 52.75
PHY-3002 : Step(135): len = 46392.6, overlap = 45.9062
PHY-3002 : Step(136): len = 47876.3, overlap = 39.5
PHY-3002 : Step(137): len = 48092.1, overlap = 38.8125
PHY-3002 : Step(138): len = 47729.9, overlap = 39
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000262923
PHY-3002 : Step(139): len = 47895.7, overlap = 37.625
PHY-3002 : Step(140): len = 49195.2, overlap = 27.4688
PHY-3002 : Step(141): len = 49339.6, overlap = 26.9062
PHY-3002 : Step(142): len = 49142.3, overlap = 26.0938
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7592, tnet num: 2130, tinst num: 1582, tnode num: 10765, tedge num: 12914.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 86.47 peak overflow 2.03
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2132.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 51536, over cnt = 241(0%), over = 943, worst = 20
PHY-1001 : End global iterations;  0.102802s wall, 0.109375s user + 0.031250s system = 0.140625s CPU (136.8%)

PHY-1001 : Congestion index: top1 = 38.94, top5 = 23.68, top10 = 15.57, top15 = 11.15.
PHY-1001 : End incremental global routing;  0.164745s wall, 0.171875s user + 0.031250s system = 0.203125s CPU (123.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2130 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.073339s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (85.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.268361s wall, 0.265625s user + 0.031250s system = 0.296875s CPU (110.6%)

OPT-1001 : Current memory(MB): used = 208, reserve = 175, peak = 208.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1700/2132.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 51536, over cnt = 241(0%), over = 943, worst = 20
PHY-1002 : len = 56664, over cnt = 170(0%), over = 470, worst = 20
PHY-1002 : len = 62616, over cnt = 42(0%), over = 65, worst = 8
PHY-1002 : len = 63232, over cnt = 14(0%), over = 15, worst = 2
PHY-1002 : len = 63840, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.120051s wall, 0.140625s user + 0.046875s system = 0.187500s CPU (156.2%)

PHY-1001 : Congestion index: top1 = 35.73, top5 = 24.13, top10 = 17.66, top15 = 13.08.
OPT-1001 : End congestion update;  0.167122s wall, 0.187500s user + 0.046875s system = 0.234375s CPU (140.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2130 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.080622s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (116.3%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.251909s wall, 0.281250s user + 0.046875s system = 0.328125s CPU (130.3%)

OPT-1001 : Current memory(MB): used = 211, reserve = 179, peak = 211.
OPT-1001 : End physical optimization;  0.794794s wall, 0.843750s user + 0.078125s system = 0.921875s CPU (116.0%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 375 LUT to BLE ...
SYN-4008 : Packed 375 LUT and 190 SEQ to BLE.
SYN-4003 : Packing 767 remaining SEQ's ...
SYN-4005 : Packed 103 SEQ with LUT/SLICE
SYN-4006 : 96 single LUT's are left
SYN-4006 : 664 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1039/1355 primitive instances ...
PHY-3001 : End packing;  0.053447s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (116.9%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 796 instances
RUN-1001 : 373 mslices, 372 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1950 nets
RUN-1001 : 1372 nets have 2 pins
RUN-1001 : 473 nets have [3 - 5] pins
RUN-1001 : 62 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 794 instances, 745 slices, 20 macros(201 instances: 131 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 49335, Over = 55.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6359, tnet num: 1948, tinst num: 794, tnode num: 8651, tedge num: 11276.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1948 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.362507s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (103.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.20392e-05
PHY-3002 : Step(143): len = 49104.8, overlap = 54.75
PHY-3002 : Step(144): len = 48438.6, overlap = 54
PHY-3002 : Step(145): len = 48132.9, overlap = 54.25
PHY-3002 : Step(146): len = 48264.3, overlap = 56.25
PHY-3002 : Step(147): len = 48096.5, overlap = 55.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.40783e-05
PHY-3002 : Step(148): len = 48490.7, overlap = 54.5
PHY-3002 : Step(149): len = 48810.3, overlap = 51.5
PHY-3002 : Step(150): len = 49742.6, overlap = 46.5
PHY-3002 : Step(151): len = 50223.6, overlap = 43
PHY-3002 : Step(152): len = 49987.7, overlap = 43.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000128157
PHY-3002 : Step(153): len = 50285.2, overlap = 43.5
PHY-3002 : Step(154): len = 50861, overlap = 41.75
PHY-3001 : Before Legalized: Len = 50861
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.143143s wall, 0.078125s user + 0.140625s system = 0.218750s CPU (152.8%)

PHY-3001 : After Legalized: Len = 62599.3, Over = 0
PHY-3001 : Trial Legalized: Len = 62599.3
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1948 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.054908s wall, 0.031250s user + 0.031250s system = 0.062500s CPU (113.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000767047
PHY-3002 : Step(155): len = 59561.1, overlap = 7.75
PHY-3002 : Step(156): len = 57591.4, overlap = 9.75
PHY-3002 : Step(157): len = 56834, overlap = 11.75
PHY-3002 : Step(158): len = 55734, overlap = 18
PHY-3002 : Step(159): len = 55002, overlap = 19.25
PHY-3002 : Step(160): len = 54623.5, overlap = 20.25
PHY-3002 : Step(161): len = 54601.6, overlap = 20.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00153409
PHY-3002 : Step(162): len = 54777.1, overlap = 18.75
PHY-3002 : Step(163): len = 54876.3, overlap = 18.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00306819
PHY-3002 : Step(164): len = 54952.8, overlap = 18.5
PHY-3002 : Step(165): len = 54952.8, overlap = 18.5
PHY-3001 : Before Legalized: Len = 54952.8
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005253s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 58759.9, Over = 0
PHY-3001 : Legalized: Len = 58759.9, Over = 0
PHY-3001 : Spreading special nets. 14 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006433s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 21 instances has been re-located, deltaX = 11, deltaY = 10, maxDist = 1.
PHY-3001 : Final: Len = 59053.9, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6359, tnet num: 1948, tinst num: 794, tnode num: 8651, tedge num: 11276.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 84/1950.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 64944, over cnt = 159(0%), over = 244, worst = 6
PHY-1002 : len = 66080, over cnt = 83(0%), over = 102, worst = 4
PHY-1002 : len = 66968, over cnt = 28(0%), over = 32, worst = 2
PHY-1002 : len = 67200, over cnt = 18(0%), over = 18, worst = 1
PHY-1002 : len = 67560, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.151291s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (113.6%)

PHY-1001 : Congestion index: top1 = 30.91, top5 = 23.11, top10 = 17.63, top15 = 13.63.
PHY-1001 : End incremental global routing;  0.212177s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (110.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1948 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067854s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (92.1%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.309887s wall, 0.312500s user + 0.015625s system = 0.328125s CPU (105.9%)

OPT-1001 : Current memory(MB): used = 213, reserve = 181, peak = 213.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1723/1950.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 67560, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007208s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 30.91, top5 = 23.11, top10 = 17.63, top15 = 13.63.
OPT-1001 : End congestion update;  0.056970s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (109.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1948 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060645s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (77.3%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 754 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 794 instances, 745 slices, 20 macros(201 instances: 131 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 59107, Over = 0
PHY-3001 : End spreading;  0.005847s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 59107, Over = 0
PHY-3001 : End incremental legalization;  0.042709s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (256.1%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.179557s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (165.3%)

OPT-1001 : Current memory(MB): used = 218, reserve = 186, peak = 218.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1948 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.054182s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (115.4%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1715/1950.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 67608, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007776s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 30.88, top5 = 23.13, top10 = 17.66, top15 = 13.64.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1948 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.054507s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (86.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.482759
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.950287s wall, 1.078125s user + 0.015625s system = 1.093750s CPU (115.1%)

RUN-1003 : finish command "place" in  5.785740s wall, 7.468750s user + 3.328125s system = 10.796875s CPU (186.6%)

RUN-1004 : used memory is 194 MB, reserved memory is 161 MB, peak memory is 218 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 796 instances
RUN-1001 : 373 mslices, 372 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1950 nets
RUN-1001 : 1372 nets have 2 pins
RUN-1001 : 473 nets have [3 - 5] pins
RUN-1001 : 62 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6359, tnet num: 1948, tinst num: 794, tnode num: 8651, tedge num: 11276.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 373 mslices, 372 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1948 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 64232, over cnt = 149(0%), over = 232, worst = 7
PHY-1002 : len = 65440, over cnt = 84(0%), over = 102, worst = 4
PHY-1002 : len = 66512, over cnt = 15(0%), over = 15, worst = 1
PHY-1002 : len = 66800, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.146388s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (106.7%)

PHY-1001 : Congestion index: top1 = 30.43, top5 = 22.97, top10 = 17.46, top15 = 13.49.
PHY-1001 : End global routing;  0.203099s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (100.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 233, reserve = 202, peak = 235.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 495, reserve = 467, peak = 495.
PHY-1001 : End build detailed router design. 3.504978s wall, 3.437500s user + 0.046875s system = 3.484375s CPU (99.4%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33056, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.106098s wall, 1.093750s user + 0.015625s system = 1.109375s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 528, reserve = 500, peak = 529.
PHY-1001 : End phase 1; 1.112840s wall, 1.093750s user + 0.015625s system = 1.109375s CPU (99.7%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 172760, over cnt = 25(0%), over = 25, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 529, reserve = 501, peak = 529.
PHY-1001 : End initial routed; 1.285740s wall, 2.000000s user + 0.187500s system = 2.187500s CPU (170.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1730(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.629   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -47.607  |  22   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.344221s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 532, reserve = 503, peak = 532.
PHY-1001 : End phase 2; 1.630060s wall, 2.343750s user + 0.187500s system = 2.531250s CPU (155.3%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 172760, over cnt = 25(0%), over = 25, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014565s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (107.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 172520, over cnt = 5(0%), over = 5, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.038702s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (121.1%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 172528, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.024668s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (126.7%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 172544, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.020116s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (77.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1730(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.629   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -47.607  |  22   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.343050s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (100.2%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 8 feed throughs used by 8 nets
PHY-1001 : End commit to database; 0.169273s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (101.5%)

PHY-1001 : Current memory(MB): used = 547, reserve = 519, peak = 547.
PHY-1001 : End phase 3; 0.738237s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (99.5%)

PHY-1003 : Routed, final wirelength = 172544
PHY-1001 : Current memory(MB): used = 548, reserve = 520, peak = 548.
PHY-1001 : End export database. 0.010126s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (154.3%)

PHY-1001 : End detail routing;  7.176144s wall, 7.796875s user + 0.250000s system = 8.046875s CPU (112.1%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6359, tnet num: 1948, tinst num: 794, tnode num: 8651, tedge num: 11276.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[29] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dib[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[8] slack -86ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[47] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[2] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[4] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[6] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[52] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[7] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[54] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_66.dia[2] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[55] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_66.dia[5] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[6] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[7] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[7] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_66.mi[0] slack -2897ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_66.mi[1] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_69.mi[0] slack -2897ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_69.mi[1] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_73.mi[0] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_73.mi[1] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_76.mi[0] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_76.mi[1] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_79.mi[0] slack -2911ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_79.mi[1] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_82.mi[0] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_82.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_85.mi[0] slack -2897ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_85.mi[1] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_87.mi[0] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/tx_data_dy_b[1]_syn_26.mi[0] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6483, tnet num: 2010, tinst num: 856, tnode num: 8775, tedge num: 11400.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_69_mi[0] slack -474ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg2_syn_69_mi[1] slack -438ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_76_mi[1] slack -359ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_76_mi[0] slack -488ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_79_mi[0] slack -463ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_79_mi[1] slack -261ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_82_mi[1] slack -728ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_82_mi[0] slack -494ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_85_mi[1] slack -752ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_85_mi[0] slack -659ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_87_mi[0] slack -749ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_73_mi[0] slack -102ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_73_mi[1] slack -387ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_66_mi[1] slack -617ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_66_mi[0] slack -563ps
RUN-1001 : End hold fix;  3.102452s wall, 3.156250s user + 0.218750s system = 3.375000s CPU (108.8%)

RUN-1003 : finish command "route" in  10.820679s wall, 11.468750s user + 0.484375s system = 11.953125s CPU (110.5%)

RUN-1004 : used memory is 522 MB, reserved memory is 493 MB, peak memory is 548 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   15
  #output                  21
  #inout                    1

Utilization Statistics
#lut                      906   out of  19600    4.62%
#reg                     1059   out of  19600    5.40%
#le                      1570
  #lut only               511   out of   1570   32.55%
  #reg only               664   out of   1570   42.29%
  #lut&reg                395   out of   1570   25.16%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         479
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         100
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    39
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
  RxTransmit       INPUT        M14        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1570   |705     |201     |1092    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1077   |302     |137     |869     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |33     |29      |4       |22      |0       |0       |
|    demodu                  |Demodulation                                     |451    |100     |41      |352     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |56     |28      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |5       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |14     |6       |0       |14      |0       |0       |
|    integ                   |Integration                                      |142    |31      |15      |114     |0       |0       |
|    modu                    |Modulation                                       |101    |48      |23      |97      |0       |1       |
|    rs422                   |Rs422Output                                      |326    |78      |46      |264     |0       |4       |
|    trans                   |SquareWaveGenerator                              |24     |16      |8       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |164    |131     |7       |117     |0       |0       |
|    U0                      |speed_select_Tx                                  |34     |25      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |24     |24      |0       |15      |0       |0       |
|    U2                      |Ctrl_Data                                        |106    |82      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |305    |260     |45      |72      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1395  
    #2          2       335   
    #3          3       112   
    #4          4        26   
    #5        5-10       67   
    #6        11-50      32   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6483, tnet num: 2010, tinst num: 856, tnode num: 8775, tedge num: 11400.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2010 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 856
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2012, pip num: 14459
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 12
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1287 valid insts, and 39028 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.186804s wall, 16.984375s user + 0.046875s system = 17.031250s CPU (534.4%)

RUN-1004 : used memory is 519 MB, reserved memory is 491 MB, peak memory is 668 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250815_145538.log"
