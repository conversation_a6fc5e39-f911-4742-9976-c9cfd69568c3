============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Thu Jul 31 16:53:54 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1573 instances
RUN-0007 : 364 luts, 962 seqs, 126 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2102 nets
RUN-1001 : 1533 nets have 2 pins
RUN-1001 : 469 nets have [3 - 5] pins
RUN-1001 : 58 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     263     
RUN-1001 :   No   |  No   |  Yes  |     120     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1571 instances, 364 luts, 962 seqs, 196 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7497, tnet num: 2100, tinst num: 1571, tnode num: 10654, tedge num: 12710.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2100 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.282413s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (99.6%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 558255
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1571.
PHY-3001 : End clustering;  0.000089s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 487345, overlap = 20.25
PHY-3002 : Step(2): len = 448792, overlap = 13.5
PHY-3002 : Step(3): len = 432082, overlap = 20.25
PHY-3002 : Step(4): len = 417506, overlap = 13.5
PHY-3002 : Step(5): len = 403226, overlap = 20.25
PHY-3002 : Step(6): len = 389892, overlap = 18
PHY-3002 : Step(7): len = 377921, overlap = 13.5
PHY-3002 : Step(8): len = 369675, overlap = 18
PHY-3002 : Step(9): len = 356844, overlap = 18
PHY-3002 : Step(10): len = 345724, overlap = 13.5
PHY-3002 : Step(11): len = 339440, overlap = 13.5
PHY-3002 : Step(12): len = 327169, overlap = 15.75
PHY-3002 : Step(13): len = 318614, overlap = 15.75
PHY-3002 : Step(14): len = 313036, overlap = 15.75
PHY-3002 : Step(15): len = 302144, overlap = 15.75
PHY-3002 : Step(16): len = 294171, overlap = 15.75
PHY-3002 : Step(17): len = 290187, overlap = 15.75
PHY-3002 : Step(18): len = 283076, overlap = 15.75
PHY-3002 : Step(19): len = 273863, overlap = 15.75
PHY-3002 : Step(20): len = 269315, overlap = 15.75
PHY-3002 : Step(21): len = 265013, overlap = 15.75
PHY-3002 : Step(22): len = 256248, overlap = 13.5
PHY-3002 : Step(23): len = 250353, overlap = 13.5
PHY-3002 : Step(24): len = 247200, overlap = 13.5
PHY-3002 : Step(25): len = 238128, overlap = 13.5
PHY-3002 : Step(26): len = 230591, overlap = 13.5
PHY-3002 : Step(27): len = 227809, overlap = 13.5
PHY-3002 : Step(28): len = 221607, overlap = 15.75
PHY-3002 : Step(29): len = 213612, overlap = 18
PHY-3002 : Step(30): len = 209380, overlap = 20.25
PHY-3002 : Step(31): len = 206384, overlap = 20.25
PHY-3002 : Step(32): len = 197254, overlap = 20.25
PHY-3002 : Step(33): len = 189079, overlap = 20.25
PHY-3002 : Step(34): len = 186696, overlap = 20.25
PHY-3002 : Step(35): len = 181266, overlap = 20.25
PHY-3002 : Step(36): len = 160160, overlap = 18
PHY-3002 : Step(37): len = 156006, overlap = 20.25
PHY-3002 : Step(38): len = 154879, overlap = 20.25
PHY-3002 : Step(39): len = 120114, overlap = 20.25
PHY-3002 : Step(40): len = 112087, overlap = 18
PHY-3002 : Step(41): len = 109592, overlap = 20.25
PHY-3002 : Step(42): len = 108058, overlap = 20.25
PHY-3002 : Step(43): len = 106355, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00010157
PHY-3002 : Step(44): len = 107067, overlap = 11.25
PHY-3002 : Step(45): len = 106857, overlap = 13.5
PHY-3002 : Step(46): len = 105942, overlap = 15.75
PHY-3002 : Step(47): len = 105726, overlap = 15.75
PHY-3002 : Step(48): len = 104195, overlap = 13.5
PHY-3002 : Step(49): len = 101628, overlap = 15.75
PHY-3002 : Step(50): len = 100994, overlap = 15.75
PHY-3002 : Step(51): len = 99015.5, overlap = 13.5
PHY-3002 : Step(52): len = 97908.5, overlap = 13.5
PHY-3002 : Step(53): len = 95010.4, overlap = 13.5
PHY-3002 : Step(54): len = 93343.8, overlap = 13.5
PHY-3002 : Step(55): len = 91988.5, overlap = 13.5
PHY-3002 : Step(56): len = 91410.4, overlap = 13.5
PHY-3002 : Step(57): len = 89500.2, overlap = 13.5
PHY-3002 : Step(58): len = 89153.6, overlap = 15.75
PHY-3002 : Step(59): len = 87386.2, overlap = 13.5625
PHY-3002 : Step(60): len = 84850.3, overlap = 16.25
PHY-3002 : Step(61): len = 83527.3, overlap = 16.25
PHY-3002 : Step(62): len = 82946.9, overlap = 16.4375
PHY-3002 : Step(63): len = 80136.6, overlap = 14.125
PHY-3002 : Step(64): len = 79886.1, overlap = 16.5625
PHY-3002 : Step(65): len = 78086.6, overlap = 16.5
PHY-3002 : Step(66): len = 76481.1, overlap = 14.4375
PHY-3002 : Step(67): len = 74719.7, overlap = 16.9375
PHY-3002 : Step(68): len = 73933.9, overlap = 17.9375
PHY-3002 : Step(69): len = 72660, overlap = 15.6875
PHY-3002 : Step(70): len = 72402.9, overlap = 15.75
PHY-3002 : Step(71): len = 71007, overlap = 15.5
PHY-3002 : Step(72): len = 69205.2, overlap = 17
PHY-3002 : Step(73): len = 67071.1, overlap = 16.875
PHY-3002 : Step(74): len = 66864.9, overlap = 16.875
PHY-3002 : Step(75): len = 65887.7, overlap = 16.8125
PHY-3002 : Step(76): len = 64691.4, overlap = 19.0625
PHY-3002 : Step(77): len = 62994.5, overlap = 16.8125
PHY-3002 : Step(78): len = 62869.7, overlap = 16.5
PHY-3002 : Step(79): len = 61704.3, overlap = 16.4375
PHY-3002 : Step(80): len = 61322.9, overlap = 13.9375
PHY-3002 : Step(81): len = 61264.5, overlap = 16.4375
PHY-3002 : Step(82): len = 61038.3, overlap = 14.1875
PHY-3002 : Step(83): len = 60607.9, overlap = 11.875
PHY-3002 : Step(84): len = 60519, overlap = 11.875
PHY-3002 : Step(85): len = 60598.5, overlap = 14.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000203141
PHY-3002 : Step(86): len = 60854.5, overlap = 11.75
PHY-3002 : Step(87): len = 60866.5, overlap = 11.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000406282
PHY-3002 : Step(88): len = 61059.4, overlap = 11.75
PHY-3002 : Step(89): len = 61065.8, overlap = 11.75
PHY-3001 : Before Legalized: Len = 61065.8
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008364s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (186.8%)

PHY-3001 : After Legalized: Len = 64778, Over = 0.5
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2100 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.065932s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (118.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00317068
PHY-3002 : Step(90): len = 64859.7, overlap = 8.28125
PHY-3002 : Step(91): len = 64788.2, overlap = 8.90625
PHY-3002 : Step(92): len = 63641.3, overlap = 9.40625
PHY-3002 : Step(93): len = 63140.4, overlap = 10.0938
PHY-3002 : Step(94): len = 61613.5, overlap = 10.4688
PHY-3002 : Step(95): len = 60891.7, overlap = 10.0938
PHY-3002 : Step(96): len = 60187.4, overlap = 10.25
PHY-3002 : Step(97): len = 59604.9, overlap = 10.3125
PHY-3002 : Step(98): len = 58449.8, overlap = 7.78125
PHY-3002 : Step(99): len = 56825.6, overlap = 7.34375
PHY-3002 : Step(100): len = 56203.1, overlap = 6.90625
PHY-3002 : Step(101): len = 55893.7, overlap = 6.5
PHY-3002 : Step(102): len = 55260, overlap = 6.5625
PHY-3002 : Step(103): len = 55009.1, overlap = 8.25
PHY-3002 : Step(104): len = 54622.2, overlap = 12.0938
PHY-3002 : Step(105): len = 54116.4, overlap = 12.9375
PHY-3002 : Step(106): len = 53725, overlap = 13.4375
PHY-3002 : Step(107): len = 53324.7, overlap = 15.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00634136
PHY-3002 : Step(108): len = 53268.5, overlap = 13.25
PHY-3002 : Step(109): len = 53157.7, overlap = 13.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2100 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.061634s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000120959
PHY-3002 : Step(110): len = 53309.8, overlap = 54.625
PHY-3002 : Step(111): len = 54038, overlap = 46.8125
PHY-3002 : Step(112): len = 54658.5, overlap = 44.7812
PHY-3002 : Step(113): len = 54621.2, overlap = 44.7188
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000241919
PHY-3002 : Step(114): len = 54428.7, overlap = 44.6875
PHY-3002 : Step(115): len = 54327.7, overlap = 44.9375
PHY-3002 : Step(116): len = 54602.7, overlap = 42.4375
PHY-3002 : Step(117): len = 54776.5, overlap = 40.6875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000483838
PHY-3002 : Step(118): len = 55462.1, overlap = 37.5938
PHY-3002 : Step(119): len = 55607.8, overlap = 35.125
PHY-3002 : Step(120): len = 56178.8, overlap = 35.1562
PHY-3002 : Step(121): len = 56307.2, overlap = 33.7188
PHY-3002 : Step(122): len = 56081.2, overlap = 34.3125
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7497, tnet num: 2100, tinst num: 1571, tnode num: 10654, tedge num: 12710.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 79.25 peak overflow 2.62
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2102.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58464, over cnt = 215(0%), over = 944, worst = 25
PHY-1001 : End global iterations;  0.065345s wall, 0.093750s user + 0.031250s system = 0.125000s CPU (191.3%)

PHY-1001 : Congestion index: top1 = 43.49, top5 = 24.72, top10 = 16.08, top15 = 11.54.
PHY-1001 : End incremental global routing;  0.113752s wall, 0.109375s user + 0.046875s system = 0.156250s CPU (137.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2100 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.065972s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (94.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.209328s wall, 0.203125s user + 0.046875s system = 0.250000s CPU (119.4%)

OPT-1001 : Current memory(MB): used = 207, reserve = 174, peak = 207.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1560/2102.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58464, over cnt = 215(0%), over = 944, worst = 25
PHY-1002 : len = 66168, over cnt = 144(0%), over = 294, worst = 11
PHY-1002 : len = 69136, over cnt = 35(0%), over = 56, worst = 7
PHY-1002 : len = 69560, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 70224, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.101207s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (154.4%)

PHY-1001 : Congestion index: top1 = 36.16, top5 = 24.47, top10 = 17.76, top15 = 13.19.
OPT-1001 : End congestion update;  0.144840s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (140.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2100 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056758s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (82.6%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.205429s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (121.7%)

OPT-1001 : Current memory(MB): used = 210, reserve = 178, peak = 210.
OPT-1001 : End physical optimization;  0.659426s wall, 0.718750s user + 0.078125s system = 0.796875s CPU (120.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 364 LUT to BLE ...
SYN-4008 : Packed 364 LUT and 187 SEQ to BLE.
SYN-4003 : Packing 775 remaining SEQ's ...
SYN-4005 : Packed 82 SEQ with LUT/SLICE
SYN-4006 : 114 single LUT's are left
SYN-4006 : 693 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1057/1368 primitive instances ...
PHY-3001 : End packing;  0.056052s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (83.6%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 806 instances
RUN-1001 : 378 mslices, 377 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1923 nets
RUN-1001 : 1359 nets have 2 pins
RUN-1001 : 460 nets have [3 - 5] pins
RUN-1001 : 62 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 804 instances, 755 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 55985.6, Over = 59
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6288, tnet num: 1921, tinst num: 804, tnode num: 8582, tedge num: 11106.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1921 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.555675s wall, 0.500000s user + 0.015625s system = 0.515625s CPU (92.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.84109e-05
PHY-3002 : Step(123): len = 55260, overlap = 57.25
PHY-3002 : Step(124): len = 54592, overlap = 57.75
PHY-3002 : Step(125): len = 54134.1, overlap = 56.5
PHY-3002 : Step(126): len = 53878.9, overlap = 56.75
PHY-3002 : Step(127): len = 53884.2, overlap = 56
PHY-3002 : Step(128): len = 53505.6, overlap = 58
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.68218e-05
PHY-3002 : Step(129): len = 53587.6, overlap = 57.75
PHY-3002 : Step(130): len = 54168.3, overlap = 57.25
PHY-3002 : Step(131): len = 54562, overlap = 56.75
PHY-3002 : Step(132): len = 54567.4, overlap = 56.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000153644
PHY-3002 : Step(133): len = 54814, overlap = 54.5
PHY-3002 : Step(134): len = 55674.3, overlap = 52.5
PHY-3001 : Before Legalized: Len = 55674.3
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.150312s wall, 0.093750s user + 0.250000s system = 0.343750s CPU (228.7%)

PHY-3001 : After Legalized: Len = 68971.8, Over = 0
PHY-3001 : Trial Legalized: Len = 68971.8
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1921 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.080136s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (78.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00125719
PHY-3002 : Step(135): len = 65111.3, overlap = 9.25
PHY-3002 : Step(136): len = 63564.8, overlap = 16.75
PHY-3002 : Step(137): len = 61777.1, overlap = 19.5
PHY-3002 : Step(138): len = 60662.9, overlap = 22
PHY-3002 : Step(139): len = 60054, overlap = 26
PHY-3002 : Step(140): len = 59446.2, overlap = 28.75
PHY-3002 : Step(141): len = 59017.7, overlap = 29.5
PHY-3002 : Step(142): len = 58646.1, overlap = 29
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00251437
PHY-3002 : Step(143): len = 58927, overlap = 29
PHY-3002 : Step(144): len = 59022.4, overlap = 28.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00502875
PHY-3002 : Step(145): len = 59076.2, overlap = 27.75
PHY-3002 : Step(146): len = 59105.7, overlap = 26.5
PHY-3001 : Before Legalized: Len = 59105.7
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007987s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (195.6%)

PHY-3001 : After Legalized: Len = 63771.3, Over = 0
PHY-3001 : Legalized: Len = 63771.3, Over = 0
PHY-3001 : Spreading special nets. 6 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.008735s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (178.9%)

PHY-3001 : 8 instances has been re-located, deltaX = 1, deltaY = 7, maxDist = 1.
PHY-3001 : Final: Len = 63759.3, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6288, tnet num: 1921, tinst num: 804, tnode num: 8582, tedge num: 11106.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 56/1923.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70528, over cnt = 165(0%), over = 239, worst = 6
PHY-1002 : len = 71480, over cnt = 105(0%), over = 134, worst = 3
PHY-1002 : len = 72896, over cnt = 10(0%), over = 13, worst = 3
PHY-1002 : len = 73040, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 73136, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.148585s wall, 0.234375s user + 0.078125s system = 0.312500s CPU (210.3%)

PHY-1001 : Congestion index: top1 = 31.88, top5 = 22.87, top10 = 17.99, top15 = 14.04.
PHY-1001 : End incremental global routing;  0.229716s wall, 0.296875s user + 0.078125s system = 0.375000s CPU (163.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1921 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.094847s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (115.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.368036s wall, 0.437500s user + 0.078125s system = 0.515625s CPU (140.1%)

OPT-1001 : Current memory(MB): used = 212, reserve = 180, peak = 212.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1718/1923.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73136, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.010011s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.88, top5 = 22.87, top10 = 17.99, top15 = 14.04.
OPT-1001 : End congestion update;  0.082804s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (113.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1921 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.072233s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (86.5%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.157349s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (99.3%)

OPT-1001 : Current memory(MB): used = 215, reserve = 182, peak = 215.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1921 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.072774s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (107.4%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1718/1923.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73136, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.009211s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (169.6%)

PHY-1001 : Congestion index: top1 = 31.88, top5 = 22.87, top10 = 17.99, top15 = 14.04.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1921 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.065811s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (95.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.413793
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  1.223812s wall, 1.296875s user + 0.078125s system = 1.375000s CPU (112.4%)

RUN-1003 : finish command "place" in  6.226586s wall, 8.000000s user + 3.734375s system = 11.734375s CPU (188.5%)

RUN-1004 : used memory is 193 MB, reserved memory is 161 MB, peak memory is 215 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 806 instances
RUN-1001 : 378 mslices, 377 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1923 nets
RUN-1001 : 1359 nets have 2 pins
RUN-1001 : 460 nets have [3 - 5] pins
RUN-1001 : 62 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6288, tnet num: 1921, tinst num: 804, tnode num: 8582, tedge num: 11106.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 378 mslices, 377 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1921 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69800, over cnt = 160(0%), over = 235, worst = 6
PHY-1002 : len = 70920, over cnt = 91(0%), over = 111, worst = 3
PHY-1002 : len = 72200, over cnt = 6(0%), over = 8, worst = 2
PHY-1002 : len = 72336, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.149093s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (146.7%)

PHY-1001 : Congestion index: top1 = 31.44, top5 = 22.64, top10 = 17.78, top15 = 13.88.
PHY-1001 : End global routing;  0.216860s wall, 0.250000s user + 0.031250s system = 0.281250s CPU (129.7%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 229, reserve = 197, peak = 231.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 494, reserve = 464, peak = 494.
PHY-1001 : End build detailed router design. 3.960334s wall, 3.890625s user + 0.078125s system = 3.968750s CPU (100.2%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 30720, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.352387s wall, 1.312500s user + 0.031250s system = 1.343750s CPU (99.4%)

PHY-1001 : Current memory(MB): used = 526, reserve = 497, peak = 526.
PHY-1001 : End phase 1; 1.361530s wall, 1.312500s user + 0.031250s system = 1.343750s CPU (98.7%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 37% nets.
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 62% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 179616, over cnt = 23(0%), over = 23, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 527, reserve = 499, peak = 527.
PHY-1001 : End initial routed; 1.826909s wall, 2.718750s user + 0.281250s system = 3.000000s CPU (164.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1708(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.786   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.827   |  -43.063  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.480095s wall, 0.484375s user + 0.000000s system = 0.484375s CPU (100.9%)

PHY-1001 : Current memory(MB): used = 529, reserve = 501, peak = 529.
PHY-1001 : End phase 2; 2.307212s wall, 3.203125s user + 0.281250s system = 3.484375s CPU (151.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 179616, over cnt = 23(0%), over = 23, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.022686s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (137.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 179400, over cnt = 5(0%), over = 5, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.032624s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (143.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 179416, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.033071s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (141.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1708(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.786   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.827   |  -43.063  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.425018s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (99.3%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 5 feed throughs used by 5 nets
PHY-1001 : End commit to database; 0.185338s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (101.2%)

PHY-1001 : Current memory(MB): used = 546, reserve = 517, peak = 546.
PHY-1001 : End phase 3; 0.859620s wall, 0.875000s user + 0.000000s system = 0.875000s CPU (101.8%)

PHY-1003 : Routed, final wirelength = 179416
PHY-1001 : Current memory(MB): used = 546, reserve = 518, peak = 546.
PHY-1001 : End export database. 0.010232s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (152.7%)

PHY-1001 : End detail routing;  8.728987s wall, 9.531250s user + 0.390625s system = 9.921875s CPU (113.7%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6288, tnet num: 1921, tinst num: 804, tnode num: 8582, tedge num: 11106.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[6] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[8] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[47] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[2] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[54] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_66.dia[2] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_52.mi[0] slack -2681ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_52.mi[1] slack -2563ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_55.mi[0] slack -2669ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_55.mi[1] slack -2587ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_58.mi[0] slack -2683ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_58.mi[1] slack -2695ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_61.mi[0] slack -2669ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_61.mi[1] slack -2695ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_64.mi[0] slack -2695ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_64.mi[1] slack -2827ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_67.mi[0] slack -2695ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_67.mi[1] slack -2587ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_70.mi[0] slack -2827ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_70.mi[1] slack -2451ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_73.mi[0] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_73.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6400, tnet num: 1977, tinst num: 860, tnode num: 8694, tedge num: 11218.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_52_mi[1] slack -137ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg2_syn_55_mi[1] slack -332ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_58_mi[0] slack -404ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_55_mi[0] slack -67ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_58_mi[1] slack -394ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_64_mi[0] slack -277ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_64_mi[1] slack -121ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_61_mi[1] slack -505ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_67_mi[0] slack -540ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_70_mi[0] slack -376ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_67_mi[1] slack -127ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_70_mi[1] slack -463ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_73_mi[0] slack -166ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_73_mi[1] slack -259ps
RUN-1001 : End hold fix;  3.798072s wall, 3.984375s user + 0.203125s system = 4.187500s CPU (110.3%)

RUN-1003 : finish command "route" in  13.139263s wall, 14.156250s user + 0.625000s system = 14.781250s CPU (112.5%)

RUN-1004 : used memory is 522 MB, reserved memory is 493 MB, peak memory is 546 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      873   out of  19600    4.45%
#reg                     1049   out of  19600    5.35%
#le                      1566
  #lut only               517   out of   1566   33.01%
  #reg only               693   out of   1566   44.25%
  #lut&reg                356   out of   1566   22.73%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         480
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         101
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    41
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1566   |677     |196     |1082    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1059   |274     |133     |859     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |35     |28      |7       |24      |0       |0       |
|    demodu                  |Demodulation                                     |441    |91      |45      |351     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |54     |28      |6       |45      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |6       |0       |16      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |12     |5       |0       |12      |0       |0       |
|    integ                   |Integration                                      |142    |33      |15      |114     |0       |0       |
|    modu                    |Modulation                                       |97     |33      |14      |95      |0       |1       |
|    rs422                   |Rs422Output                                      |313    |64      |46      |258     |0       |4       |
|    trans                   |SquareWaveGenerator                              |31     |25      |6       |17      |0       |0       |
|  u_uart                    |UART_Control                                     |178    |130     |7       |116     |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |28      |7       |14      |0       |0       |
|    U1                      |uart_tx                                          |26     |18      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |117    |84      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |307    |262     |45      |73      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1376  
    #2          2       327   
    #3          3       107   
    #4          4        26   
    #5        5-10       67   
    #6        11-50      31   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6400, tnet num: 1977, tinst num: 860, tnode num: 8694, tedge num: 11218.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1977 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 860
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1979, pip num: 14544
BIT-1002 : Init feedthrough completely, num: 8
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1333 valid insts, and 38905 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.215831s wall, 18.703125s user + 0.062500s system = 18.765625s CPU (583.5%)

RUN-1004 : used memory is 519 MB, reserved memory is 492 MB, peak memory is 668 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250731_165354.log"
