============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Tue Aug  5 10:32:52 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1583 instances
RUN-0007 : 374 luts, 966 seqs, 122 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2115 nets
RUN-1001 : 1540 nets have 2 pins
RUN-1001 : 472 nets have [3 - 5] pins
RUN-1001 : 59 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     263     
RUN-1001 :   No   |  No   |  Yes  |     124     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1581 instances, 374 luts, 966 seqs, 192 slices, 19 macros(192 instances: 122 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7548, tnet num: 2113, tinst num: 1581, tnode num: 10716, tedge num: 12793.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2113 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.267819s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (99.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 529315
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1581.
PHY-3001 : End clustering;  0.000020s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 460192, overlap = 20.25
PHY-3002 : Step(2): len = 431445, overlap = 15.75
PHY-3002 : Step(3): len = 416837, overlap = 20.25
PHY-3002 : Step(4): len = 406689, overlap = 13.5
PHY-3002 : Step(5): len = 392897, overlap = 18
PHY-3002 : Step(6): len = 380429, overlap = 18
PHY-3002 : Step(7): len = 370572, overlap = 13.5
PHY-3002 : Step(8): len = 363849, overlap = 15.75
PHY-3002 : Step(9): len = 350008, overlap = 15.75
PHY-3002 : Step(10): len = 342712, overlap = 15.75
PHY-3002 : Step(11): len = 335418, overlap = 15.75
PHY-3002 : Step(12): len = 325834, overlap = 13.5
PHY-3002 : Step(13): len = 315931, overlap = 13.5
PHY-3002 : Step(14): len = 311896, overlap = 13.5
PHY-3002 : Step(15): len = 300414, overlap = 15.75
PHY-3002 : Step(16): len = 291914, overlap = 15.75
PHY-3002 : Step(17): len = 287069, overlap = 15.75
PHY-3002 : Step(18): len = 281171, overlap = 15.75
PHY-3002 : Step(19): len = 270338, overlap = 15.75
PHY-3002 : Step(20): len = 266782, overlap = 15.75
PHY-3002 : Step(21): len = 260384, overlap = 13.5
PHY-3002 : Step(22): len = 253435, overlap = 13.5
PHY-3002 : Step(23): len = 247656, overlap = 13.5
PHY-3002 : Step(24): len = 244459, overlap = 13.5
PHY-3002 : Step(25): len = 233963, overlap = 13.5
PHY-3002 : Step(26): len = 228046, overlap = 18
PHY-3002 : Step(27): len = 224490, overlap = 20.25
PHY-3002 : Step(28): len = 220212, overlap = 20.25
PHY-3002 : Step(29): len = 202846, overlap = 20.25
PHY-3002 : Step(30): len = 198797, overlap = 20.25
PHY-3002 : Step(31): len = 196426, overlap = 20.25
PHY-3002 : Step(32): len = 182626, overlap = 20.25
PHY-3002 : Step(33): len = 159894, overlap = 20.25
PHY-3002 : Step(34): len = 158170, overlap = 20.25
PHY-3002 : Step(35): len = 148896, overlap = 20.25
PHY-3002 : Step(36): len = 111319, overlap = 20.25
PHY-3002 : Step(37): len = 108022, overlap = 18
PHY-3002 : Step(38): len = 106314, overlap = 18
PHY-3002 : Step(39): len = 102582, overlap = 18
PHY-3002 : Step(40): len = 101495, overlap = 20.25
PHY-3002 : Step(41): len = 99873.5, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.31228e-05
PHY-3002 : Step(42): len = 100736, overlap = 11.25
PHY-3002 : Step(43): len = 99986.2, overlap = 13.5
PHY-3002 : Step(44): len = 98852.6, overlap = 15.75
PHY-3002 : Step(45): len = 98346.1, overlap = 13.5
PHY-3002 : Step(46): len = 96071.4, overlap = 15.75
PHY-3002 : Step(47): len = 93496.9, overlap = 15.75
PHY-3002 : Step(48): len = 92421.7, overlap = 13.5
PHY-3002 : Step(49): len = 91967.6, overlap = 13.5
PHY-3002 : Step(50): len = 89173.4, overlap = 15.75
PHY-3002 : Step(51): len = 87014.6, overlap = 11.25
PHY-3002 : Step(52): len = 85659.2, overlap = 13.5
PHY-3002 : Step(53): len = 85226.1, overlap = 13.5
PHY-3002 : Step(54): len = 83108.4, overlap = 13.5
PHY-3002 : Step(55): len = 81621.9, overlap = 15.75
PHY-3002 : Step(56): len = 79940.7, overlap = 18
PHY-3002 : Step(57): len = 78760.8, overlap = 15.75
PHY-3002 : Step(58): len = 76353.9, overlap = 18
PHY-3002 : Step(59): len = 75765.3, overlap = 18
PHY-3002 : Step(60): len = 74942.4, overlap = 18.625
PHY-3002 : Step(61): len = 73817.6, overlap = 17
PHY-3002 : Step(62): len = 72828.5, overlap = 14.875
PHY-3002 : Step(63): len = 72367.3, overlap = 15
PHY-3002 : Step(64): len = 72137, overlap = 15.125
PHY-3002 : Step(65): len = 71358.7, overlap = 15.4375
PHY-3002 : Step(66): len = 70332.3, overlap = 15.5
PHY-3002 : Step(67): len = 69147.8, overlap = 15.875
PHY-3002 : Step(68): len = 66761.6, overlap = 16.375
PHY-3002 : Step(69): len = 66085.6, overlap = 16.5
PHY-3002 : Step(70): len = 64987.3, overlap = 16.4375
PHY-3002 : Step(71): len = 64533.5, overlap = 16.3125
PHY-3002 : Step(72): len = 64321.2, overlap = 16.1875
PHY-3002 : Step(73): len = 63615.5, overlap = 13.8125
PHY-3002 : Step(74): len = 62867.5, overlap = 17.9375
PHY-3002 : Step(75): len = 62342.9, overlap = 18.1875
PHY-3002 : Step(76): len = 61231.8, overlap = 15.75
PHY-3002 : Step(77): len = 60541.3, overlap = 15.3125
PHY-3002 : Step(78): len = 59976.6, overlap = 15.25
PHY-3002 : Step(79): len = 59895.2, overlap = 15.1875
PHY-3002 : Step(80): len = 59732.3, overlap = 19.6875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000166246
PHY-3002 : Step(81): len = 59735.3, overlap = 15.1875
PHY-3002 : Step(82): len = 59551.8, overlap = 15.1875
PHY-3002 : Step(83): len = 59486.8, overlap = 15.1875
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000332491
PHY-3002 : Step(84): len = 59735.8, overlap = 15.1875
PHY-3002 : Step(85): len = 59876.5, overlap = 15.1875
PHY-3001 : Before Legalized: Len = 59876.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007014s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 63288.2, Over = 1.6875
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2113 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.067242s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (92.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(86): len = 63523, overlap = 8.4375
PHY-3002 : Step(87): len = 63363.1, overlap = 7.59375
PHY-3002 : Step(88): len = 62322.3, overlap = 7.46875
PHY-3002 : Step(89): len = 61494.6, overlap = 8.21875
PHY-3002 : Step(90): len = 60540.1, overlap = 8.84375
PHY-3002 : Step(91): len = 59186.9, overlap = 8.4375
PHY-3002 : Step(92): len = 58088.7, overlap = 8.75
PHY-3002 : Step(93): len = 57174.6, overlap = 8.28125
PHY-3002 : Step(94): len = 56789, overlap = 8.15625
PHY-3002 : Step(95): len = 56115.2, overlap = 8.09375
PHY-3002 : Step(96): len = 55680.5, overlap = 8.78125
PHY-3002 : Step(97): len = 55280.4, overlap = 13.6562
PHY-3002 : Step(98): len = 54699.2, overlap = 12.3438
PHY-3002 : Step(99): len = 54195.8, overlap = 13.2188
PHY-3002 : Step(100): len = 53324.7, overlap = 13.1875
PHY-3002 : Step(101): len = 52562.6, overlap = 13.5312
PHY-3002 : Step(102): len = 52107.9, overlap = 14.0938
PHY-3002 : Step(103): len = 51474, overlap = 14.125
PHY-3002 : Step(104): len = 51278.4, overlap = 14.5312
PHY-3002 : Step(105): len = 50989.5, overlap = 17.1562
PHY-3002 : Step(106): len = 50868.2, overlap = 16.9688
PHY-3002 : Step(107): len = 50904.7, overlap = 17.2188
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000278547
PHY-3002 : Step(108): len = 50856.1, overlap = 14.5938
PHY-3002 : Step(109): len = 50589.8, overlap = 14.4062
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000557095
PHY-3002 : Step(110): len = 50724.3, overlap = 14.2812
PHY-3002 : Step(111): len = 50833.8, overlap = 14.3125
PHY-3002 : Step(112): len = 50882.8, overlap = 14
PHY-3002 : Step(113): len = 50865.6, overlap = 14.125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2113 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.059720s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00011125
PHY-3002 : Step(114): len = 51077.7, overlap = 52.9688
PHY-3002 : Step(115): len = 52473.7, overlap = 44.2812
PHY-3002 : Step(116): len = 52673.5, overlap = 43.6562
PHY-3002 : Step(117): len = 52024.2, overlap = 44.8125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0002225
PHY-3002 : Step(118): len = 52057.2, overlap = 45.125
PHY-3002 : Step(119): len = 53217.2, overlap = 41.0625
PHY-3002 : Step(120): len = 53874.7, overlap = 39.4688
PHY-3002 : Step(121): len = 53769.3, overlap = 37.5312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000445001
PHY-3002 : Step(122): len = 53662.3, overlap = 37
PHY-3002 : Step(123): len = 53442.3, overlap = 35.875
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7548, tnet num: 2113, tinst num: 1581, tnode num: 10716, tedge num: 12793.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 82.09 peak overflow 2.50
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2115.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55968, over cnt = 220(0%), over = 952, worst = 22
PHY-1001 : End global iterations;  0.071476s wall, 0.093750s user + 0.031250s system = 0.125000s CPU (174.9%)

PHY-1001 : Congestion index: top1 = 44.59, top5 = 25.13, top10 = 15.83, top15 = 11.25.
PHY-1001 : End incremental global routing;  0.122061s wall, 0.125000s user + 0.046875s system = 0.171875s CPU (140.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2113 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067441s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (92.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.218352s wall, 0.218750s user + 0.046875s system = 0.265625s CPU (121.6%)

OPT-1001 : Current memory(MB): used = 209, reserve = 176, peak = 209.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1571/2115.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55968, over cnt = 220(0%), over = 952, worst = 22
PHY-1002 : len = 63400, over cnt = 154(0%), over = 286, worst = 8
PHY-1002 : len = 66168, over cnt = 51(0%), over = 54, worst = 3
PHY-1002 : len = 67000, over cnt = 10(0%), over = 10, worst = 1
PHY-1002 : len = 67680, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.089305s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (140.0%)

PHY-1001 : Congestion index: top1 = 37.80, top5 = 24.80, top10 = 17.40, top15 = 12.83.
OPT-1001 : End congestion update;  0.133325s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (128.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2113 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061974s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.8%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.198638s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (125.9%)

OPT-1001 : Current memory(MB): used = 212, reserve = 179, peak = 212.
OPT-1001 : End physical optimization;  0.662404s wall, 0.718750s user + 0.046875s system = 0.765625s CPU (115.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 374 LUT to BLE ...
SYN-4008 : Packed 374 LUT and 191 SEQ to BLE.
SYN-4003 : Packing 775 remaining SEQ's ...
SYN-4005 : Packed 79 SEQ with LUT/SLICE
SYN-4006 : 120 single LUT's are left
SYN-4006 : 696 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1070/1377 primitive instances ...
PHY-3001 : End packing;  0.050888s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.1%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 809 instances
RUN-1001 : 379 mslices, 379 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1932 nets
RUN-1001 : 1360 nets have 2 pins
RUN-1001 : 470 nets have [3 - 5] pins
RUN-1001 : 58 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 807 instances, 758 slices, 19 macros(192 instances: 122 mslices 70 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 53643, Over = 58.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6330, tnet num: 1930, tinst num: 807, tnode num: 8630, tedge num: 11184.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1930 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.294160s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (95.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.29211e-05
PHY-3002 : Step(124): len = 53222.2, overlap = 59.25
PHY-3002 : Step(125): len = 52661, overlap = 60
PHY-3002 : Step(126): len = 52202.7, overlap = 60
PHY-3002 : Step(127): len = 52218, overlap = 60.5
PHY-3002 : Step(128): len = 52082.5, overlap = 61.5
PHY-3002 : Step(129): len = 51896.4, overlap = 57.5
PHY-3002 : Step(130): len = 51618.5, overlap = 57.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.58423e-05
PHY-3002 : Step(131): len = 51853.5, overlap = 59.25
PHY-3002 : Step(132): len = 52113, overlap = 58.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000113531
PHY-3002 : Step(133): len = 52978.1, overlap = 57
PHY-3002 : Step(134): len = 53481.2, overlap = 56
PHY-3002 : Step(135): len = 54433.5, overlap = 52.5
PHY-3001 : Before Legalized: Len = 54433.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.090824s wall, 0.046875s user + 0.171875s system = 0.218750s CPU (240.9%)

PHY-3001 : After Legalized: Len = 67661.9, Over = 0
PHY-3001 : Trial Legalized: Len = 67661.9
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1930 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.059124s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (79.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000668389
PHY-3002 : Step(136): len = 62885.1, overlap = 13.25
PHY-3002 : Step(137): len = 61827.8, overlap = 15
PHY-3002 : Step(138): len = 60261, overlap = 17.75
PHY-3002 : Step(139): len = 58899.2, overlap = 23.75
PHY-3002 : Step(140): len = 58375.3, overlap = 23.25
PHY-3002 : Step(141): len = 58122.1, overlap = 23.75
PHY-3002 : Step(142): len = 57756.9, overlap = 24.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00133678
PHY-3002 : Step(143): len = 58156.1, overlap = 23.75
PHY-3002 : Step(144): len = 58242, overlap = 23.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00267356
PHY-3002 : Step(145): len = 58393.2, overlap = 22.5
PHY-3002 : Step(146): len = 58501.8, overlap = 22
PHY-3001 : Before Legalized: Len = 58501.8
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005430s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (287.7%)

PHY-3001 : After Legalized: Len = 63139.3, Over = 0
PHY-3001 : Legalized: Len = 63139.3, Over = 0
PHY-3001 : Spreading special nets. 5 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006188s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 9 instances has been re-located, deltaX = 0, deltaY = 11, maxDist = 2.
PHY-3001 : Final: Len = 63327.3, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6330, tnet num: 1930, tinst num: 807, tnode num: 8630, tedge num: 11184.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 67/1932.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70264, over cnt = 160(0%), over = 243, worst = 6
PHY-1002 : len = 71336, over cnt = 73(0%), over = 98, worst = 6
PHY-1002 : len = 72488, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 72552, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 72632, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.127565s wall, 0.171875s user + 0.031250s system = 0.203125s CPU (159.2%)

PHY-1001 : Congestion index: top1 = 32.56, top5 = 23.40, top10 = 18.02, top15 = 14.09.
PHY-1001 : End incremental global routing;  0.181997s wall, 0.218750s user + 0.031250s system = 0.250000s CPU (137.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1930 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060967s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.272668s wall, 0.312500s user + 0.031250s system = 0.343750s CPU (126.1%)

OPT-1001 : Current memory(MB): used = 215, reserve = 182, peak = 215.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1718/1932.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72632, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005871s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.56, top5 = 23.40, top10 = 18.02, top15 = 14.09.
OPT-1001 : End congestion update;  0.055580s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (112.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1930 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050507s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.8%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 767 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 807 instances, 758 slices, 19 macros(192 instances: 122 mslices 70 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 63336, Over = 0
PHY-3001 : End spreading;  0.005276s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 63336, Over = 0
PHY-3001 : End incremental legalization;  0.037556s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (83.2%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.157089s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (169.1%)

OPT-1001 : Current memory(MB): used = 220, reserve = 187, peak = 220.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1930 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050233s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.3%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1714/1932.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72656, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008465s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (184.6%)

PHY-1001 : Congestion index: top1 = 32.56, top5 = 23.41, top10 = 18.02, top15 = 14.09.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1930 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051859s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (90.4%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 32.241379
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.869237s wall, 1.015625s user + 0.046875s system = 1.062500s CPU (122.2%)

RUN-1003 : finish command "place" in  5.000341s wall, 7.046875s user + 3.140625s system = 10.187500s CPU (203.7%)

RUN-1004 : used memory is 197 MB, reserved memory is 165 MB, peak memory is 220 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 809 instances
RUN-1001 : 379 mslices, 379 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1932 nets
RUN-1001 : 1360 nets have 2 pins
RUN-1001 : 470 nets have [3 - 5] pins
RUN-1001 : 58 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6330, tnet num: 1930, tinst num: 807, tnode num: 8630, tedge num: 11184.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 379 mslices, 379 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1930 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69448, over cnt = 159(0%), over = 248, worst = 6
PHY-1002 : len = 70520, over cnt = 90(0%), over = 120, worst = 3
PHY-1002 : len = 71944, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 72040, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72088, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.123479s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (164.5%)

PHY-1001 : Congestion index: top1 = 32.33, top5 = 23.06, top10 = 17.88, top15 = 13.94.
PHY-1001 : End global routing;  0.175623s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (142.4%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 232, reserve = 201, peak = 235.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 494, reserve = 465, peak = 494.
PHY-1001 : End build detailed router design. 3.223162s wall, 3.156250s user + 0.062500s system = 3.218750s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 30776, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.098601s wall, 1.078125s user + 0.000000s system = 1.078125s CPU (98.1%)

PHY-1001 : Current memory(MB): used = 526, reserve = 498, peak = 526.
PHY-1001 : End phase 1; 1.105631s wall, 1.078125s user + 0.000000s system = 1.078125s CPU (97.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 30% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 178792, over cnt = 25(0%), over = 25, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 528, reserve = 499, peak = 529.
PHY-1001 : End initial routed; 1.551362s wall, 2.500000s user + 0.250000s system = 2.750000s CPU (177.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1720(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -47.780  |  21   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.339672s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (96.6%)

PHY-1001 : Current memory(MB): used = 531, reserve = 503, peak = 531.
PHY-1001 : End phase 2; 1.891129s wall, 2.828125s user + 0.250000s system = 3.078125s CPU (162.8%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 178792, over cnt = 25(0%), over = 25, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014994s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (104.2%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 178616, over cnt = 8(0%), over = 8, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.027496s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (113.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 178632, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.022201s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (70.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1720(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -47.780  |  21   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.363991s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (98.7%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.166130s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (103.5%)

PHY-1001 : Current memory(MB): used = 546, reserve = 518, peak = 546.
PHY-1001 : End phase 3; 0.726275s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (99.0%)

PHY-1003 : Routed, final wirelength = 178632
PHY-1001 : Current memory(MB): used = 547, reserve = 519, peak = 547.
PHY-1001 : End export database. 0.010152s wall, 0.000000s user + 0.015625s system = 0.015625s CPU (153.9%)

PHY-1001 : End detail routing;  7.136769s wall, 7.968750s user + 0.328125s system = 8.296875s CPU (116.3%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6330, tnet num: 1930, tinst num: 807, tnode num: 8630, tedge num: 11184.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[3] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[0] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[47] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[2] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[52] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[7] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[53] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[8] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[54] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_66.dia[2] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[0] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[13] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[4] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[16] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[17] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[8] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_52.mi[0] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_52.mi[1] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_55.mi[0] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_55.mi[1] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_58.mi[0] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_58.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_61.mi[0] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_61.mi[1] slack -2679ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_64.mi[0] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_64.mi[1] slack -2909ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_67.mi[0] slack -2897ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_67.mi[1] slack -2897ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_70.mi[0] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_70.mi[1] slack -2897ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_73.mi[0] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_73.mi[1] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6454, tnet num: 1992, tinst num: 869, tnode num: 8754, tedge num: 11308.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_64_mi[0] slack -579ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_64_mi[1] slack -627ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_73_mi[1] slack -723ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_73_mi[0] slack -831ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_70_mi[0] slack -586ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_58_mi[1] slack -980ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_61_mi[1] slack -435ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_58_mi[0] slack -808ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_61_mi[0] slack -818ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_67_mi[1] slack -613ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg2_syn_67_mi[0] slack -1246ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_52_mi[1] slack -901ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_55_mi[1] slack -630ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_55_mi[0] slack -597ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_52_mi[0] slack -717ps
RUN-1001 : End hold fix;  3.177111s wall, 3.187500s user + 0.328125s system = 3.515625s CPU (110.7%)

RUN-1003 : finish command "route" in  10.815124s wall, 11.718750s user + 0.671875s system = 12.390625s CPU (114.6%)

RUN-1004 : used memory is 521 MB, reserved memory is 493 MB, peak memory is 547 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      888   out of  19600    4.53%
#reg                     1052   out of  19600    5.37%
#le                      1584
  #lut only               532   out of   1584   33.59%
  #reg only               696   out of   1584   43.94%
  #lut&reg                356   out of   1584   22.47%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                      Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0       479
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3       101
#3        wendu/clk_us                    GCLK               mslice             wendu/cnt_b[2]_syn_10.q0    42
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4       1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di             1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1584   |696     |192     |1085    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1069   |289     |128     |861     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |28     |24      |4       |20      |0       |0       |
|    demodu                  |Demodulation                                     |452    |103     |41      |352     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |56     |29      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |6       |0       |16      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |13     |6       |0       |13      |0       |0       |
|    integ                   |Integration                                      |139    |21      |15      |111     |0       |0       |
|    modu                    |Modulation                                       |102    |44      |14      |100     |0       |1       |
|    rs422                   |Rs422Output                                      |314    |71      |46      |259     |0       |4       |
|    trans                   |SquareWaveGenerator                              |34     |26      |8       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |183    |132     |7       |118     |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |25     |18      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |121    |86      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |308    |263     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1383  
    #2          2       330   
    #3          3       109   
    #4          4        31   
    #5        5-10       64   
    #6        11-50      32   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6454, tnet num: 1992, tinst num: 869, tnode num: 8754, tedge num: 11308.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1992 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 869
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1994, pip num: 14641
BIT-1002 : Init feedthrough completely, num: 7
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1291 valid insts, and 39134 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.154155s wall, 18.093750s user + 0.078125s system = 18.171875s CPU (576.1%)

RUN-1004 : used memory is 517 MB, reserved memory is 490 MB, peak memory is 667 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250805_103252.log"
