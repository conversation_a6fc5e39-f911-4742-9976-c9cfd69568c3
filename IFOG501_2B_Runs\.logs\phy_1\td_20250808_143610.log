============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Fri Aug  8 14:36:10 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1550 instances
RUN-0007 : 371 luts, 936 seqs, 122 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2080 nets
RUN-1001 : 1534 nets have 2 pins
RUN-1001 : 443 nets have [3 - 5] pins
RUN-1001 : 61 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     232     
RUN-1001 :   No   |  No   |  Yes  |     125     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1548 instances, 371 luts, 936 seqs, 192 slices, 20 macros(192 instances: 122 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7410, tnet num: 2078, tinst num: 1548, tnode num: 10508, tedge num: 12557.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2078 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.245893s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (101.7%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 516070
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1548.
PHY-3001 : End clustering;  0.000024s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 452171, overlap = 18
PHY-3002 : Step(2): len = 418796, overlap = 13.5
PHY-3002 : Step(3): len = 408094, overlap = 18
PHY-3002 : Step(4): len = 395456, overlap = 9
PHY-3002 : Step(5): len = 382792, overlap = 11.25
PHY-3002 : Step(6): len = 364289, overlap = 11.25
PHY-3002 : Step(7): len = 358054, overlap = 15.75
PHY-3002 : Step(8): len = 350136, overlap = 18
PHY-3002 : Step(9): len = 340625, overlap = 15.75
PHY-3002 : Step(10): len = 333459, overlap = 15.75
PHY-3002 : Step(11): len = 328296, overlap = 15.75
PHY-3002 : Step(12): len = 315416, overlap = 13.5
PHY-3002 : Step(13): len = 310332, overlap = 13.5
PHY-3002 : Step(14): len = 303716, overlap = 13.5
PHY-3002 : Step(15): len = 292606, overlap = 15.75
PHY-3002 : Step(16): len = 285967, overlap = 15.75
PHY-3002 : Step(17): len = 282642, overlap = 15.75
PHY-3002 : Step(18): len = 271471, overlap = 15.75
PHY-3002 : Step(19): len = 265110, overlap = 15.75
PHY-3002 : Step(20): len = 260771, overlap = 15.75
PHY-3002 : Step(21): len = 255292, overlap = 13.5
PHY-3002 : Step(22): len = 243965, overlap = 20.25
PHY-3002 : Step(23): len = 240772, overlap = 20.25
PHY-3002 : Step(24): len = 236189, overlap = 20.25
PHY-3002 : Step(25): len = 223499, overlap = 20.25
PHY-3002 : Step(26): len = 214954, overlap = 20.25
PHY-3002 : Step(27): len = 213576, overlap = 20.25
PHY-3002 : Step(28): len = 189842, overlap = 18
PHY-3002 : Step(29): len = 155046, overlap = 18
PHY-3002 : Step(30): len = 151221, overlap = 18
PHY-3002 : Step(31): len = 148944, overlap = 18
PHY-3002 : Step(32): len = 133684, overlap = 15.75
PHY-3002 : Step(33): len = 130842, overlap = 18
PHY-3002 : Step(34): len = 129160, overlap = 20.25
PHY-3002 : Step(35): len = 126958, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000108126
PHY-3002 : Step(36): len = 127487, overlap = 11.25
PHY-3002 : Step(37): len = 126039, overlap = 11.25
PHY-3002 : Step(38): len = 125204, overlap = 11.25
PHY-3002 : Step(39): len = 123708, overlap = 11.25
PHY-3002 : Step(40): len = 120102, overlap = 11.25
PHY-3002 : Step(41): len = 117452, overlap = 11.25
PHY-3002 : Step(42): len = 116144, overlap = 9
PHY-3002 : Step(43): len = 113826, overlap = 9
PHY-3002 : Step(44): len = 107275, overlap = 13.5
PHY-3002 : Step(45): len = 105353, overlap = 13.5
PHY-3002 : Step(46): len = 104121, overlap = 11.25
PHY-3002 : Step(47): len = 102279, overlap = 15.75
PHY-3002 : Step(48): len = 101555, overlap = 13.5
PHY-3002 : Step(49): len = 99946.6, overlap = 15.75
PHY-3002 : Step(50): len = 96035.5, overlap = 13.5
PHY-3002 : Step(51): len = 94198.6, overlap = 13.5
PHY-3002 : Step(52): len = 93073.2, overlap = 13.5
PHY-3002 : Step(53): len = 89883.5, overlap = 18
PHY-3002 : Step(54): len = 87164.1, overlap = 15.75
PHY-3002 : Step(55): len = 84671, overlap = 18
PHY-3002 : Step(56): len = 83759.9, overlap = 15.75
PHY-3002 : Step(57): len = 81886.3, overlap = 15.75
PHY-3002 : Step(58): len = 80325.9, overlap = 15.75
PHY-3002 : Step(59): len = 80013, overlap = 15.75
PHY-3002 : Step(60): len = 78440.8, overlap = 18
PHY-3002 : Step(61): len = 77472.8, overlap = 18
PHY-3002 : Step(62): len = 76399.8, overlap = 18
PHY-3002 : Step(63): len = 74422.8, overlap = 15.75
PHY-3002 : Step(64): len = 71831, overlap = 15.9375
PHY-3002 : Step(65): len = 70519.3, overlap = 17
PHY-3002 : Step(66): len = 70202.9, overlap = 16.875
PHY-3002 : Step(67): len = 69371.9, overlap = 19.875
PHY-3002 : Step(68): len = 68992, overlap = 19.9375
PHY-3002 : Step(69): len = 67324.3, overlap = 18
PHY-3002 : Step(70): len = 66091.6, overlap = 16.3125
PHY-3002 : Step(71): len = 64707.8, overlap = 18.75
PHY-3002 : Step(72): len = 64795.4, overlap = 18.75
PHY-3002 : Step(73): len = 64293.8, overlap = 16.5
PHY-3002 : Step(74): len = 63510.3, overlap = 16.5
PHY-3002 : Step(75): len = 63531.3, overlap = 16.6875
PHY-3002 : Step(76): len = 62494.6, overlap = 16.9375
PHY-3002 : Step(77): len = 60859.2, overlap = 16.9375
PHY-3002 : Step(78): len = 60652.7, overlap = 17
PHY-3002 : Step(79): len = 60023, overlap = 19.25
PHY-3002 : Step(80): len = 59851.1, overlap = 19.25
PHY-3002 : Step(81): len = 59867.6, overlap = 17.0625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000216252
PHY-3002 : Step(82): len = 60018.6, overlap = 17.0625
PHY-3002 : Step(83): len = 59914.1, overlap = 17.0625
PHY-3002 : Step(84): len = 59571.6, overlap = 17.0625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000432505
PHY-3002 : Step(85): len = 59955.3, overlap = 16.9375
PHY-3002 : Step(86): len = 59986.5, overlap = 16.75
PHY-3002 : Step(87): len = 59997.1, overlap = 19
PHY-3001 : Before Legalized: Len = 59997.1
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006376s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 63355.3, Over = 3.25
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2078 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.058451s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (106.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(88): len = 63264.5, overlap = 11.1875
PHY-3002 : Step(89): len = 63872, overlap = 11.625
PHY-3002 : Step(90): len = 62336.1, overlap = 11.8125
PHY-3002 : Step(91): len = 61952.2, overlap = 11.5625
PHY-3002 : Step(92): len = 60994.2, overlap = 12.5
PHY-3002 : Step(93): len = 60243.8, overlap = 11.4688
PHY-3002 : Step(94): len = 59874.4, overlap = 10.6562
PHY-3002 : Step(95): len = 59419.2, overlap = 10.6562
PHY-3002 : Step(96): len = 58246.6, overlap = 10.1875
PHY-3002 : Step(97): len = 57284, overlap = 11
PHY-3002 : Step(98): len = 56181.5, overlap = 10.625
PHY-3002 : Step(99): len = 55796.1, overlap = 10.5
PHY-3002 : Step(100): len = 55409.5, overlap = 15.5938
PHY-3002 : Step(101): len = 55132.5, overlap = 16.9062
PHY-3002 : Step(102): len = 54920.5, overlap = 17.4375
PHY-3002 : Step(103): len = 54542.9, overlap = 16.9375
PHY-3002 : Step(104): len = 54126.9, overlap = 16.5625
PHY-3002 : Step(105): len = 53907.8, overlap = 16.7188
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00330221
PHY-3002 : Step(106): len = 53700, overlap = 16.7188
PHY-3002 : Step(107): len = 53747.5, overlap = 16.7812
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00660443
PHY-3002 : Step(108): len = 53614.2, overlap = 16.7812
PHY-3002 : Step(109): len = 53614.2, overlap = 16.7812
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2078 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.058086s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (107.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.65066e-05
PHY-3002 : Step(110): len = 54412.6, overlap = 51
PHY-3002 : Step(111): len = 54890.1, overlap = 46.75
PHY-3002 : Step(112): len = 55329.4, overlap = 45.7812
PHY-3002 : Step(113): len = 55325.6, overlap = 46.4688
PHY-3002 : Step(114): len = 55072.6, overlap = 47.0938
PHY-3002 : Step(115): len = 54612.2, overlap = 47.125
PHY-3002 : Step(116): len = 54400.1, overlap = 42.5
PHY-3002 : Step(117): len = 54348.4, overlap = 43.5625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000193013
PHY-3002 : Step(118): len = 54299, overlap = 40.0312
PHY-3002 : Step(119): len = 54331.9, overlap = 37.0938
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000386026
PHY-3002 : Step(120): len = 54725.1, overlap = 33.8438
PHY-3002 : Step(121): len = 55125.2, overlap = 33.875
PHY-3002 : Step(122): len = 55894.8, overlap = 30.875
PHY-3002 : Step(123): len = 55886.5, overlap = 30.2188
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7410, tnet num: 2078, tinst num: 1548, tnode num: 10508, tedge num: 12557.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 89.09 peak overflow 2.62
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2080.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58456, over cnt = 232(0%), over = 1026, worst = 17
PHY-1001 : End global iterations;  0.058340s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (133.9%)

PHY-1001 : Congestion index: top1 = 43.62, top5 = 25.24, top10 = 16.19, top15 = 11.51.
PHY-1001 : End incremental global routing;  0.108182s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (115.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2078 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.064858s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (96.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.200883s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (108.9%)

OPT-1001 : Current memory(MB): used = 209, reserve = 176, peak = 209.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1568/2080.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58456, over cnt = 232(0%), over = 1026, worst = 17
PHY-1002 : len = 63792, over cnt = 194(0%), over = 539, worst = 14
PHY-1002 : len = 70208, over cnt = 36(0%), over = 48, worst = 4
PHY-1002 : len = 70416, over cnt = 11(0%), over = 13, worst = 3
PHY-1002 : len = 71072, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.097667s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (96.0%)

PHY-1001 : Congestion index: top1 = 36.49, top5 = 24.36, top10 = 17.75, top15 = 13.26.
OPT-1001 : End congestion update;  0.139820s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (89.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2078 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056051s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (111.5%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.199339s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (94.1%)

OPT-1001 : Current memory(MB): used = 212, reserve = 179, peak = 212.
OPT-1001 : End physical optimization;  0.649008s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (113.2%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 371 LUT to BLE ...
SYN-4008 : Packed 371 LUT and 190 SEQ to BLE.
SYN-4003 : Packing 746 remaining SEQ's ...
SYN-4005 : Packed 86 SEQ with LUT/SLICE
SYN-4006 : 109 single LUT's are left
SYN-4006 : 660 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1031/1338 primitive instances ...
PHY-3001 : End packing;  0.058348s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (107.1%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 784 instances
RUN-1001 : 366 mslices, 367 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1898 nets
RUN-1001 : 1358 nets have 2 pins
RUN-1001 : 437 nets have [3 - 5] pins
RUN-1001 : 61 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 782 instances, 733 slices, 20 macros(192 instances: 122 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 55758.4, Over = 49.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6189, tnet num: 1896, tinst num: 782, tnode num: 8422, tedge num: 10929.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1896 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.268360s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (99.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.73142e-05
PHY-3002 : Step(124): len = 54943.5, overlap = 49
PHY-3002 : Step(125): len = 54508.6, overlap = 50.5
PHY-3002 : Step(126): len = 54598.6, overlap = 50.5
PHY-3002 : Step(127): len = 54708.9, overlap = 50.5
PHY-3002 : Step(128): len = 54496.1, overlap = 49.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.46283e-05
PHY-3002 : Step(129): len = 54758.9, overlap = 49.25
PHY-3002 : Step(130): len = 55381.4, overlap = 47.25
PHY-3002 : Step(131): len = 55686, overlap = 45
PHY-3002 : Step(132): len = 55663.6, overlap = 43.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000149257
PHY-3002 : Step(133): len = 56051, overlap = 41.75
PHY-3002 : Step(134): len = 56804.7, overlap = 40.75
PHY-3001 : Before Legalized: Len = 56804.7
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.092215s wall, 0.062500s user + 0.140625s system = 0.203125s CPU (220.3%)

PHY-3001 : After Legalized: Len = 69501.2, Over = 0
PHY-3001 : Trial Legalized: Len = 69501.2
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1896 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.046028s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (101.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00133769
PHY-3002 : Step(135): len = 65404.3, overlap = 5
PHY-3002 : Step(136): len = 63995.7, overlap = 9.25
PHY-3002 : Step(137): len = 62123.3, overlap = 11.5
PHY-3002 : Step(138): len = 61013.3, overlap = 14.5
PHY-3002 : Step(139): len = 60295.6, overlap = 15.75
PHY-3002 : Step(140): len = 59888.6, overlap = 18.5
PHY-3002 : Step(141): len = 59459.9, overlap = 19.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00267539
PHY-3002 : Step(142): len = 59587.7, overlap = 19.5
PHY-3002 : Step(143): len = 59577, overlap = 20.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00535078
PHY-3002 : Step(144): len = 59665.6, overlap = 19.25
PHY-3002 : Step(145): len = 59683.3, overlap = 18.75
PHY-3001 : Before Legalized: Len = 59683.3
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004976s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 64151.5, Over = 0
PHY-3001 : Legalized: Len = 64151.5, Over = 0
PHY-3001 : Spreading special nets. 11 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005326s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 18 instances has been re-located, deltaX = 0, deltaY = 21, maxDist = 2.
PHY-3001 : Final: Len = 64339.5, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6189, tnet num: 1896, tinst num: 782, tnode num: 8422, tedge num: 10929.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 64/1898.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70672, over cnt = 138(0%), over = 234, worst = 7
PHY-1002 : len = 71888, over cnt = 82(0%), over = 103, worst = 3
PHY-1002 : len = 72904, over cnt = 17(0%), over = 21, worst = 2
PHY-1002 : len = 73096, over cnt = 6(0%), over = 7, worst = 2
PHY-1002 : len = 73256, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.118736s wall, 0.109375s user + 0.031250s system = 0.140625s CPU (118.4%)

PHY-1001 : Congestion index: top1 = 31.23, top5 = 22.85, top10 = 17.65, top15 = 13.73.
PHY-1001 : End incremental global routing;  0.171426s wall, 0.171875s user + 0.031250s system = 0.203125s CPU (118.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1896 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053826s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (116.1%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.252408s wall, 0.250000s user + 0.031250s system = 0.281250s CPU (111.4%)

OPT-1001 : Current memory(MB): used = 213, reserve = 180, peak = 214.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1679/1898.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73256, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006241s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (250.4%)

PHY-1001 : Congestion index: top1 = 31.23, top5 = 22.85, top10 = 17.65, top15 = 13.73.
OPT-1001 : End congestion update;  0.052246s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (119.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1896 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046595s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.6%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 742 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 782 instances, 733 slices, 20 macros(192 instances: 122 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 64347.4, Over = 0
PHY-3001 : End spreading;  0.004812s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 64347.4, Over = 0
PHY-3001 : End incremental legalization;  0.034194s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (91.4%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.145792s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (107.2%)

OPT-1001 : Current memory(MB): used = 217, reserve = 185, peak = 218.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1896 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.043256s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (108.4%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1675/1898.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73248, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007723s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (202.3%)

PHY-1001 : Congestion index: top1 = 31.25, top5 = 22.87, top10 = 17.67, top15 = 13.73.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1896 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.044995s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (104.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.827586
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.801305s wall, 0.796875s user + 0.031250s system = 0.828125s CPU (103.3%)

RUN-1003 : finish command "place" in  4.835938s wall, 6.656250s user + 2.875000s system = 9.531250s CPU (197.1%)

RUN-1004 : used memory is 193 MB, reserved memory is 159 MB, peak memory is 218 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 784 instances
RUN-1001 : 366 mslices, 367 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1898 nets
RUN-1001 : 1358 nets have 2 pins
RUN-1001 : 437 nets have [3 - 5] pins
RUN-1001 : 61 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6189, tnet num: 1896, tinst num: 782, tnode num: 8422, tedge num: 10929.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 366 mslices, 367 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1896 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69592, over cnt = 153(0%), over = 247, worst = 7
PHY-1002 : len = 70792, over cnt = 90(0%), over = 111, worst = 4
PHY-1002 : len = 72120, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72136, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.130567s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (119.7%)

PHY-1001 : Congestion index: top1 = 31.29, top5 = 22.66, top10 = 17.46, top15 = 13.61.
PHY-1001 : End global routing;  0.179388s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (113.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 233, reserve = 201, peak = 233.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 492, reserve = 464, peak = 492.
PHY-1001 : End build detailed router design. 3.073478s wall, 3.000000s user + 0.078125s system = 3.078125s CPU (100.2%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 30320, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.018163s wall, 1.015625s user + 0.000000s system = 1.015625s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 524, reserve = 498, peak = 524.
PHY-1001 : End phase 1; 1.024015s wall, 1.015625s user + 0.000000s system = 1.015625s CPU (99.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 177608, over cnt = 33(0%), over = 33, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 527, reserve = 499, peak = 527.
PHY-1001 : End initial routed; 1.667493s wall, 2.250000s user + 0.140625s system = 2.390625s CPU (143.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1687(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.947   |  -43.979  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.326943s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (100.4%)

PHY-1001 : Current memory(MB): used = 528, reserve = 499, peak = 528.
PHY-1001 : End phase 2; 1.994531s wall, 2.578125s user + 0.140625s system = 2.718750s CPU (136.3%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 177608, over cnt = 33(0%), over = 33, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.013395s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (116.6%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 177240, over cnt = 13(0%), over = 13, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.033664s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (92.8%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 177248, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.021970s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (71.1%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 177288, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.021951s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (142.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1687(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.947   |  -43.979  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.318725s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (98.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 5 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.159160s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (98.2%)

PHY-1001 : Current memory(MB): used = 542, reserve = 513, peak = 542.
PHY-1001 : End phase 3; 0.697054s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (98.6%)

PHY-1003 : Routed, final wirelength = 177288
PHY-1001 : Current memory(MB): used = 542, reserve = 514, peak = 542.
PHY-1001 : End export database. 0.009979s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  6.970773s wall, 7.468750s user + 0.218750s system = 7.687500s CPU (110.3%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6189, tnet num: 1896, tinst num: 782, tnode num: 8422, tedge num: 10929.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[41] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[5] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[7] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[1] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[4] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[6] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[53] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[8] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[0] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[0] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[2] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[3] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[4] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[4] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[0] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_68.mi[0] slack -2827ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_68.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_71.mi[0] slack -2719ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_71.mi[1] slack -2947ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_74.mi[0] slack -2588ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_74.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_77.mi[0] slack -2588ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_77.mi[1] slack -2588ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_80.mi[0] slack -2583ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_80.mi[1] slack -2801ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_83.mi[0] slack -2588ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_83.mi[1] slack -2827ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_86.mi[0] slack -2588ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_86.mi[1] slack -2801ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_89.mi[0] slack -2827ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_89.mi[1] slack -2827ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6307, tnet num: 1955, tinst num: 841, tnode num: 8540, tedge num: 11047.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_71_mi[1] slack -735ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_77_mi[0] slack -304ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_77_mi[1] slack -427ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_74_mi[0] slack -330ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_80_mi[0] slack -747ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_80_mi[1] slack -516ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_83_mi[0] slack -368ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_83_mi[1] slack -423ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_86_mi[1] slack -592ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_89_mi[0] slack -606ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_86_mi[0] slack -157ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_89_mi[1] slack -412ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_68_mi[0] slack -505ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_74_mi[1] slack -753ps
RUN-1001 : End hold fix;  2.967555s wall, 3.046875s user + 0.281250s system = 3.328125s CPU (112.2%)

RUN-1003 : finish command "route" in  10.432489s wall, 11.031250s user + 0.500000s system = 11.531250s CPU (110.5%)

RUN-1004 : used memory is 530 MB, reserved memory is 504 MB, peak memory is 542 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      877   out of  19600    4.47%
#reg                     1020   out of  19600    5.20%
#le                      1537
  #lut only               517   out of   1537   33.64%
  #reg only               660   out of   1537   42.94%
  #lut&reg                360   out of   1537   23.42%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         463
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         100
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q0    39
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1537   |685     |192     |1053    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1028   |282     |128     |830     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |23     |19      |4       |20      |0       |0       |
|    demodu                  |Demodulation                                     |447    |109     |41      |353     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |58     |44      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |10      |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |17     |17      |0       |17      |0       |0       |
|    integ                   |Integration                                      |139    |17      |15      |111     |0       |0       |
|    modu                    |Modulation                                       |69     |43      |14      |65      |0       |1       |
|    rs422                   |Rs422Output                                      |321    |73      |46      |261     |0       |4       |
|    trans                   |SquareWaveGenerator                              |29     |21      |8       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |178    |129     |7       |118     |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |26     |19      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |116    |82      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |307    |262     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1378  
    #2          2       299   
    #3          3       113   
    #4          4        25   
    #5        5-10       66   
    #6        11-50      31   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6307, tnet num: 1955, tinst num: 841, tnode num: 8540, tedge num: 11047.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1955 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 841
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1957, pip num: 14415
BIT-1002 : Init feedthrough completely, num: 7
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1344 valid insts, and 38573 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.001358s wall, 17.750000s user + 0.078125s system = 17.828125s CPU (594.0%)

RUN-1004 : used memory is 547 MB, reserved memory is 516 MB, peak memory is 675 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250808_143610.log"
