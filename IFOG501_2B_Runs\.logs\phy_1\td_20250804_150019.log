============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Mon Aug  4 15:00:19 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1581 instances
RUN-0007 : 370 luts, 964 seqs, 126 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2114 nets
RUN-1001 : 1538 nets have 2 pins
RUN-1001 : 472 nets have [3 - 5] pins
RUN-1001 : 61 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     263     
RUN-1001 :   No   |  No   |  Yes  |     122     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1579 instances, 370 luts, 964 seqs, 196 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7539, tnet num: 2112, tinst num: 1579, tnode num: 10706, tedge num: 12781.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2112 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.254763s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (104.3%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 522692
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1579.
PHY-3001 : End clustering;  0.000020s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 446923, overlap = 20.25
PHY-3002 : Step(2): len = 427536, overlap = 18
PHY-3002 : Step(3): len = 413133, overlap = 18
PHY-3002 : Step(4): len = 403779, overlap = 15.75
PHY-3002 : Step(5): len = 391417, overlap = 18
PHY-3002 : Step(6): len = 381329, overlap = 13.5
PHY-3002 : Step(7): len = 371868, overlap = 13.5
PHY-3002 : Step(8): len = 363494, overlap = 13.5
PHY-3002 : Step(9): len = 354877, overlap = 15.75
PHY-3002 : Step(10): len = 346344, overlap = 15.75
PHY-3002 : Step(11): len = 338713, overlap = 13.5
PHY-3002 : Step(12): len = 331335, overlap = 13.5
PHY-3002 : Step(13): len = 324205, overlap = 13.5
PHY-3002 : Step(14): len = 316329, overlap = 13.5
PHY-3002 : Step(15): len = 309323, overlap = 15.75
PHY-3002 : Step(16): len = 302505, overlap = 13.5
PHY-3002 : Step(17): len = 295829, overlap = 13.5
PHY-3002 : Step(18): len = 290020, overlap = 13.5
PHY-3002 : Step(19): len = 284501, overlap = 13.5
PHY-3002 : Step(20): len = 278488, overlap = 13.5
PHY-3002 : Step(21): len = 272610, overlap = 13.5
PHY-3002 : Step(22): len = 267276, overlap = 13.5
PHY-3002 : Step(23): len = 261759, overlap = 13.5
PHY-3002 : Step(24): len = 255745, overlap = 13.5
PHY-3002 : Step(25): len = 251449, overlap = 13.5
PHY-3002 : Step(26): len = 243569, overlap = 20.25
PHY-3002 : Step(27): len = 238383, overlap = 20.25
PHY-3002 : Step(28): len = 234960, overlap = 20.25
PHY-3002 : Step(29): len = 228883, overlap = 20.25
PHY-3002 : Step(30): len = 216783, overlap = 20.25
PHY-3002 : Step(31): len = 212880, overlap = 20.25
PHY-3002 : Step(32): len = 210103, overlap = 20.25
PHY-3002 : Step(33): len = 179431, overlap = 20.25
PHY-3002 : Step(34): len = 168016, overlap = 20.25
PHY-3002 : Step(35): len = 166845, overlap = 20.25
PHY-3002 : Step(36): len = 155490, overlap = 20.25
PHY-3002 : Step(37): len = 132381, overlap = 20.25
PHY-3002 : Step(38): len = 128839, overlap = 20.25
PHY-3002 : Step(39): len = 127843, overlap = 20.25
PHY-3002 : Step(40): len = 124280, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000124738
PHY-3002 : Step(41): len = 124498, overlap = 15.75
PHY-3002 : Step(42): len = 123196, overlap = 15.75
PHY-3002 : Step(43): len = 122216, overlap = 15.75
PHY-3002 : Step(44): len = 121192, overlap = 9
PHY-3002 : Step(45): len = 117326, overlap = 11.25
PHY-3002 : Step(46): len = 115945, overlap = 9
PHY-3002 : Step(47): len = 113969, overlap = 9
PHY-3002 : Step(48): len = 112583, overlap = 9
PHY-3002 : Step(49): len = 107098, overlap = 11.25
PHY-3002 : Step(50): len = 105603, overlap = 9
PHY-3002 : Step(51): len = 104086, overlap = 11.25
PHY-3002 : Step(52): len = 103404, overlap = 11.25
PHY-3002 : Step(53): len = 103293, overlap = 11.25
PHY-3002 : Step(54): len = 102277, overlap = 11.25
PHY-3002 : Step(55): len = 99017.9, overlap = 15.75
PHY-3002 : Step(56): len = 94301.6, overlap = 13.5
PHY-3002 : Step(57): len = 91714.2, overlap = 15.75
PHY-3002 : Step(58): len = 92014.5, overlap = 15.75
PHY-3002 : Step(59): len = 90642.7, overlap = 15.75
PHY-3002 : Step(60): len = 89715.1, overlap = 18
PHY-3002 : Step(61): len = 87040.6, overlap = 18
PHY-3002 : Step(62): len = 84415.9, overlap = 15.75
PHY-3002 : Step(63): len = 83480.6, overlap = 15.75
PHY-3002 : Step(64): len = 81593.2, overlap = 15.75
PHY-3002 : Step(65): len = 80846.8, overlap = 13.5
PHY-3002 : Step(66): len = 78705.9, overlap = 15.75
PHY-3002 : Step(67): len = 78039.7, overlap = 18
PHY-3002 : Step(68): len = 76699.2, overlap = 18
PHY-3002 : Step(69): len = 75041.4, overlap = 15.75
PHY-3002 : Step(70): len = 74508.4, overlap = 18
PHY-3002 : Step(71): len = 73110, overlap = 15.75
PHY-3002 : Step(72): len = 71911.2, overlap = 15.75
PHY-3002 : Step(73): len = 70044, overlap = 15.875
PHY-3002 : Step(74): len = 69359.1, overlap = 15.875
PHY-3002 : Step(75): len = 68031.6, overlap = 16
PHY-3002 : Step(76): len = 67444.9, overlap = 15.875
PHY-3002 : Step(77): len = 66299.2, overlap = 18
PHY-3002 : Step(78): len = 65203.6, overlap = 18
PHY-3002 : Step(79): len = 63753.7, overlap = 15.75
PHY-3002 : Step(80): len = 62991.3, overlap = 15.75
PHY-3002 : Step(81): len = 62980.1, overlap = 15.75
PHY-3002 : Step(82): len = 62287.7, overlap = 15.75
PHY-3002 : Step(83): len = 61382.5, overlap = 18
PHY-3002 : Step(84): len = 61044.9, overlap = 18
PHY-3002 : Step(85): len = 60691.2, overlap = 15.75
PHY-3002 : Step(86): len = 60344.4, overlap = 15.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000249476
PHY-3002 : Step(87): len = 60676.9, overlap = 15.75
PHY-3002 : Step(88): len = 60656.1, overlap = 15.75
PHY-3002 : Step(89): len = 60655.1, overlap = 15.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000498953
PHY-3002 : Step(90): len = 60980.8, overlap = 13.5
PHY-3002 : Step(91): len = 61025.3, overlap = 13.5
PHY-3002 : Step(92): len = 61023.9, overlap = 13.5
PHY-3001 : Before Legalized: Len = 61023.9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007845s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 64732.7, Over = 0
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2112 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.062549s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(93): len = 64812, overlap = 2.5
PHY-3002 : Step(94): len = 64804, overlap = 1.75
PHY-3002 : Step(95): len = 63822.6, overlap = 1.9375
PHY-3002 : Step(96): len = 63556.4, overlap = 2
PHY-3002 : Step(97): len = 62296.5, overlap = 1.875
PHY-3002 : Step(98): len = 61571.8, overlap = 1.5625
PHY-3002 : Step(99): len = 61176.1, overlap = 1.75
PHY-3002 : Step(100): len = 60202.8, overlap = 3.0625
PHY-3002 : Step(101): len = 59147, overlap = 2.9375
PHY-3002 : Step(102): len = 57676.3, overlap = 3.3125
PHY-3002 : Step(103): len = 57220.6, overlap = 3.25
PHY-3002 : Step(104): len = 56931.4, overlap = 3.0625
PHY-3002 : Step(105): len = 55902, overlap = 2.875
PHY-3002 : Step(106): len = 55113.5, overlap = 5.0625
PHY-3002 : Step(107): len = 54093.4, overlap = 5.75
PHY-3002 : Step(108): len = 53956.6, overlap = 7.4375
PHY-3002 : Step(109): len = 53579.1, overlap = 9.9375
PHY-3002 : Step(110): len = 52432.2, overlap = 10.3125
PHY-3002 : Step(111): len = 52043.8, overlap = 11.1875
PHY-3002 : Step(112): len = 51993.4, overlap = 11.25
PHY-3002 : Step(113): len = 51926.5, overlap = 10.5625
PHY-3002 : Step(114): len = 51957.1, overlap = 10.75
PHY-3002 : Step(115): len = 51631.2, overlap = 11.875
PHY-3002 : Step(116): len = 51141.4, overlap = 12
PHY-3002 : Step(117): len = 50700.8, overlap = 12.7188
PHY-3002 : Step(118): len = 50368.5, overlap = 12.125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000643737
PHY-3002 : Step(119): len = 50314.1, overlap = 17.5938
PHY-3002 : Step(120): len = 50248.2, overlap = 17.4375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00128747
PHY-3002 : Step(121): len = 50124.4, overlap = 17.625
PHY-3002 : Step(122): len = 50124.4, overlap = 17.625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2112 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.074249s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (105.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.67886e-05
PHY-3002 : Step(123): len = 50785.5, overlap = 47.9062
PHY-3002 : Step(124): len = 51106, overlap = 47.4688
PHY-3002 : Step(125): len = 50908.3, overlap = 46.2188
PHY-3002 : Step(126): len = 50704.4, overlap = 46.1875
PHY-3002 : Step(127): len = 50622.3, overlap = 46.4688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000133577
PHY-3002 : Step(128): len = 50629.4, overlap = 45.375
PHY-3002 : Step(129): len = 50986.5, overlap = 44.9375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000216128
PHY-3002 : Step(130): len = 51084.8, overlap = 44.5938
PHY-3002 : Step(131): len = 52174.8, overlap = 41.2812
PHY-3002 : Step(132): len = 53749.5, overlap = 35.5
PHY-3002 : Step(133): len = 53955.3, overlap = 36.7188
PHY-3002 : Step(134): len = 53732.9, overlap = 35.9062
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000432256
PHY-3002 : Step(135): len = 53795.7, overlap = 36.3125
PHY-3002 : Step(136): len = 53917.2, overlap = 34.9688
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7539, tnet num: 2112, tinst num: 1579, tnode num: 10706, tedge num: 12781.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 100.47 peak overflow 3.28
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2114.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 57056, over cnt = 221(0%), over = 1070, worst = 24
PHY-1001 : End global iterations;  0.071268s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (153.5%)

PHY-1001 : Congestion index: top1 = 47.20, top5 = 25.55, top10 = 15.80, top15 = 11.17.
PHY-1001 : End incremental global routing;  0.122080s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (128.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2112 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.071489s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (87.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.224642s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (111.3%)

OPT-1001 : Current memory(MB): used = 208, reserve = 175, peak = 208.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1572/2114.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 57056, over cnt = 221(0%), over = 1070, worst = 24
PHY-1002 : len = 62944, over cnt = 169(0%), over = 531, worst = 19
PHY-1002 : len = 69264, over cnt = 36(0%), over = 38, worst = 2
PHY-1002 : len = 70072, over cnt = 3(0%), over = 4, worst = 2
PHY-1002 : len = 70136, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.109089s wall, 0.140625s user + 0.031250s system = 0.171875s CPU (157.6%)

PHY-1001 : Congestion index: top1 = 38.75, top5 = 25.11, top10 = 17.74, top15 = 13.10.
OPT-1001 : End congestion update;  0.152494s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (143.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2112 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058440s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (106.9%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.214122s wall, 0.250000s user + 0.031250s system = 0.281250s CPU (131.4%)

OPT-1001 : Current memory(MB): used = 211, reserve = 178, peak = 211.
OPT-1001 : End physical optimization;  0.685762s wall, 0.812500s user + 0.046875s system = 0.859375s CPU (125.3%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 370 LUT to BLE ...
SYN-4008 : Packed 370 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 775 remaining SEQ's ...
SYN-4005 : Packed 97 SEQ with LUT/SLICE
SYN-4006 : 100 single LUT's are left
SYN-4006 : 678 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1048/1359 primitive instances ...
PHY-3001 : End packing;  0.051622s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (90.8%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 799 instances
RUN-1001 : 374 mslices, 374 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1933 nets
RUN-1001 : 1360 nets have 2 pins
RUN-1001 : 467 nets have [3 - 5] pins
RUN-1001 : 62 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 797 instances, 748 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 54256.6, Over = 60.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6316, tnet num: 1931, tinst num: 797, tnode num: 8602, tedge num: 11164.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1931 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.284022s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (99.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.83738e-05
PHY-3002 : Step(137): len = 53417.5, overlap = 60.75
PHY-3002 : Step(138): len = 52888.2, overlap = 60.25
PHY-3002 : Step(139): len = 52655.4, overlap = 59
PHY-3002 : Step(140): len = 52877.3, overlap = 60
PHY-3002 : Step(141): len = 52877.6, overlap = 62.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.67477e-05
PHY-3002 : Step(142): len = 53051.9, overlap = 61.5
PHY-3002 : Step(143): len = 53446.1, overlap = 61.75
PHY-3002 : Step(144): len = 53864.3, overlap = 59.5
PHY-3002 : Step(145): len = 54434.6, overlap = 57.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000153495
PHY-3002 : Step(146): len = 55213.3, overlap = 53.25
PHY-3002 : Step(147): len = 55779, overlap = 51.25
PHY-3001 : Before Legalized: Len = 55779
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.077307s wall, 0.046875s user + 0.171875s system = 0.218750s CPU (283.0%)

PHY-3001 : After Legalized: Len = 68912.1, Over = 0
PHY-3001 : Trial Legalized: Len = 68912.1
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1931 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.053982s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (86.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.0014389
PHY-3002 : Step(148): len = 65036.2, overlap = 6
PHY-3002 : Step(149): len = 63537.4, overlap = 10.5
PHY-3002 : Step(150): len = 61639.6, overlap = 12.75
PHY-3002 : Step(151): len = 60745, overlap = 11.25
PHY-3002 : Step(152): len = 59628.7, overlap = 16
PHY-3002 : Step(153): len = 59187.6, overlap = 17
PHY-3002 : Step(154): len = 58832.7, overlap = 17.75
PHY-3002 : Step(155): len = 58611.4, overlap = 18.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00287781
PHY-3002 : Step(156): len = 58782.8, overlap = 18.75
PHY-3002 : Step(157): len = 58848, overlap = 18.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00575561
PHY-3002 : Step(158): len = 58872.7, overlap = 18.75
PHY-3002 : Step(159): len = 58868.7, overlap = 19
PHY-3001 : Before Legalized: Len = 58868.7
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005233s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 63454.9, Over = 0
PHY-3001 : Legalized: Len = 63454.9, Over = 0
PHY-3001 : Spreading special nets. 10 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.007359s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (212.3%)

PHY-3001 : 17 instances has been re-located, deltaX = 0, deltaY = 17, maxDist = 1.
PHY-3001 : Final: Len = 63638.9, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6316, tnet num: 1931, tinst num: 797, tnode num: 8602, tedge num: 11164.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 110/1933.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71296, over cnt = 137(0%), over = 225, worst = 6
PHY-1002 : len = 71952, over cnt = 91(0%), over = 123, worst = 4
PHY-1002 : len = 73288, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 73368, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 73400, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.133377s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (128.9%)

PHY-1001 : Congestion index: top1 = 32.87, top5 = 22.90, top10 = 17.62, top15 = 13.74.
PHY-1001 : End incremental global routing;  0.185227s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (118.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1931 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059412s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.273528s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (114.2%)

OPT-1001 : Current memory(MB): used = 213, reserve = 180, peak = 213.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1706/1933.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73400, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006403s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.87, top5 = 22.90, top10 = 17.62, top15 = 13.74.
OPT-1001 : End congestion update;  0.054239s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (115.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1931 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052215s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (89.8%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 757 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 797 instances, 748 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 63637.6, Over = 0
PHY-3001 : End spreading;  0.005102s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 63637.6, Over = 0
PHY-3001 : End incremental legalization;  0.038306s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (81.6%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.159427s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (98.0%)

OPT-1001 : Current memory(MB): used = 218, reserve = 185, peak = 218.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1931 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052209s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (119.7%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1702/1933.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73400, over cnt = 1(0%), over = 2, worst = 2
PHY-1002 : len = 73416, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 73432, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.024658s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (316.8%)

PHY-1001 : Congestion index: top1 = 32.91, top5 = 22.88, top10 = 17.61, top15 = 13.73.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1931 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050198s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (124.5%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 32.482759
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.882559s wall, 1.062500s user + 0.015625s system = 1.078125s CPU (122.2%)

RUN-1003 : finish command "place" in  5.188365s wall, 7.953125s user + 3.343750s system = 11.296875s CPU (217.7%)

RUN-1004 : used memory is 195 MB, reserved memory is 162 MB, peak memory is 218 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 799 instances
RUN-1001 : 374 mslices, 374 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1933 nets
RUN-1001 : 1360 nets have 2 pins
RUN-1001 : 467 nets have [3 - 5] pins
RUN-1001 : 62 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6316, tnet num: 1931, tinst num: 797, tnode num: 8602, tedge num: 11164.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 374 mslices, 374 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1931 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69184, over cnt = 139(0%), over = 224, worst = 6
PHY-1002 : len = 70344, over cnt = 77(0%), over = 95, worst = 4
PHY-1002 : len = 71576, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 71656, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.123292s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (114.1%)

PHY-1001 : Congestion index: top1 = 31.79, top5 = 22.35, top10 = 17.22, top15 = 13.44.
PHY-1001 : End global routing;  0.174466s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (107.5%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 233, reserve = 202, peak = 233.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 490, reserve = 462, peak = 490.
PHY-1001 : End build detailed router design. 3.163470s wall, 3.093750s user + 0.062500s system = 3.156250s CPU (99.8%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 30056, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.098569s wall, 1.093750s user + 0.000000s system = 1.093750s CPU (99.6%)

PHY-1001 : Current memory(MB): used = 523, reserve = 495, peak = 523.
PHY-1001 : End phase 1; 1.105230s wall, 1.109375s user + 0.000000s system = 1.109375s CPU (100.4%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 37% nets.
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 175872, over cnt = 35(0%), over = 35, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 525, reserve = 496, peak = 525.
PHY-1001 : End initial routed; 1.716106s wall, 2.531250s user + 0.093750s system = 2.625000s CPU (153.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1718(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.003   |  -46.683  |  24   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.336267s wall, 0.328125s user + 0.015625s system = 0.343750s CPU (102.2%)

PHY-1001 : Current memory(MB): used = 527, reserve = 498, peak = 527.
PHY-1001 : End phase 2; 2.052471s wall, 2.859375s user + 0.109375s system = 2.968750s CPU (144.6%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 175872, over cnt = 35(0%), over = 35, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014525s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (107.6%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 175720, over cnt = 5(0%), over = 5, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.030721s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (101.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 175688, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.026807s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (116.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1718(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.003   |  -46.683  |  24   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.332274s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (98.8%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 9 feed throughs used by 6 nets
PHY-1001 : End commit to database; 0.167435s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (102.7%)

PHY-1001 : Current memory(MB): used = 541, reserve = 512, peak = 541.
PHY-1001 : End phase 3; 0.700829s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (98.1%)

PHY-1003 : Routed, final wirelength = 175688
PHY-1001 : Current memory(MB): used = 541, reserve = 513, peak = 541.
PHY-1001 : End export database. 0.009494s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (164.6%)

PHY-1001 : End detail routing;  7.207065s wall, 7.906250s user + 0.187500s system = 8.093750s CPU (112.3%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6316, tnet num: 1931, tinst num: 797, tnode num: 8602, tedge num: 11164.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[24] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dia[6] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[26] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dia[8] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[32] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dib[5] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[3] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[6] slack -86ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[0] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[13] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[4] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[17] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[8] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/asy_w_rst1 endpin signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_15.sr slack -89ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/asy_w_rst1 endpin signal_process/demodu/fifo/rd_to_wr_cross_inst/reg2_syn_24.sr slack -73ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_52.mi[0] slack -2991ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_52.mi[1] slack -2632ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_55.mi[0] slack -3003ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_55.mi[1] slack -2991ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_58.mi[0] slack -2871ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_58.mi[1] slack -2859ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_61.mi[0] slack -2991ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_61.mi[1] slack -3003ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_64.mi[0] slack -3003ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_64.mi[1] slack -2763ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_67.mi[0] slack -3003ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_67.mi[1] slack -2763ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_70.mi[0] slack -2991ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_70.mi[1] slack -3003ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_73.mi[0] slack -2632ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_73.mi[1] slack -2763ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6438, tnet num: 1992, tinst num: 858, tnode num: 8724, tedge num: 11286.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_52_mi[1] slack -647ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_55_mi[1] slack -814ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_55_mi[0] slack -62ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_52_mi[0] slack -413ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_58_mi[0] slack -562ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_58_mi[1] slack -568ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg2_syn_64_mi[0] slack -615ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_64_mi[1] slack -633ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_61_mi[0] slack -664ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_67_mi[1] slack -508ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_61_mi[1] slack -356ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_67_mi[0] slack -653ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_70_mi[0] slack -614ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_70_mi[1] slack -559ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_73_mi[0] slack -665ps
RUN-1001 : End hold fix;  3.245953s wall, 3.437500s user + 0.171875s system = 3.609375s CPU (111.2%)

RUN-1003 : finish command "route" in  10.950502s wall, 11.843750s user + 0.375000s system = 12.218750s CPU (111.6%)

RUN-1004 : used memory is 516 MB, reserved memory is 488 MB, peak memory is 541 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      891   out of  19600    4.55%
#reg                     1053   out of  19600    5.37%
#le                      1569
  #lut only               516   out of   1569   32.89%
  #reg only               678   out of   1569   43.21%
  #lut&reg                375   out of   1569   23.90%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         474
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         101
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    40
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1569   |695     |196     |1086    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1063   |294     |134     |865     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |24     |18      |6       |21      |0       |0       |
|    demodu                  |Demodulation                                     |457    |102     |45      |353     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |57     |32      |6       |45      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |5       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |12     |7       |0       |12      |0       |0       |
|    integ                   |Integration                                      |138    |36      |15      |110     |0       |0       |
|    modu                    |Modulation                                       |103    |54      |14      |101     |0       |1       |
|    rs422                   |Rs422Output                                      |315    |66      |46      |259     |0       |4       |
|    trans                   |SquareWaveGenerator                              |26     |18      |8       |21      |0       |0       |
|  u_uart                    |UART_Control                                     |181    |131     |7       |117     |0       |0       |
|    U0                      |speed_select_Tx                                  |34     |26      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |26     |19      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |121    |86      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |305    |260     |45      |70      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1382  
    #2          2       334   
    #3          3       110   
    #4          4        23   
    #5        5-10       68   
    #6        11-50      32   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6438, tnet num: 1992, tinst num: 858, tnode num: 8724, tedge num: 11286.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1992 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 858
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1994, pip num: 14531
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 13
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1358 valid insts, and 38943 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.317282s wall, 18.656250s user + 0.078125s system = 18.734375s CPU (564.8%)

RUN-1004 : used memory is 516 MB, reserved memory is 488 MB, peak memory is 665 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250804_150019.log"
