============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Fri Aug 15 14:18:56 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1558 instances
RUN-0007 : 369 luts, 941 seqs, 127 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2089 nets
RUN-1001 : 1540 nets have 2 pins
RUN-1001 : 447 nets have [3 - 5] pins
RUN-1001 : 60 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     237     
RUN-1001 :   No   |  No   |  Yes  |     125     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1556 instances, 369 luts, 941 seqs, 197 slices, 21 macros(197 instances: 127 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7442, tnet num: 2087, tinst num: 1556, tnode num: 10551, tedge num: 12604.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2087 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.245202s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (102.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 518548
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1556.
PHY-3001 : End clustering;  0.000020s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 453878, overlap = 18
PHY-3002 : Step(2): len = 425241, overlap = 13.5
PHY-3002 : Step(3): len = 408946, overlap = 20.25
PHY-3002 : Step(4): len = 395185, overlap = 13.5
PHY-3002 : Step(5): len = 385551, overlap = 18
PHY-3002 : Step(6): len = 367339, overlap = 11.25
PHY-3002 : Step(7): len = 357532, overlap = 15.75
PHY-3002 : Step(8): len = 349478, overlap = 15.75
PHY-3002 : Step(9): len = 343062, overlap = 15.75
PHY-3002 : Step(10): len = 333444, overlap = 11.25
PHY-3002 : Step(11): len = 328135, overlap = 13.5
PHY-3002 : Step(12): len = 321045, overlap = 13.5
PHY-3002 : Step(13): len = 314734, overlap = 13.5
PHY-3002 : Step(14): len = 307219, overlap = 13.5
PHY-3002 : Step(15): len = 303136, overlap = 13.5
PHY-3002 : Step(16): len = 294088, overlap = 13.5
PHY-3002 : Step(17): len = 287862, overlap = 13.5
PHY-3002 : Step(18): len = 282111, overlap = 13.5
PHY-3002 : Step(19): len = 277248, overlap = 15.75
PHY-3002 : Step(20): len = 269431, overlap = 15.75
PHY-3002 : Step(21): len = 265006, overlap = 15.75
PHY-3002 : Step(22): len = 259683, overlap = 15.75
PHY-3002 : Step(23): len = 255497, overlap = 15.75
PHY-3002 : Step(24): len = 245874, overlap = 15.75
PHY-3002 : Step(25): len = 240691, overlap = 18
PHY-3002 : Step(26): len = 236809, overlap = 20.25
PHY-3002 : Step(27): len = 229633, overlap = 20.25
PHY-3002 : Step(28): len = 218169, overlap = 20.25
PHY-3002 : Step(29): len = 216220, overlap = 20.25
PHY-3002 : Step(30): len = 207637, overlap = 20.25
PHY-3002 : Step(31): len = 187270, overlap = 20.25
PHY-3002 : Step(32): len = 181529, overlap = 20.25
PHY-3002 : Step(33): len = 179955, overlap = 20.25
PHY-3002 : Step(34): len = 123346, overlap = 18
PHY-3002 : Step(35): len = 117113, overlap = 15.75
PHY-3002 : Step(36): len = 115043, overlap = 15.75
PHY-3002 : Step(37): len = 113707, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000103624
PHY-3002 : Step(38): len = 113970, overlap = 13.5
PHY-3002 : Step(39): len = 112145, overlap = 13.5
PHY-3002 : Step(40): len = 111145, overlap = 13.5
PHY-3002 : Step(41): len = 109945, overlap = 11.25
PHY-3002 : Step(42): len = 107619, overlap = 11.25
PHY-3002 : Step(43): len = 105863, overlap = 11.25
PHY-3002 : Step(44): len = 103522, overlap = 11.25
PHY-3002 : Step(45): len = 102665, overlap = 11.25
PHY-3002 : Step(46): len = 99490.1, overlap = 11.25
PHY-3002 : Step(47): len = 97999.4, overlap = 11.25
PHY-3002 : Step(48): len = 95951.6, overlap = 11.25
PHY-3002 : Step(49): len = 95213.8, overlap = 13.5
PHY-3002 : Step(50): len = 92141, overlap = 11.25
PHY-3002 : Step(51): len = 89901.5, overlap = 15.75
PHY-3002 : Step(52): len = 87422.6, overlap = 15.75
PHY-3002 : Step(53): len = 87375.7, overlap = 13.5
PHY-3002 : Step(54): len = 83279.7, overlap = 13.5
PHY-3002 : Step(55): len = 81084.8, overlap = 11.25
PHY-3002 : Step(56): len = 78899.2, overlap = 15.75
PHY-3002 : Step(57): len = 78341.8, overlap = 15.75
PHY-3002 : Step(58): len = 76799, overlap = 15.75
PHY-3002 : Step(59): len = 76175.3, overlap = 15.75
PHY-3002 : Step(60): len = 73827, overlap = 15.75
PHY-3002 : Step(61): len = 70657.4, overlap = 17.1875
PHY-3002 : Step(62): len = 69374.8, overlap = 17.3125
PHY-3002 : Step(63): len = 68248.1, overlap = 17.3125
PHY-3002 : Step(64): len = 67089.1, overlap = 15.625
PHY-3002 : Step(65): len = 66406.8, overlap = 15.8125
PHY-3002 : Step(66): len = 66054.2, overlap = 15.9375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000207248
PHY-3002 : Step(67): len = 66553.3, overlap = 15.9375
PHY-3002 : Step(68): len = 66387.2, overlap = 15.9375
PHY-3002 : Step(69): len = 66361.4, overlap = 15.9375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000414496
PHY-3002 : Step(70): len = 66766.4, overlap = 15.9375
PHY-3002 : Step(71): len = 66797.3, overlap = 15.9375
PHY-3002 : Step(72): len = 66785.6, overlap = 15.9375
PHY-3001 : Before Legalized: Len = 66785.6
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005936s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 68988.6, Over = 2.4375
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2087 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.057646s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (81.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(73): len = 68802.6, overlap = 12.9688
PHY-3002 : Step(74): len = 67493.3, overlap = 13.1562
PHY-3002 : Step(75): len = 65948.8, overlap = 13.0312
PHY-3002 : Step(76): len = 64974.8, overlap = 12.9688
PHY-3002 : Step(77): len = 64047.9, overlap = 13.0938
PHY-3002 : Step(78): len = 63349.3, overlap = 12.7188
PHY-3002 : Step(79): len = 61413.9, overlap = 14.8125
PHY-3002 : Step(80): len = 59926, overlap = 18.4062
PHY-3002 : Step(81): len = 59346.6, overlap = 19
PHY-3002 : Step(82): len = 58618.6, overlap = 19.6875
PHY-3002 : Step(83): len = 58191.9, overlap = 19.8438
PHY-3002 : Step(84): len = 57419, overlap = 20.4375
PHY-3002 : Step(85): len = 56922.9, overlap = 18.4062
PHY-3002 : Step(86): len = 56672.6, overlap = 18.2188
PHY-3002 : Step(87): len = 55933.6, overlap = 17.7188
PHY-3002 : Step(88): len = 55531.8, overlap = 17.5625
PHY-3002 : Step(89): len = 54458.3, overlap = 15.4375
PHY-3002 : Step(90): len = 53939.2, overlap = 15.5938
PHY-3002 : Step(91): len = 53476.4, overlap = 15.5
PHY-3002 : Step(92): len = 52960.8, overlap = 15.6875
PHY-3002 : Step(93): len = 52277.8, overlap = 17.2812
PHY-3002 : Step(94): len = 51758.7, overlap = 21.25
PHY-3002 : Step(95): len = 51061.8, overlap = 19.2188
PHY-3002 : Step(96): len = 50844.1, overlap = 20.0938
PHY-3002 : Step(97): len = 50585.8, overlap = 20.4062
PHY-3002 : Step(98): len = 50324.8, overlap = 20.0312
PHY-3002 : Step(99): len = 49441.6, overlap = 22.6562
PHY-3002 : Step(100): len = 49217.6, overlap = 24.9062
PHY-3002 : Step(101): len = 48828.8, overlap = 24
PHY-3002 : Step(102): len = 48477.2, overlap = 23.9375
PHY-3002 : Step(103): len = 48207.8, overlap = 24.1562
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000261021
PHY-3002 : Step(104): len = 48189.2, overlap = 22.5312
PHY-3002 : Step(105): len = 48166.1, overlap = 22.7812
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000522041
PHY-3002 : Step(106): len = 48180.1, overlap = 22.1875
PHY-3002 : Step(107): len = 48248.2, overlap = 21.8125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2087 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.069539s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (112.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.21002e-05
PHY-3002 : Step(108): len = 48322.8, overlap = 61.2812
PHY-3002 : Step(109): len = 49311, overlap = 51.0938
PHY-3002 : Step(110): len = 50097.4, overlap = 50.1875
PHY-3002 : Step(111): len = 49908.1, overlap = 49.8438
PHY-3002 : Step(112): len = 49642.5, overlap = 46.4688
PHY-3002 : Step(113): len = 49699.3, overlap = 46.3438
PHY-3002 : Step(114): len = 49561.1, overlap = 46.4062
PHY-3002 : Step(115): len = 49532.9, overlap = 45.9062
PHY-3002 : Step(116): len = 49684.6, overlap = 46.3438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0001242
PHY-3002 : Step(117): len = 49699.9, overlap = 45.8438
PHY-3002 : Step(118): len = 49826.9, overlap = 45.5938
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000209733
PHY-3002 : Step(119): len = 50073.5, overlap = 42.2188
PHY-3002 : Step(120): len = 51523.7, overlap = 34.7188
PHY-3002 : Step(121): len = 51385.1, overlap = 38
PHY-3002 : Step(122): len = 51787.7, overlap = 36.5312
PHY-3002 : Step(123): len = 52255.3, overlap = 36.25
PHY-3002 : Step(124): len = 52418.9, overlap = 35.9375
PHY-3002 : Step(125): len = 52208.9, overlap = 36.0312
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000419465
PHY-3002 : Step(126): len = 52257.2, overlap = 35.6562
PHY-3002 : Step(127): len = 52181.3, overlap = 35.5
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7442, tnet num: 2087, tinst num: 1556, tnode num: 10551, tedge num: 12604.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 85.06 peak overflow 3.69
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2089.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55288, over cnt = 234(0%), over = 983, worst = 23
PHY-1001 : End global iterations;  0.082611s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (75.7%)

PHY-1001 : Congestion index: top1 = 45.11, top5 = 25.13, top10 = 15.78, top15 = 11.29.
PHY-1001 : End incremental global routing;  0.140871s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (88.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2087 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.064997s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (96.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.234651s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (93.2%)

OPT-1001 : Current memory(MB): used = 207, reserve = 174, peak = 207.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1565/2089.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55288, over cnt = 234(0%), over = 983, worst = 23
PHY-1002 : len = 62472, over cnt = 162(0%), over = 342, worst = 14
PHY-1002 : len = 65320, over cnt = 63(0%), over = 100, worst = 12
PHY-1002 : len = 66120, over cnt = 13(0%), over = 17, worst = 3
PHY-1002 : len = 66624, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.091623s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (136.4%)

PHY-1001 : Congestion index: top1 = 38.21, top5 = 24.74, top10 = 17.47, top15 = 12.98.
OPT-1001 : End congestion update;  0.134908s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (127.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2087 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059001s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.9%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.197166s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (118.9%)

OPT-1001 : Current memory(MB): used = 210, reserve = 177, peak = 210.
OPT-1001 : End physical optimization;  0.670072s wall, 0.671875s user + 0.015625s system = 0.687500s CPU (102.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 369 LUT to BLE ...
SYN-4008 : Packed 369 LUT and 190 SEQ to BLE.
SYN-4003 : Packing 751 remaining SEQ's ...
SYN-4005 : Packed 83 SEQ with LUT/SLICE
SYN-4006 : 112 single LUT's are left
SYN-4006 : 668 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1037/1349 primitive instances ...
PHY-3001 : End packing;  0.046453s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.9%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 787 instances
RUN-1001 : 368 mslices, 368 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1907 nets
RUN-1001 : 1364 nets have 2 pins
RUN-1001 : 442 nets have [3 - 5] pins
RUN-1001 : 60 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 785 instances, 736 slices, 21 macros(197 instances: 127 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 51948.4, Over = 63.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6206, tnet num: 1905, tinst num: 785, tnode num: 8443, tedge num: 10952.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.273946s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (97.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.42922e-05
PHY-3002 : Step(128): len = 51475.6, overlap = 61.75
PHY-3002 : Step(129): len = 50991.1, overlap = 60
PHY-3002 : Step(130): len = 50871, overlap = 58.5
PHY-3002 : Step(131): len = 50959.1, overlap = 58.5
PHY-3002 : Step(132): len = 50755.3, overlap = 59
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.85844e-05
PHY-3002 : Step(133): len = 50925.8, overlap = 57.25
PHY-3002 : Step(134): len = 51296.2, overlap = 54.75
PHY-3002 : Step(135): len = 51917.9, overlap = 55
PHY-3002 : Step(136): len = 52365.1, overlap = 53.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000137169
PHY-3002 : Step(137): len = 52712.8, overlap = 51.75
PHY-3002 : Step(138): len = 53337.6, overlap = 48.5
PHY-3001 : Before Legalized: Len = 53337.6
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.099836s wall, 0.046875s user + 0.125000s system = 0.171875s CPU (172.2%)

PHY-3001 : After Legalized: Len = 67382.7, Over = 0
PHY-3001 : Trial Legalized: Len = 67382.7
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.046155s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (101.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00110354
PHY-3002 : Step(139): len = 63816.8, overlap = 6.25
PHY-3002 : Step(140): len = 62458.8, overlap = 10.25
PHY-3002 : Step(141): len = 60467, overlap = 13.5
PHY-3002 : Step(142): len = 59247.1, overlap = 18
PHY-3002 : Step(143): len = 58693.7, overlap = 22
PHY-3002 : Step(144): len = 58179, overlap = 23.5
PHY-3002 : Step(145): len = 57675.2, overlap = 23.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00220708
PHY-3002 : Step(146): len = 57932.6, overlap = 24
PHY-3002 : Step(147): len = 58017.8, overlap = 23.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00441417
PHY-3002 : Step(148): len = 58130.6, overlap = 23.75
PHY-3002 : Step(149): len = 58172.1, overlap = 23.25
PHY-3001 : Before Legalized: Len = 58172.1
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004941s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (316.2%)

PHY-3001 : After Legalized: Len = 62208.1, Over = 0
PHY-3001 : Legalized: Len = 62208.1, Over = 0
PHY-3001 : Spreading special nets. 3 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.004875s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 6 instances has been re-located, deltaX = 0, deltaY = 6, maxDist = 1.
PHY-3001 : Final: Len = 62226.1, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6206, tnet num: 1905, tinst num: 785, tnode num: 8443, tedge num: 10952.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 69/1907.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68672, over cnt = 131(0%), over = 215, worst = 6
PHY-1002 : len = 69568, over cnt = 90(0%), over = 121, worst = 6
PHY-1002 : len = 70944, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 71008, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.118410s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (158.3%)

PHY-1001 : Congestion index: top1 = 31.25, top5 = 22.67, top10 = 17.52, top15 = 13.59.
PHY-1001 : End incremental global routing;  0.168859s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (138.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057126s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (109.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.254387s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (129.0%)

OPT-1001 : Current memory(MB): used = 212, reserve = 180, peak = 212.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1699/1907.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71008, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005332s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (293.1%)

PHY-1001 : Congestion index: top1 = 31.25, top5 = 22.67, top10 = 17.52, top15 = 13.59.
OPT-1001 : End congestion update;  0.051017s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047472s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.7%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 745 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 785 instances, 736 slices, 21 macros(197 instances: 127 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 62226.4, Over = 0
PHY-3001 : End spreading;  0.004714s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 62226.4, Over = 0
PHY-3001 : End incremental legalization;  0.033292s wall, 0.078125s user + 0.015625s system = 0.093750s CPU (281.6%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.144541s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (140.5%)

OPT-1001 : Current memory(MB): used = 217, reserve = 185, peak = 217.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.045491s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (103.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1695/1907.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71016, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007711s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.25, top5 = 22.69, top10 = 17.52, top15 = 13.59.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048757s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (96.1%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.896552
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.809408s wall, 0.921875s user + 0.015625s system = 0.937500s CPU (115.8%)

RUN-1003 : finish command "place" in  4.748268s wall, 6.187500s user + 2.921875s system = 9.109375s CPU (191.8%)

RUN-1004 : used memory is 200 MB, reserved memory is 167 MB, peak memory is 217 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 787 instances
RUN-1001 : 368 mslices, 368 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1907 nets
RUN-1001 : 1364 nets have 2 pins
RUN-1001 : 442 nets have [3 - 5] pins
RUN-1001 : 60 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6206, tnet num: 1905, tinst num: 785, tnode num: 8443, tedge num: 10952.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 368 mslices, 368 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 67768, over cnt = 140(0%), over = 215, worst = 7
PHY-1002 : len = 68592, over cnt = 89(0%), over = 115, worst = 6
PHY-1002 : len = 70192, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 70224, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.115923s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (148.3%)

PHY-1001 : Congestion index: top1 = 31.03, top5 = 22.29, top10 = 17.26, top15 = 13.41.
PHY-1001 : End global routing;  0.163569s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (133.7%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 228, reserve = 196, peak = 233.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 492, reserve = 463, peak = 492.
PHY-1001 : End build detailed router design. 3.101122s wall, 3.046875s user + 0.046875s system = 3.093750s CPU (99.8%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 30280, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.065206s wall, 1.046875s user + 0.015625s system = 1.062500s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 524, reserve = 497, peak = 524.
PHY-1001 : End phase 1; 1.072013s wall, 1.046875s user + 0.015625s system = 1.062500s CPU (99.1%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 38% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 171688, over cnt = 34(0%), over = 34, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 524, reserve = 497, peak = 524.
PHY-1001 : End initial routed; 1.375117s wall, 2.140625s user + 0.093750s system = 2.234375s CPU (162.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1692(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.870   |  -44.225  |  19   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.327514s wall, 0.328125s user + 0.015625s system = 0.343750s CPU (105.0%)

PHY-1001 : Current memory(MB): used = 527, reserve = 498, peak = 527.
PHY-1001 : End phase 2; 1.702722s wall, 2.468750s user + 0.109375s system = 2.578125s CPU (151.4%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 171688, over cnt = 34(0%), over = 34, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014792s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (105.6%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 171408, over cnt = 4(0%), over = 4, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.033150s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (94.3%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 171424, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.020465s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (152.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1692(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.870   |  -44.225  |  19   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.324478s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (101.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 5 feed throughs used by 5 nets
PHY-1001 : End commit to database; 0.161105s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (97.0%)

PHY-1001 : Current memory(MB): used = 540, reserve = 512, peak = 540.
PHY-1001 : End phase 3; 0.680070s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (101.1%)

PHY-1003 : Routed, final wirelength = 171424
PHY-1001 : Current memory(MB): used = 540, reserve = 512, peak = 540.
PHY-1001 : End export database. 0.010079s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (155.0%)

PHY-1001 : End detail routing;  6.742548s wall, 7.421875s user + 0.171875s system = 7.593750s CPU (112.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6206, tnet num: 1905, tinst num: 785, tnode num: 8443, tedge num: 10952.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[37] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[8] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[15] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/asy_w_rst1 endpin signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_20.sr slack -81ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg1_syn_217.mi[0] slack -2726ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_74.mi[0] slack -2499ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_74.mi[1] slack -2870ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_78.mi[0] slack -2870ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_78.mi[1] slack -2870ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_81.mi[0] slack -2858ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_81.mi[1] slack -2630ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_84.mi[0] slack -2870ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_84.mi[1] slack -2726ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_87.mi[0] slack -2870ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_87.mi[1] slack -2494ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_90.mi[0] slack -2571ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_90.mi[1] slack -2870ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_93.mi[0] slack -2858ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_93.mi[1] slack -2858ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_95.mi[0] slack -2630ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6314, tnet num: 1959, tinst num: 839, tnode num: 8551, tedge num: 11060.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_74_mi[1] slack -131ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_93_mi[1] slack -479ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_95_mi[0] slack -403ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_93_mi[0] slack -469ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_78_mi[1] slack -583ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_78_mi[0] slack -245ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg2_syn_81_mi[0] slack -235ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_81_mi[1] slack -40ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_84_mi[1] slack -193ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_87_mi[0] slack -231ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_84_mi[0] slack -681ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_90_mi[1] slack -330ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_90_mi[0] slack -474ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_87_mi[1] slack -219ps
RUN-1001 : End hold fix;  3.110086s wall, 3.218750s user + 0.296875s system = 3.515625s CPU (113.0%)

RUN-1003 : finish command "route" in  10.336048s wall, 11.187500s user + 0.468750s system = 11.656250s CPU (112.8%)

RUN-1004 : used memory is 520 MB, reserved memory is 492 MB, peak memory is 540 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   15
  #output                  21
  #inout                    1

Utilization Statistics
#lut                      873   out of  19600    4.45%
#reg                     1025   out of  19600    5.23%
#le                      1541
  #lut only               516   out of   1541   33.48%
  #reg only               668   out of   1541   43.35%
  #lut&reg                357   out of   1541   23.17%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    13
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         461
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         100
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    41
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
  RxTransmit       INPUT        M14        LVCMOS33          N/A          PULLUP      IREG    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1541   |676     |197     |1059    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1038   |277     |133     |835     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |23     |19      |4       |20      |0       |0       |
|    demodu                  |Demodulation                                     |444    |89      |46      |352     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |57     |30      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |6       |0       |16      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |13     |6       |0       |13      |0       |0       |
|    integ                   |Integration                                      |142    |23      |15      |114     |0       |0       |
|    modu                    |Modulation                                       |68     |41      |14      |64      |0       |1       |
|    rs422                   |Rs422Output                                      |325    |77      |46      |265     |0       |4       |
|    trans                   |SquareWaveGenerator                              |36     |28      |8       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |174    |127     |7       |118     |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |26      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |26     |19      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |113    |82      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |305    |260     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1379  
    #2          2       307   
    #3          3       100   
    #4          4        35   
    #5        5-10       64   
    #6        11-50      31   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6314, tnet num: 1959, tinst num: 839, tnode num: 8551, tedge num: 11060.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1959 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 839
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1961, pip num: 14304
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 11
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1288 valid insts, and 38416 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.016069s wall, 17.265625s user + 0.046875s system = 17.312500s CPU (574.0%)

RUN-1004 : used memory is 516 MB, reserved memory is 491 MB, peak memory is 665 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250815_141856.log"
