============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Mon Aug  4 15:10:33 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1571 instances
RUN-0007 : 366 luts, 951 seqs, 133 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2113 nets
RUN-1001 : 1541 nets have 2 pins
RUN-1001 : 466 nets have [3 - 5] pins
RUN-1001 : 63 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     251     
RUN-1001 :   No   |  No   |  Yes  |     121     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1569 instances, 366 luts, 951 seqs, 203 slices, 21 macros(203 instances: 133 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7529, tnet num: 2111, tinst num: 1569, tnode num: 10687, tedge num: 12794.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2111 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.253335s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (98.7%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 534824
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1569.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 468674, overlap = 18
PHY-3002 : Step(2): len = 450546, overlap = 11.25
PHY-3002 : Step(3): len = 439765, overlap = 20.25
PHY-3002 : Step(4): len = 428777, overlap = 13.5
PHY-3002 : Step(5): len = 416392, overlap = 18
PHY-3002 : Step(6): len = 404020, overlap = 13.5
PHY-3002 : Step(7): len = 393515, overlap = 13.5
PHY-3002 : Step(8): len = 384930, overlap = 13.5
PHY-3002 : Step(9): len = 373772, overlap = 15.75
PHY-3002 : Step(10): len = 365899, overlap = 15.75
PHY-3002 : Step(11): len = 359441, overlap = 13.5
PHY-3002 : Step(12): len = 347823, overlap = 11.25
PHY-3002 : Step(13): len = 340052, overlap = 11.25
PHY-3002 : Step(14): len = 334669, overlap = 11.25
PHY-3002 : Step(15): len = 325182, overlap = 11.25
PHY-3002 : Step(16): len = 316461, overlap = 11.25
PHY-3002 : Step(17): len = 311740, overlap = 13.5
PHY-3002 : Step(18): len = 303827, overlap = 13.5
PHY-3002 : Step(19): len = 296189, overlap = 13.5
PHY-3002 : Step(20): len = 290756, overlap = 13.5
PHY-3002 : Step(21): len = 285428, overlap = 13.5
PHY-3002 : Step(22): len = 275892, overlap = 13.5
PHY-3002 : Step(23): len = 269434, overlap = 13.5
PHY-3002 : Step(24): len = 265907, overlap = 13.5
PHY-3002 : Step(25): len = 258141, overlap = 18
PHY-3002 : Step(26): len = 248544, overlap = 20.25
PHY-3002 : Step(27): len = 244813, overlap = 20.25
PHY-3002 : Step(28): len = 239386, overlap = 20.25
PHY-3002 : Step(29): len = 228466, overlap = 20.25
PHY-3002 : Step(30): len = 223287, overlap = 20.25
PHY-3002 : Step(31): len = 220395, overlap = 20.25
PHY-3002 : Step(32): len = 212976, overlap = 20.25
PHY-3002 : Step(33): len = 198227, overlap = 20.25
PHY-3002 : Step(34): len = 194063, overlap = 20.25
PHY-3002 : Step(35): len = 191469, overlap = 20.25
PHY-3002 : Step(36): len = 171027, overlap = 18
PHY-3002 : Step(37): len = 159595, overlap = 20.25
PHY-3002 : Step(38): len = 158172, overlap = 20.25
PHY-3002 : Step(39): len = 143318, overlap = 18
PHY-3002 : Step(40): len = 117744, overlap = 15.75
PHY-3002 : Step(41): len = 115724, overlap = 18
PHY-3002 : Step(42): len = 112128, overlap = 18
PHY-3002 : Step(43): len = 109040, overlap = 18
PHY-3002 : Step(44): len = 107525, overlap = 13.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.9269e-05
PHY-3002 : Step(45): len = 108205, overlap = 11.25
PHY-3002 : Step(46): len = 107424, overlap = 13.5
PHY-3002 : Step(47): len = 105748, overlap = 13.5
PHY-3002 : Step(48): len = 105393, overlap = 13.5
PHY-3002 : Step(49): len = 104440, overlap = 11.25
PHY-3002 : Step(50): len = 101685, overlap = 11.25
PHY-3002 : Step(51): len = 101119, overlap = 11.25
PHY-3002 : Step(52): len = 99943.6, overlap = 11.25
PHY-3002 : Step(53): len = 97188.3, overlap = 13.5
PHY-3002 : Step(54): len = 93444.8, overlap = 11.25
PHY-3002 : Step(55): len = 92880, overlap = 11.25
PHY-3002 : Step(56): len = 91634.2, overlap = 13.5
PHY-3002 : Step(57): len = 89767.1, overlap = 15.75
PHY-3002 : Step(58): len = 86604.4, overlap = 15.75
PHY-3002 : Step(59): len = 84930.2, overlap = 11.25
PHY-3002 : Step(60): len = 84118.9, overlap = 11.25
PHY-3002 : Step(61): len = 83485.6, overlap = 13.5
PHY-3002 : Step(62): len = 81360.4, overlap = 18
PHY-3002 : Step(63): len = 81049.3, overlap = 15.75
PHY-3002 : Step(64): len = 79032.9, overlap = 13.5
PHY-3002 : Step(65): len = 76960.2, overlap = 14.5625
PHY-3002 : Step(66): len = 76697.9, overlap = 14.5625
PHY-3002 : Step(67): len = 75164.2, overlap = 19.625
PHY-3002 : Step(68): len = 73769.1, overlap = 17.5625
PHY-3002 : Step(69): len = 72662.8, overlap = 17.5625
PHY-3002 : Step(70): len = 71850.7, overlap = 15.4375
PHY-3002 : Step(71): len = 70401.7, overlap = 13.3125
PHY-3002 : Step(72): len = 69761.6, overlap = 13.3125
PHY-3002 : Step(73): len = 67450.2, overlap = 20.5
PHY-3002 : Step(74): len = 65330.6, overlap = 18.25
PHY-3002 : Step(75): len = 64440.2, overlap = 19.0625
PHY-3002 : Step(76): len = 64395.9, overlap = 16.6875
PHY-3002 : Step(77): len = 63741.6, overlap = 16.4375
PHY-3002 : Step(78): len = 62980.1, overlap = 16.3125
PHY-3002 : Step(79): len = 61925, overlap = 13.9375
PHY-3002 : Step(80): len = 60643.6, overlap = 13.8125
PHY-3002 : Step(81): len = 60089.6, overlap = 13.6875
PHY-3002 : Step(82): len = 59533.7, overlap = 17.8125
PHY-3002 : Step(83): len = 59244.8, overlap = 17.6875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000198538
PHY-3002 : Step(84): len = 59517.8, overlap = 15.4375
PHY-3002 : Step(85): len = 59724.9, overlap = 15.4375
PHY-3002 : Step(86): len = 59735.2, overlap = 15.4375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000397076
PHY-3002 : Step(87): len = 59705.4, overlap = 15.4375
PHY-3002 : Step(88): len = 59715.6, overlap = 15.4375
PHY-3001 : Before Legalized: Len = 59715.6
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.018437s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 62478.4, Over = 1.9375
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2111 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.065193s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (95.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(89): len = 62804.1, overlap = 9
PHY-3002 : Step(90): len = 62524.8, overlap = 8.625
PHY-3002 : Step(91): len = 61837.6, overlap = 8.625
PHY-3002 : Step(92): len = 61017.4, overlap = 7.875
PHY-3002 : Step(93): len = 60070.5, overlap = 7.75
PHY-3002 : Step(94): len = 58653.7, overlap = 8
PHY-3002 : Step(95): len = 57675.4, overlap = 5.4375
PHY-3002 : Step(96): len = 57052.2, overlap = 5.6875
PHY-3002 : Step(97): len = 56421.8, overlap = 5.3125
PHY-3002 : Step(98): len = 55752.7, overlap = 5.3125
PHY-3002 : Step(99): len = 54710.1, overlap = 7.125
PHY-3002 : Step(100): len = 54329.1, overlap = 7.5
PHY-3002 : Step(101): len = 53877.6, overlap = 8.375
PHY-3002 : Step(102): len = 53534.6, overlap = 8.4375
PHY-3002 : Step(103): len = 52819, overlap = 8.96875
PHY-3002 : Step(104): len = 52638.7, overlap = 9.21875
PHY-3002 : Step(105): len = 52180.9, overlap = 9.53125
PHY-3002 : Step(106): len = 51851.7, overlap = 9.78125
PHY-3002 : Step(107): len = 51355, overlap = 11.2188
PHY-3002 : Step(108): len = 51354.1, overlap = 11.4688
PHY-3002 : Step(109): len = 50994.1, overlap = 13.875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000304655
PHY-3002 : Step(110): len = 50912.3, overlap = 14.375
PHY-3002 : Step(111): len = 50921.9, overlap = 12.1875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00060931
PHY-3002 : Step(112): len = 50860.2, overlap = 11.9062
PHY-3002 : Step(113): len = 50851.8, overlap = 11.7188
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2111 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.061857s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.68407e-05
PHY-3002 : Step(114): len = 51114.6, overlap = 51.8125
PHY-3002 : Step(115): len = 52458.4, overlap = 50.6875
PHY-3002 : Step(116): len = 52671.7, overlap = 49.6562
PHY-3002 : Step(117): len = 52161.5, overlap = 44.2812
PHY-3002 : Step(118): len = 52142.9, overlap = 43.875
PHY-3002 : Step(119): len = 52165.7, overlap = 43.5312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000133681
PHY-3002 : Step(120): len = 52362.5, overlap = 42.9688
PHY-3002 : Step(121): len = 52716.4, overlap = 41.7812
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000262071
PHY-3002 : Step(122): len = 52829.4, overlap = 41.4375
PHY-3002 : Step(123): len = 54085.1, overlap = 37.8438
PHY-3002 : Step(124): len = 54881.6, overlap = 35.1875
PHY-3002 : Step(125): len = 55507.6, overlap = 34
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000524143
PHY-3002 : Step(126): len = 55479.6, overlap = 34.0625
PHY-3002 : Step(127): len = 55641.3, overlap = 32.3125
PHY-3002 : Step(128): len = 55678.3, overlap = 32.125
PHY-3002 : Step(129): len = 55756.6, overlap = 31.7188
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7529, tnet num: 2111, tinst num: 1569, tnode num: 10687, tedge num: 12794.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 86.25 peak overflow 2.62
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2113.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58944, over cnt = 246(0%), over = 1033, worst = 23
PHY-1001 : End global iterations;  0.068902s wall, 0.093750s user + 0.031250s system = 0.125000s CPU (181.4%)

PHY-1001 : Congestion index: top1 = 44.81, top5 = 26.05, top10 = 17.25, top15 = 12.27.
PHY-1001 : End incremental global routing;  0.118330s wall, 0.140625s user + 0.031250s system = 0.171875s CPU (145.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2111 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.069738s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (112.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.216273s wall, 0.218750s user + 0.046875s system = 0.265625s CPU (122.8%)

OPT-1001 : Current memory(MB): used = 208, reserve = 174, peak = 208.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1643/2113.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58944, over cnt = 246(0%), over = 1033, worst = 23
PHY-1002 : len = 67520, over cnt = 146(0%), over = 259, worst = 13
PHY-1002 : len = 69728, over cnt = 34(0%), over = 41, worst = 4
PHY-1002 : len = 70432, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 70544, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.107001s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (131.4%)

PHY-1001 : Congestion index: top1 = 37.18, top5 = 25.19, top10 = 18.73, top15 = 13.91.
OPT-1001 : End congestion update;  0.152683s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (122.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2111 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058904s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (106.1%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.215180s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (116.2%)

OPT-1001 : Current memory(MB): used = 211, reserve = 178, peak = 211.
OPT-1001 : End physical optimization;  0.693047s wall, 0.750000s user + 0.062500s system = 0.812500s CPU (117.2%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 366 LUT to BLE ...
SYN-4008 : Packed 366 LUT and 186 SEQ to BLE.
SYN-4003 : Packing 765 remaining SEQ's ...
SYN-4005 : Packed 98 SEQ with LUT/SLICE
SYN-4006 : 101 single LUT's are left
SYN-4006 : 667 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1033/1351 primitive instances ...
PHY-3001 : End packing;  0.045624s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (102.7%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 799 instances
RUN-1001 : 374 mslices, 374 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1935 nets
RUN-1001 : 1370 nets have 2 pins
RUN-1001 : 460 nets have [3 - 5] pins
RUN-1001 : 62 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 797 instances, 748 slices, 21 macros(203 instances: 133 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 56096.8, Over = 60.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6313, tnet num: 1933, tinst num: 797, tnode num: 8607, tedge num: 11173.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1933 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.300280s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (98.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.67294e-05
PHY-3002 : Step(130): len = 55354, overlap = 61.5
PHY-3002 : Step(131): len = 54656.1, overlap = 61.75
PHY-3002 : Step(132): len = 53796.5, overlap = 63
PHY-3002 : Step(133): len = 53548.3, overlap = 62.5
PHY-3002 : Step(134): len = 53436.5, overlap = 61.75
PHY-3002 : Step(135): len = 53301.2, overlap = 61.75
PHY-3002 : Step(136): len = 52904, overlap = 61.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.34587e-05
PHY-3002 : Step(137): len = 53359.2, overlap = 60.75
PHY-3002 : Step(138): len = 54028.9, overlap = 60
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000146917
PHY-3002 : Step(139): len = 54708, overlap = 56.75
PHY-3002 : Step(140): len = 55430.1, overlap = 54
PHY-3002 : Step(141): len = 56381.4, overlap = 52
PHY-3002 : Step(142): len = 56519.8, overlap = 51.25
PHY-3001 : Before Legalized: Len = 56519.8
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.076918s wall, 0.062500s user + 0.156250s system = 0.218750s CPU (284.4%)

PHY-3001 : After Legalized: Len = 68363.5, Over = 0
PHY-3001 : Trial Legalized: Len = 68363.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1933 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.049776s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00116594
PHY-3002 : Step(143): len = 64417.8, overlap = 8.75
PHY-3002 : Step(144): len = 63228.7, overlap = 11.75
PHY-3002 : Step(145): len = 61750.6, overlap = 17.25
PHY-3002 : Step(146): len = 60897.6, overlap = 20.5
PHY-3002 : Step(147): len = 60053.4, overlap = 23
PHY-3002 : Step(148): len = 59664, overlap = 22.25
PHY-3002 : Step(149): len = 59414.2, overlap = 22.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00233188
PHY-3002 : Step(150): len = 59623.6, overlap = 22
PHY-3002 : Step(151): len = 59698.6, overlap = 22.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00466376
PHY-3002 : Step(152): len = 59762.4, overlap = 22.5
PHY-3002 : Step(153): len = 59818.7, overlap = 22.75
PHY-3001 : Before Legalized: Len = 59818.7
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005608s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (278.6%)

PHY-3001 : After Legalized: Len = 63934.4, Over = 0
PHY-3001 : Legalized: Len = 63934.4, Over = 0
PHY-3001 : Spreading special nets. 11 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006218s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 16 instances has been re-located, deltaX = 4, deltaY = 13, maxDist = 2.
PHY-3001 : Final: Len = 64290.4, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6313, tnet num: 1933, tinst num: 797, tnode num: 8607, tedge num: 11173.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 112/1935.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71584, over cnt = 154(0%), over = 235, worst = 6
PHY-1002 : len = 72560, over cnt = 74(0%), over = 96, worst = 4
PHY-1002 : len = 73600, over cnt = 14(0%), over = 14, worst = 1
PHY-1002 : len = 73808, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 73824, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.131875s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (154.0%)

PHY-1001 : Congestion index: top1 = 32.26, top5 = 23.25, top10 = 18.19, top15 = 14.26.
PHY-1001 : End incremental global routing;  0.189506s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (131.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1933 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.083251s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (93.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.300976s wall, 0.343750s user + 0.015625s system = 0.359375s CPU (119.4%)

OPT-1001 : Current memory(MB): used = 213, reserve = 181, peak = 213.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1723/1935.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73824, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007016s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (222.7%)

PHY-1001 : Congestion index: top1 = 32.26, top5 = 23.25, top10 = 18.19, top15 = 14.26.
OPT-1001 : End congestion update;  0.054924s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (113.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1933 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050531s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (92.8%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 757 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 797 instances, 748 slices, 21 macros(203 instances: 133 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 64225.8, Over = 0
PHY-3001 : End spreading;  0.005268s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 64225.8, Over = 0
PHY-3001 : End incremental legalization;  0.035311s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (88.5%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.154710s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (171.7%)

OPT-1001 : Current memory(MB): used = 217, reserve = 185, peak = 218.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1933 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050429s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1719/1935.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73744, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007124s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (219.3%)

PHY-1001 : Congestion index: top1 = 32.24, top5 = 23.28, top10 = 18.19, top15 = 14.26.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1933 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052564s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (89.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.793103
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.897468s wall, 1.031250s user + 0.031250s system = 1.062500s CPU (118.4%)

RUN-1003 : finish command "place" in  5.165209s wall, 7.968750s user + 3.562500s system = 11.531250s CPU (223.2%)

RUN-1004 : used memory is 196 MB, reserved memory is 163 MB, peak memory is 218 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 799 instances
RUN-1001 : 374 mslices, 374 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1935 nets
RUN-1001 : 1370 nets have 2 pins
RUN-1001 : 460 nets have [3 - 5] pins
RUN-1001 : 62 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6313, tnet num: 1933, tinst num: 797, tnode num: 8607, tedge num: 11173.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 374 mslices, 374 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1933 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69880, over cnt = 151(0%), over = 233, worst = 6
PHY-1002 : len = 70840, over cnt = 81(0%), over = 112, worst = 6
PHY-1002 : len = 72296, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72312, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.119695s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (130.5%)

PHY-1001 : Congestion index: top1 = 31.92, top5 = 22.91, top10 = 17.91, top15 = 13.95.
PHY-1001 : End global routing;  0.169593s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (129.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 235, reserve = 203, peak = 235.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 494, reserve = 466, peak = 494.
PHY-1001 : End build detailed router design. 3.171608s wall, 3.109375s user + 0.062500s system = 3.171875s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 30064, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.103245s wall, 1.078125s user + 0.015625s system = 1.093750s CPU (99.1%)

PHY-1001 : Current memory(MB): used = 526, reserve = 499, peak = 526.
PHY-1001 : End phase 1; 1.109887s wall, 1.078125s user + 0.031250s system = 1.109375s CPU (100.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 29% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 181144, over cnt = 23(0%), over = 23, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 529, reserve = 500, peak = 530.
PHY-1001 : End initial routed; 1.655925s wall, 2.593750s user + 0.140625s system = 2.734375s CPU (165.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1714(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.870   |  -44.351  |  21   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.338916s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (101.4%)

PHY-1001 : Current memory(MB): used = 530, reserve = 502, peak = 530.
PHY-1001 : End phase 2; 1.994930s wall, 2.937500s user + 0.140625s system = 3.078125s CPU (154.3%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 181144, over cnt = 23(0%), over = 23, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015732s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (99.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 181104, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.028379s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (110.1%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 181136, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.021047s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (74.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1714(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.870   |  -44.351  |  21   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.349436s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (102.8%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 5 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.170293s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (100.9%)

PHY-1001 : Current memory(MB): used = 545, reserve = 517, peak = 545.
PHY-1001 : End phase 3; 0.718307s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (100.1%)

PHY-1003 : Routed, final wirelength = 181136
PHY-1001 : Current memory(MB): used = 546, reserve = 517, peak = 546.
PHY-1001 : End export database. 0.009539s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  7.176756s wall, 8.015625s user + 0.250000s system = 8.265625s CPU (115.2%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6313, tnet num: 1933, tinst num: 797, tnode num: 8607, tedge num: 11173.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[26] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dia[8] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[3] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[6] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[7] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[5] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[0] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[0] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[1] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_58.mi[0] slack -2870ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_58.mi[1] slack -2738ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_62.mi[0] slack -2630ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_62.mi[1] slack -2738ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_65.mi[0] slack -2738ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_65.mi[1] slack -2726ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_68.mi[0] slack -2726ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_68.mi[1] slack -2858ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_71.mi[0] slack -2726ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_71.mi[1] slack -2724ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_74.mi[0] slack -2870ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_74.mi[1] slack -2724ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_77.mi[0] slack -2712ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_77.mi[1] slack -2738ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_79.mi[0] slack -2712ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin wendu/cur_state[0]_syn_3744.mi[0] slack -2858ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6429, tnet num: 1991, tinst num: 855, tnode num: 8723, tedge num: 11289.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_71_mi[0] slack -261ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_68_mi[1] slack -387ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_71_mi[1] slack -354ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_68_mi[0] slack -673ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_62_mi[0] slack -527ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_62_mi[1] slack -143ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg2_syn_65_mi[1] slack -396ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_65_mi[0] slack -502ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_58_mi[1] slack -4ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin wendu/cur_state[0]_syn_3744_mi[0] slack -251ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_79_mi[0] slack -177ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_77_mi[1] slack -648ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_74_mi[1] slack -832ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_74_mi[0] slack -177ps
RUN-1001 : End hold fix;  3.143406s wall, 3.109375s user + 0.265625s system = 3.375000s CPU (107.4%)

RUN-1003 : finish command "route" in  10.831837s wall, 11.671875s user + 0.515625s system = 12.187500s CPU (112.5%)

RUN-1004 : used memory is 500 MB, reserved memory is 475 MB, peak memory is 546 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      891   out of  19600    4.55%
#reg                     1051   out of  19600    5.36%
#le                      1558
  #lut only               507   out of   1558   32.54%
  #reg only               667   out of   1558   42.81%
  #lut&reg                384   out of   1558   24.65%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                                      Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0                       481
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3                       102
#3        wendu/clk_us                    GCLK               mslice             signal_process/trans/clk_out_n_syn_54.q0    38
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4                       1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di                             1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1558   |688     |203     |1084    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1060   |288     |141     |861     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |21     |14      |7       |18      |0       |0       |
|    demodu                  |Demodulation                                     |448    |102     |45      |353     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |56     |34      |6       |48      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |9       |0       |16      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |8       |0       |15      |0       |0       |
|    integ                   |Integration                                      |139    |24      |15      |111     |0       |0       |
|    modu                    |Modulation                                       |104    |54      |21      |100     |0       |1       |
|    rs422                   |Rs422Output                                      |315    |68      |46      |260     |0       |4       |
|    trans                   |SquareWaveGenerator                              |33     |26      |7       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |176    |133     |7       |114     |0       |0       |
|    U0                      |speed_select_Tx                                  |34     |27      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |26     |19      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |116    |87      |0       |82      |0       |0       |
|  wendu                     |DS18B20                                          |302    |257     |45      |75      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1389  
    #2          2       326   
    #3          3       105   
    #4          4        29   
    #5        5-10       67   
    #6        11-50      32   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6429, tnet num: 1991, tinst num: 855, tnode num: 8723, tedge num: 11289.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1991 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 855
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1993, pip num: 14761
BIT-1002 : Init feedthrough completely, num: 8
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1323 valid insts, and 39446 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.322938s wall, 18.468750s user + 0.156250s system = 18.625000s CPU (560.5%)

RUN-1004 : used memory is 517 MB, reserved memory is 492 MB, peak memory is 665 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250804_151033.log"
