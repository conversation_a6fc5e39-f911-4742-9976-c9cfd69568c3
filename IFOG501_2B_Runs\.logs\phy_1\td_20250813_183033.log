============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Wed Aug 13 18:30:33 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1575 instances
RUN-0007 : 368 luts, 950 seqs, 136 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2124 nets
RUN-1001 : 1552 nets have 2 pins
RUN-1001 : 471 nets have [3 - 5] pins
RUN-1001 : 58 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     246     
RUN-1001 :   No   |  No   |  Yes  |     125     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1573 instances, 368 luts, 950 seqs, 206 slices, 21 macros(206 instances: 136 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7560, tnet num: 2122, tinst num: 1573, tnode num: 10721, tedge num: 12867.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2122 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.268783s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (98.8%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 538116
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1573.
PHY-3001 : End clustering;  0.000020s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 460000, overlap = 20.25
PHY-3002 : Step(2): len = 433360, overlap = 18
PHY-3002 : Step(3): len = 416244, overlap = 20.25
PHY-3002 : Step(4): len = 404078, overlap = 13.5
PHY-3002 : Step(5): len = 389905, overlap = 18
PHY-3002 : Step(6): len = 374119, overlap = 13.5
PHY-3002 : Step(7): len = 366425, overlap = 15.75
PHY-3002 : Step(8): len = 360172, overlap = 15.75
PHY-3002 : Step(9): len = 347329, overlap = 15.75
PHY-3002 : Step(10): len = 340736, overlap = 11.25
PHY-3002 : Step(11): len = 333973, overlap = 13.5
PHY-3002 : Step(12): len = 326521, overlap = 13.5
PHY-3002 : Step(13): len = 317667, overlap = 15.75
PHY-3002 : Step(14): len = 313977, overlap = 13.5
PHY-3002 : Step(15): len = 304630, overlap = 15.75
PHY-3002 : Step(16): len = 297471, overlap = 15.75
PHY-3002 : Step(17): len = 292117, overlap = 15.75
PHY-3002 : Step(18): len = 287242, overlap = 15.75
PHY-3002 : Step(19): len = 276869, overlap = 15.75
PHY-3002 : Step(20): len = 271877, overlap = 15.75
PHY-3002 : Step(21): len = 267787, overlap = 15.75
PHY-3002 : Step(22): len = 261251, overlap = 15.75
PHY-3002 : Step(23): len = 248383, overlap = 13.5
PHY-3002 : Step(24): len = 245299, overlap = 18
PHY-3002 : Step(25): len = 240329, overlap = 20.25
PHY-3002 : Step(26): len = 233615, overlap = 20.25
PHY-3002 : Step(27): len = 225689, overlap = 20.25
PHY-3002 : Step(28): len = 222871, overlap = 20.25
PHY-3002 : Step(29): len = 215782, overlap = 20.25
PHY-3002 : Step(30): len = 210092, overlap = 20.25
PHY-3002 : Step(31): len = 204861, overlap = 20.25
PHY-3002 : Step(32): len = 201277, overlap = 20.25
PHY-3002 : Step(33): len = 193059, overlap = 20.25
PHY-3002 : Step(34): len = 189302, overlap = 20.25
PHY-3002 : Step(35): len = 185054, overlap = 20.25
PHY-3002 : Step(36): len = 180440, overlap = 20.25
PHY-3002 : Step(37): len = 173558, overlap = 20.25
PHY-3002 : Step(38): len = 169933, overlap = 20.25
PHY-3002 : Step(39): len = 165393, overlap = 20.25
PHY-3002 : Step(40): len = 161003, overlap = 20.25
PHY-3002 : Step(41): len = 156474, overlap = 20.25
PHY-3002 : Step(42): len = 153968, overlap = 20.25
PHY-3002 : Step(43): len = 144195, overlap = 20.25
PHY-3002 : Step(44): len = 137514, overlap = 20.25
PHY-3002 : Step(45): len = 134777, overlap = 20.25
PHY-3002 : Step(46): len = 132684, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.89427e-05
PHY-3002 : Step(47): len = 132926, overlap = 18
PHY-3002 : Step(48): len = 131696, overlap = 15.75
PHY-3002 : Step(49): len = 130815, overlap = 13.5
PHY-3002 : Step(50): len = 127738, overlap = 11.25
PHY-3002 : Step(51): len = 122463, overlap = 15.75
PHY-3002 : Step(52): len = 120770, overlap = 11.25
PHY-3002 : Step(53): len = 117402, overlap = 13.5
PHY-3002 : Step(54): len = 114406, overlap = 11.25
PHY-3002 : Step(55): len = 112911, overlap = 11.25
PHY-3002 : Step(56): len = 110085, overlap = 15.75
PHY-3002 : Step(57): len = 107468, overlap = 13.5
PHY-3002 : Step(58): len = 104791, overlap = 13.5
PHY-3002 : Step(59): len = 102762, overlap = 15.75
PHY-3002 : Step(60): len = 101847, overlap = 13.5
PHY-3002 : Step(61): len = 97554.6, overlap = 13.5
PHY-3002 : Step(62): len = 95771.3, overlap = 13.5
PHY-3002 : Step(63): len = 93240.9, overlap = 15.75
PHY-3002 : Step(64): len = 92590.3, overlap = 15.75
PHY-3002 : Step(65): len = 90151.6, overlap = 18
PHY-3002 : Step(66): len = 88824.8, overlap = 18
PHY-3002 : Step(67): len = 86269.5, overlap = 15.75
PHY-3002 : Step(68): len = 84506.2, overlap = 15.75
PHY-3002 : Step(69): len = 83186, overlap = 15.75
PHY-3002 : Step(70): len = 81468.1, overlap = 15.75
PHY-3002 : Step(71): len = 78031.4, overlap = 17.3125
PHY-3002 : Step(72): len = 76556.7, overlap = 20.0625
PHY-3002 : Step(73): len = 76333, overlap = 20.125
PHY-3002 : Step(74): len = 75342.2, overlap = 17.875
PHY-3002 : Step(75): len = 73657.7, overlap = 18.3125
PHY-3002 : Step(76): len = 73017.5, overlap = 18.3125
PHY-3002 : Step(77): len = 71816.1, overlap = 18.0625
PHY-3002 : Step(78): len = 70646.1, overlap = 18
PHY-3002 : Step(79): len = 70035.3, overlap = 18.0625
PHY-3002 : Step(80): len = 68998.6, overlap = 15.625
PHY-3002 : Step(81): len = 68255.5, overlap = 17.75
PHY-3002 : Step(82): len = 67578.4, overlap = 19.75
PHY-3002 : Step(83): len = 67474.7, overlap = 17.375
PHY-3002 : Step(84): len = 66808.9, overlap = 17.75
PHY-3002 : Step(85): len = 65829.8, overlap = 15.5
PHY-3002 : Step(86): len = 65291.6, overlap = 15.5625
PHY-3002 : Step(87): len = 65349.5, overlap = 15.5625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000197885
PHY-3002 : Step(88): len = 65632.7, overlap = 15.5
PHY-3002 : Step(89): len = 65692.1, overlap = 15.5
PHY-3002 : Step(90): len = 65650.2, overlap = 15.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000395771
PHY-3002 : Step(91): len = 65961.1, overlap = 15.5
PHY-3002 : Step(92): len = 66046.6, overlap = 15.5
PHY-3001 : Before Legalized: Len = 66046.6
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008171s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 70250.8, Over = 2
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2122 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.061371s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(93): len = 70227.4, overlap = 7.34375
PHY-3002 : Step(94): len = 70533.5, overlap = 6.40625
PHY-3002 : Step(95): len = 69331.6, overlap = 6.21875
PHY-3002 : Step(96): len = 69232.8, overlap = 6.59375
PHY-3002 : Step(97): len = 67699.1, overlap = 8.03125
PHY-3002 : Step(98): len = 67149.6, overlap = 8.40625
PHY-3002 : Step(99): len = 66809.1, overlap = 9.21875
PHY-3002 : Step(100): len = 66106.5, overlap = 9.59375
PHY-3002 : Step(101): len = 65026.3, overlap = 9.15625
PHY-3002 : Step(102): len = 64338.4, overlap = 9.59375
PHY-3002 : Step(103): len = 63278.6, overlap = 9.65625
PHY-3002 : Step(104): len = 62795.5, overlap = 9.90625
PHY-3002 : Step(105): len = 62055.2, overlap = 10.0938
PHY-3002 : Step(106): len = 61701.7, overlap = 10.7812
PHY-3002 : Step(107): len = 61320.4, overlap = 10.8438
PHY-3002 : Step(108): len = 61119.1, overlap = 10.5938
PHY-3002 : Step(109): len = 60584.6, overlap = 10.5938
PHY-3002 : Step(110): len = 60479.8, overlap = 10.6562
PHY-3002 : Step(111): len = 60148.3, overlap = 9.96875
PHY-3002 : Step(112): len = 59006.7, overlap = 10.7812
PHY-3002 : Step(113): len = 58233.7, overlap = 11.25
PHY-3002 : Step(114): len = 57839.2, overlap = 14.6875
PHY-3002 : Step(115): len = 57749.5, overlap = 13.9688
PHY-3002 : Step(116): len = 56726.7, overlap = 13.0312
PHY-3002 : Step(117): len = 56601.9, overlap = 13
PHY-3002 : Step(118): len = 56154.9, overlap = 13
PHY-3002 : Step(119): len = 55869.3, overlap = 12.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000768278
PHY-3002 : Step(120): len = 55772.4, overlap = 11.9688
PHY-3002 : Step(121): len = 55853.8, overlap = 11.1875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00153656
PHY-3002 : Step(122): len = 55751.3, overlap = 10.9688
PHY-3002 : Step(123): len = 55711.8, overlap = 10.7812
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2122 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.067099s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (93.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000114744
PHY-3002 : Step(124): len = 55942.1, overlap = 49.0625
PHY-3002 : Step(125): len = 56671, overlap = 44.9062
PHY-3002 : Step(126): len = 57424.5, overlap = 43.4375
PHY-3002 : Step(127): len = 57395.1, overlap = 39.8125
PHY-3002 : Step(128): len = 57030.6, overlap = 39.8438
PHY-3002 : Step(129): len = 56268.6, overlap = 39.3125
PHY-3002 : Step(130): len = 56156.2, overlap = 40.5312
PHY-3002 : Step(131): len = 56265.3, overlap = 38.4688
PHY-3002 : Step(132): len = 56429.4, overlap = 32.5
PHY-3002 : Step(133): len = 56503.7, overlap = 29.4375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000229488
PHY-3002 : Step(134): len = 56170.5, overlap = 31.2812
PHY-3002 : Step(135): len = 55764.5, overlap = 30.4062
PHY-3002 : Step(136): len = 55816.8, overlap = 29.875
PHY-3002 : Step(137): len = 55861.5, overlap = 30.1562
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000458975
PHY-3002 : Step(138): len = 56158.1, overlap = 29.5938
PHY-3002 : Step(139): len = 56948.5, overlap = 26.7812
PHY-3002 : Step(140): len = 57380.4, overlap = 27.2188
PHY-3002 : Step(141): len = 57627.4, overlap = 24
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7560, tnet num: 2122, tinst num: 1573, tnode num: 10721, tedge num: 12867.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 90.88 peak overflow 2.62
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2124.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 60216, over cnt = 245(0%), over = 986, worst = 19
PHY-1001 : End global iterations;  0.071329s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (65.7%)

PHY-1001 : Congestion index: top1 = 42.78, top5 = 25.67, top10 = 16.69, top15 = 11.98.
PHY-1001 : End incremental global routing;  0.122090s wall, 0.093750s user + 0.015625s system = 0.109375s CPU (89.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2122 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.072608s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (107.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.223959s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (90.7%)

OPT-1001 : Current memory(MB): used = 208, reserve = 175, peak = 208.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1654/2124.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 60216, over cnt = 245(0%), over = 986, worst = 19
PHY-1002 : len = 66096, over cnt = 177(0%), over = 455, worst = 15
PHY-1002 : len = 70640, over cnt = 85(0%), over = 148, worst = 13
PHY-1002 : len = 72784, over cnt = 15(0%), over = 18, worst = 4
PHY-1002 : len = 73032, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.088278s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (123.9%)

PHY-1001 : Congestion index: top1 = 38.00, top5 = 25.47, top10 = 18.45, top15 = 13.83.
OPT-1001 : End congestion update;  0.131205s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (119.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2122 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061549s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.5%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.196476s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (111.3%)

OPT-1001 : Current memory(MB): used = 211, reserve = 178, peak = 211.
OPT-1001 : End physical optimization;  0.667981s wall, 0.656250s user + 0.015625s system = 0.671875s CPU (100.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 368 LUT to BLE ...
SYN-4008 : Packed 368 LUT and 190 SEQ to BLE.
SYN-4003 : Packing 760 remaining SEQ's ...
SYN-4005 : Packed 89 SEQ with LUT/SLICE
SYN-4006 : 101 single LUT's are left
SYN-4006 : 671 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1039/1360 primitive instances ...
PHY-3001 : End packing;  0.047142s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (99.4%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 803 instances
RUN-1001 : 376 mslices, 376 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1942 nets
RUN-1001 : 1373 nets have 2 pins
RUN-1001 : 466 nets have [3 - 5] pins
RUN-1001 : 59 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 801 instances, 752 slices, 21 macros(206 instances: 136 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 57506.6, Over = 49.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6344, tnet num: 1940, tinst num: 801, tnode num: 8633, tedge num: 11257.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1940 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.294891s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (100.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.7901e-05
PHY-3002 : Step(142): len = 56852.7, overlap = 48.75
PHY-3002 : Step(143): len = 56506.8, overlap = 48
PHY-3002 : Step(144): len = 56249.3, overlap = 48.25
PHY-3002 : Step(145): len = 56151.8, overlap = 49
PHY-3002 : Step(146): len = 56198.2, overlap = 46.5
PHY-3002 : Step(147): len = 56163.1, overlap = 46.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.58021e-05
PHY-3002 : Step(148): len = 56515.1, overlap = 46.5
PHY-3002 : Step(149): len = 56523, overlap = 44.5
PHY-3002 : Step(150): len = 56972.9, overlap = 44.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000191604
PHY-3002 : Step(151): len = 57695.1, overlap = 43.5
PHY-3002 : Step(152): len = 57883.2, overlap = 42.75
PHY-3001 : Before Legalized: Len = 57883.2
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.084800s wall, 0.078125s user + 0.093750s system = 0.171875s CPU (202.7%)

PHY-3001 : After Legalized: Len = 70370.9, Over = 0
PHY-3001 : Trial Legalized: Len = 70370.9
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1940 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.048503s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (128.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00118145
PHY-3002 : Step(153): len = 66968.2, overlap = 5
PHY-3002 : Step(154): len = 64921.9, overlap = 9.5
PHY-3002 : Step(155): len = 62756.1, overlap = 13
PHY-3002 : Step(156): len = 61855, overlap = 17.75
PHY-3002 : Step(157): len = 61206.9, overlap = 20.5
PHY-3002 : Step(158): len = 60823.6, overlap = 21.25
PHY-3002 : Step(159): len = 60400.6, overlap = 23.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00236289
PHY-3002 : Step(160): len = 60556.6, overlap = 23.5
PHY-3002 : Step(161): len = 60577.7, overlap = 24.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00472579
PHY-3002 : Step(162): len = 60659.3, overlap = 24.75
PHY-3002 : Step(163): len = 60659.3, overlap = 24.75
PHY-3001 : Before Legalized: Len = 60659.3
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005080s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 65355.5, Over = 0
PHY-3001 : Legalized: Len = 65355.5, Over = 0
PHY-3001 : Spreading special nets. 9 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005867s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (266.3%)

PHY-3001 : 17 instances has been re-located, deltaX = 7, deltaY = 10, maxDist = 1.
PHY-3001 : Final: Len = 65515.5, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6344, tnet num: 1940, tinst num: 801, tnode num: 8633, tedge num: 11257.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 66/1942.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71656, over cnt = 139(0%), over = 220, worst = 7
PHY-1002 : len = 72608, over cnt = 78(0%), over = 95, worst = 4
PHY-1002 : len = 73696, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 73776, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.109992s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (113.6%)

PHY-1001 : Congestion index: top1 = 31.62, top5 = 22.64, top10 = 17.40, top15 = 13.66.
PHY-1001 : End incremental global routing;  0.162101s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (106.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1940 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060658s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.251768s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (105.5%)

OPT-1001 : Current memory(MB): used = 213, reserve = 181, peak = 214.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1719/1942.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73776, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006726s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.62, top5 = 22.64, top10 = 17.40, top15 = 13.66.
OPT-1001 : End congestion update;  0.055194s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (84.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1940 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050641s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.6%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 761 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 801 instances, 752 slices, 21 macros(206 instances: 136 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 65457.6, Over = 0
PHY-3001 : Spreading special nets. 1 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.004895s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (319.2%)

PHY-3001 : 1 instances has been re-located, deltaX = 1, deltaY = 1, maxDist = 2.
PHY-3001 : Final: Len = 65513.6, Over = 0
PHY-3001 : End incremental legalization;  0.036297s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (86.1%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 2 cells processed and 150 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.155072s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (100.8%)

OPT-1001 : Current memory(MB): used = 217, reserve = 185, peak = 217.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1940 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052047s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (90.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1711/1942.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73776, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.009173s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.62, top5 = 22.66, top10 = 17.41, top15 = 13.65.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1940 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047871s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.9%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.172414
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.839439s wall, 0.859375s user + 0.031250s system = 0.890625s CPU (106.1%)

RUN-1003 : finish command "place" in  5.163156s wall, 6.968750s user + 2.953125s system = 9.921875s CPU (192.2%)

RUN-1004 : used memory is 195 MB, reserved memory is 161 MB, peak memory is 217 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 803 instances
RUN-1001 : 376 mslices, 376 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1942 nets
RUN-1001 : 1373 nets have 2 pins
RUN-1001 : 466 nets have [3 - 5] pins
RUN-1001 : 59 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6344, tnet num: 1940, tinst num: 801, tnode num: 8633, tedge num: 11257.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 376 mslices, 376 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1940 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70632, over cnt = 138(0%), over = 221, worst = 7
PHY-1002 : len = 71736, over cnt = 81(0%), over = 99, worst = 3
PHY-1002 : len = 72560, over cnt = 33(0%), over = 38, worst = 2
PHY-1002 : len = 73168, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.110705s wall, 0.171875s user + 0.031250s system = 0.203125s CPU (183.5%)

PHY-1001 : Congestion index: top1 = 31.70, top5 = 22.49, top10 = 17.31, top15 = 13.56.
PHY-1001 : End global routing;  0.160708s wall, 0.234375s user + 0.031250s system = 0.265625s CPU (165.3%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 233, reserve = 201, peak = 234.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 492, reserve = 463, peak = 492.
PHY-1001 : End build detailed router design. 3.147861s wall, 3.093750s user + 0.062500s system = 3.156250s CPU (100.3%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 31032, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.095153s wall, 1.046875s user + 0.000000s system = 1.046875s CPU (95.6%)

PHY-1001 : Current memory(MB): used = 524, reserve = 496, peak = 524.
PHY-1001 : End phase 1; 1.101078s wall, 1.046875s user + 0.000000s system = 1.046875s CPU (95.1%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 38% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 182280, over cnt = 29(0%), over = 29, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 527, reserve = 498, peak = 528.
PHY-1001 : End initial routed; 1.699371s wall, 2.859375s user + 0.187500s system = 3.046875s CPU (179.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1718(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.720   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.003   |  -46.799  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.341741s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (100.6%)

PHY-1001 : Current memory(MB): used = 529, reserve = 500, peak = 529.
PHY-1001 : End phase 2; 2.041205s wall, 3.203125s user + 0.187500s system = 3.390625s CPU (166.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 182280, over cnt = 29(0%), over = 29, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014963s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (104.4%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 182224, over cnt = 6(0%), over = 6, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.025821s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (60.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 182240, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.021548s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (72.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1718(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.720   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.003   |  -46.799  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.351823s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (102.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 6 feed throughs used by 6 nets
PHY-1001 : End commit to database; 0.169394s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (101.5%)

PHY-1001 : Current memory(MB): used = 546, reserve = 517, peak = 546.
PHY-1001 : End phase 3; 0.713851s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (100.7%)

PHY-1003 : Routed, final wirelength = 182240
PHY-1001 : Current memory(MB): used = 546, reserve = 518, peak = 546.
PHY-1001 : End export database. 0.010555s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  7.193551s wall, 8.218750s user + 0.265625s system = 8.484375s CPU (117.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6344, tnet num: 1940, tinst num: 801, tnode num: 8633, tedge num: 11257.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[40] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[4] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[6] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[7] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[4] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[15] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[17] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[8] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/crc_data_b1[2]_syn_8.mi[0] slack -2763ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_66.mi[0] slack -2991ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_66.mi[1] slack -2991ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_70.mi[0] slack -2871ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_70.mi[1] slack -2991ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_73.mi[0] slack -2763ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_73.mi[1] slack -2991ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_76.mi[0] slack -2871ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_76.mi[1] slack -2871ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_79.mi[0] slack -3003ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_79.mi[1] slack -3003ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_82.mi[0] slack -2845ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_82.mi[1] slack -2845ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_85.mi[0] slack -3003ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_85.mi[1] slack -2991ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_87.mi[0] slack -2871ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6462, tnet num: 1999, tinst num: 860, tnode num: 8751, tedge num: 11375.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/crc_data_b1[2]_syn_8_mi[0] slack -299ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_70_mi[1] slack -342ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_70_mi[0] slack -582ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_73_mi[0] slack -460ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_73_mi[1] slack -171ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_79_mi[1] slack -662ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_79_mi[0] slack -486ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_76_mi[1] slack -440ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_87_mi[0] slack -342ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_82_mi[1] slack -616ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_85_mi[1] slack -306ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_85_mi[0] slack -178ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_82_mi[0] slack -226ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_66_mi[1] slack -688ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg2_syn_66_mi[0] slack -816ps
RUN-1001 : End hold fix;  3.064971s wall, 2.921875s user + 0.156250s system = 3.078125s CPU (100.4%)

RUN-1003 : finish command "route" in  10.749581s wall, 11.703125s user + 0.453125s system = 12.156250s CPU (113.1%)

RUN-1004 : used memory is 500 MB, reserved memory is 470 MB, peak memory is 546 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   15
  #output                  21
  #inout                    1

Utilization Statistics
#lut                      905   out of  19600    4.62%
#reg                     1052   out of  19600    5.37%
#le                      1576
  #lut only               524   out of   1576   33.25%
  #reg only               671   out of   1576   42.58%
  #lut&reg                381   out of   1576   24.18%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    13
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                      Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0       478
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3       100
#3        wendu/clk_us                    GCLK               lslice             wendu/cnt_b[2]_syn_10.q0    40
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4       1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di             1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
  RxTransmit       INPUT        M14        LVCMOS33          N/A          PULLUP      IREG    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1576   |699     |206     |1086    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1075   |294     |142     |862     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |23     |19      |4       |20      |0       |0       |
|    demodu                  |Demodulation                                     |454    |99      |46      |350     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |54     |29      |6       |44      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |14     |7       |0       |14      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |7       |0       |15      |0       |0       |
|    integ                   |Integration                                      |143    |19      |15      |115     |0       |0       |
|    modu                    |Modulation                                       |103    |54      |23      |99      |0       |1       |
|    rs422                   |Rs422Output                                      |316    |75      |46      |257     |0       |4       |
|    trans                   |SquareWaveGenerator                              |36     |28      |8       |21      |0       |0       |
|  u_uart                    |UART_Control                                     |169    |130     |7       |117     |0       |0       |
|    U0                      |speed_select_Tx                                  |34     |27      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |25     |18      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |110    |85      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |308    |263     |45      |72      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1393  
    #2          2       330   
    #3          3       100   
    #4          4        36   
    #5        5-10       64   
    #6        11-50      33   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6462, tnet num: 1999, tinst num: 860, tnode num: 8751, tedge num: 11375.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1999 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 860
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2001, pip num: 14728
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 9
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1373 valid insts, and 39540 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.317838s wall, 18.984375s user + 0.062500s system = 19.046875s CPU (574.1%)

RUN-1004 : used memory is 520 MB, reserved memory is 492 MB, peak memory is 666 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250813_183033.log"
