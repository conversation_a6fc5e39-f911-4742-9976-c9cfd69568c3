============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Wed Aug 13 17:35:32 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 33 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 827 instances
RUN-0007 : 231 luts, 438 seqs, 73 mslices, 40 lslices, 37 pads, 2 brams, 1 dsps
RUN-1001 : There are total 1078 nets
RUN-1001 : 785 nets have 2 pins
RUN-1001 : 211 nets have [3 - 5] pins
RUN-1001 : 60 nets have [6 - 10] pins
RUN-1001 : 6 nets have [11 - 20] pins
RUN-1001 : 12 nets have [21 - 99] pins
RUN-1001 : 4 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     138     
RUN-1001 :   No   |  No   |  Yes  |     117     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     131     
RUN-1001 :   Yes  |  No   |  Yes  |     25      
RUN-1001 :   Yes  |  Yes  |  No   |     26      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |   8   |     5      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 15
PHY-3001 : Initial placement ...
PHY-3001 : design contains 825 instances, 231 luts, 438 seqs, 113 slices, 16 macros(113 instances: 73 mslices 40 lslices)
PHY-0007 : Cell area utilization is 2%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 3729, tnet num: 1076, tinst num: 825, tnode num: 5110, tedge num: 6186.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 1076 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.228460s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (102.6%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 273649
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 825.
PHY-3001 : End clustering;  0.000059s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 2%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 240483, overlap = 6.75
PHY-3002 : Step(2): len = 221432, overlap = 6.75
PHY-3002 : Step(3): len = 209000, overlap = 6.75
PHY-3002 : Step(4): len = 198372, overlap = 6.75
PHY-3002 : Step(5): len = 192116, overlap = 6.75
PHY-3002 : Step(6): len = 183954, overlap = 6.75
PHY-3002 : Step(7): len = 176434, overlap = 6.75
PHY-3002 : Step(8): len = 170334, overlap = 6.75
PHY-3002 : Step(9): len = 163936, overlap = 6.75
PHY-3002 : Step(10): len = 156930, overlap = 6.75
PHY-3002 : Step(11): len = 152284, overlap = 6.75
PHY-3002 : Step(12): len = 145872, overlap = 6.75
PHY-3002 : Step(13): len = 140122, overlap = 6.75
PHY-3002 : Step(14): len = 136088, overlap = 6.75
PHY-3002 : Step(15): len = 130868, overlap = 6.75
PHY-3002 : Step(16): len = 125383, overlap = 6.75
PHY-3002 : Step(17): len = 122850, overlap = 6.75
PHY-3002 : Step(18): len = 119000, overlap = 6.75
PHY-3002 : Step(19): len = 112122, overlap = 6.75
PHY-3002 : Step(20): len = 109710, overlap = 6.75
PHY-3002 : Step(21): len = 107168, overlap = 6.75
PHY-3002 : Step(22): len = 101425, overlap = 6.75
PHY-3002 : Step(23): len = 98593, overlap = 6.75
PHY-3002 : Step(24): len = 97193.8, overlap = 6.75
PHY-3002 : Step(25): len = 91882.6, overlap = 6.75
PHY-3002 : Step(26): len = 85074.1, overlap = 6.75
PHY-3002 : Step(27): len = 83521, overlap = 6.75
PHY-3002 : Step(28): len = 81997.6, overlap = 6.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000502015
PHY-3002 : Step(29): len = 81687, overlap = 6.75
PHY-3002 : Step(30): len = 81460, overlap = 6.75
PHY-3002 : Step(31): len = 81182.2, overlap = 6.75
PHY-3002 : Step(32): len = 79792.1, overlap = 4.5
PHY-3002 : Step(33): len = 78580.1, overlap = 2.25
PHY-3002 : Step(34): len = 75714.4, overlap = 4.5
PHY-3002 : Step(35): len = 74670.3, overlap = 4.5
PHY-3002 : Step(36): len = 74077, overlap = 4.5
PHY-3002 : Step(37): len = 72819.2, overlap = 4.5
PHY-3002 : Step(38): len = 70565.2, overlap = 4.5
PHY-3002 : Step(39): len = 68292.1, overlap = 0
PHY-3002 : Step(40): len = 67494.8, overlap = 4.5
PHY-3002 : Step(41): len = 66841.9, overlap = 2.25
PHY-3002 : Step(42): len = 64916.8, overlap = 2.25
PHY-3002 : Step(43): len = 62259.9, overlap = 2.25
PHY-3002 : Step(44): len = 61097.8, overlap = 4.5
PHY-3002 : Step(45): len = 60051, overlap = 4.5
PHY-3002 : Step(46): len = 58495.2, overlap = 2.25
PHY-3002 : Step(47): len = 57585.3, overlap = 4.5
PHY-3002 : Step(48): len = 56632.6, overlap = 2.25
PHY-3002 : Step(49): len = 55774.8, overlap = 4.5
PHY-3002 : Step(50): len = 54536.2, overlap = 0
PHY-3002 : Step(51): len = 52545.8, overlap = 4.5
PHY-3002 : Step(52): len = 51030.4, overlap = 2.25
PHY-3002 : Step(53): len = 50171.7, overlap = 4.5
PHY-3002 : Step(54): len = 49848.2, overlap = 2.25
PHY-3002 : Step(55): len = 46880.9, overlap = 2.25
PHY-3002 : Step(56): len = 45039.4, overlap = 2.25
PHY-3002 : Step(57): len = 44371.9, overlap = 4.5
PHY-3002 : Step(58): len = 43970.4, overlap = 2.25
PHY-3002 : Step(59): len = 43647, overlap = 4.5
PHY-3002 : Step(60): len = 43349.6, overlap = 4.5
PHY-3002 : Step(61): len = 43088.6, overlap = 4.5
PHY-3002 : Step(62): len = 42799.6, overlap = 2.25
PHY-3002 : Step(63): len = 42041, overlap = 2.25
PHY-3002 : Step(64): len = 41014.8, overlap = 4.5
PHY-3002 : Step(65): len = 40924.2, overlap = 4.5
PHY-3002 : Step(66): len = 40506.4, overlap = 4.5
PHY-3002 : Step(67): len = 40077.2, overlap = 2.25
PHY-3002 : Step(68): len = 39455.8, overlap = 4.5
PHY-3002 : Step(69): len = 38872.6, overlap = 4.5
PHY-3002 : Step(70): len = 38449.3, overlap = 4.5
PHY-3002 : Step(71): len = 38001.4, overlap = 4.5
PHY-3002 : Step(72): len = 37998.6, overlap = 2.25
PHY-3002 : Step(73): len = 37980.6, overlap = 2.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00100403
PHY-3002 : Step(74): len = 37800.9, overlap = 4.5
PHY-3002 : Step(75): len = 37718.5, overlap = 4.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00200806
PHY-3002 : Step(76): len = 37784.4, overlap = 4.5
PHY-3002 : Step(77): len = 37784.8, overlap = 2.25
PHY-3001 : Before Legalized: Len = 37784.8
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005436s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (287.5%)

PHY-3001 : After Legalized: Len = 40118.2, Over = 0
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 3%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1076 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.027811s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (112.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(78): len = 40402.5, overlap = 0.0625
PHY-3002 : Step(79): len = 40712.2, overlap = 0.0625
PHY-3002 : Step(80): len = 39726.8, overlap = 0.125
PHY-3002 : Step(81): len = 40164.5, overlap = 0
PHY-3002 : Step(82): len = 40103, overlap = 1
PHY-3002 : Step(83): len = 39323.7, overlap = 1.25
PHY-3002 : Step(84): len = 38461.9, overlap = 1.75
PHY-3002 : Step(85): len = 37631.9, overlap = 1.8125
PHY-3002 : Step(86): len = 37224.9, overlap = 2.1875
PHY-3002 : Step(87): len = 36749.2, overlap = 2.5
PHY-3002 : Step(88): len = 35946.8, overlap = 2.0625
PHY-3002 : Step(89): len = 34866.6, overlap = 2.28125
PHY-3002 : Step(90): len = 34887.4, overlap = 2.71875
PHY-3002 : Step(91): len = 34679.6, overlap = 3.40625
PHY-3002 : Step(92): len = 34110.5, overlap = 3.40625
PHY-3002 : Step(93): len = 34298.7, overlap = 3.5
PHY-3002 : Step(94): len = 34438.4, overlap = 3.9375
PHY-3002 : Step(95): len = 34333, overlap = 4.5625
PHY-3002 : Step(96): len = 34020.3, overlap = 4.65625
PHY-3002 : Step(97): len = 33246.1, overlap = 4.875
PHY-3002 : Step(98): len = 32780.4, overlap = 6.3125
PHY-3002 : Step(99): len = 32628.3, overlap = 6.59375
PHY-3002 : Step(100): len = 32228.3, overlap = 6.78125
PHY-3002 : Step(101): len = 32226.6, overlap = 7.4375
PHY-3002 : Step(102): len = 32277.4, overlap = 7.625
PHY-3002 : Step(103): len = 32201.9, overlap = 7.5625
PHY-3002 : Step(104): len = 32242.7, overlap = 7.65625
PHY-3002 : Step(105): len = 32145.4, overlap = 7.65625
PHY-3002 : Step(106): len = 32029.2, overlap = 7.5625
PHY-3002 : Step(107): len = 31741.6, overlap = 7.5625
PHY-3002 : Step(108): len = 31770.2, overlap = 7.5625
PHY-3002 : Step(109): len = 31323.4, overlap = 7.5625
PHY-3002 : Step(110): len = 31110.7, overlap = 7.46875
PHY-3002 : Step(111): len = 30977.8, overlap = 7.46875
PHY-3002 : Step(112): len = 30932, overlap = 7.46875
PHY-3002 : Step(113): len = 30954.4, overlap = 7.46875
PHY-3002 : Step(114): len = 30871.1, overlap = 7.5625
PHY-3002 : Step(115): len = 30765.1, overlap = 7.65625
PHY-3002 : Step(116): len = 30822.2, overlap = 7.65625
PHY-3002 : Step(117): len = 30838.8, overlap = 7.65625
PHY-3002 : Step(118): len = 30883.7, overlap = 7.65625
PHY-3002 : Step(119): len = 30681.5, overlap = 7.65625
PHY-3002 : Step(120): len = 30631.4, overlap = 7.65625
PHY-3002 : Step(121): len = 30644.8, overlap = 7.65625
PHY-3002 : Step(122): len = 30621.1, overlap = 7.375
PHY-3002 : Step(123): len = 30546.1, overlap = 7.375
PHY-3002 : Step(124): len = 30529.4, overlap = 7.375
PHY-3002 : Step(125): len = 30542.1, overlap = 6.84375
PHY-3002 : Step(126): len = 30531.4, overlap = 6.75
PHY-3002 : Step(127): len = 30458.4, overlap = 6.75
PHY-3002 : Step(128): len = 30453.7, overlap = 6.40625
PHY-3002 : Step(129): len = 30485.7, overlap = 6.40625
PHY-3002 : Step(130): len = 30442.5, overlap = 6.3125
PHY-3002 : Step(131): len = 30479.2, overlap = 6.21875
PHY-3002 : Step(132): len = 30525.1, overlap = 6.03125
PHY-3002 : Step(133): len = 30462.1, overlap = 5.9375
PHY-3002 : Step(134): len = 30421.1, overlap = 5.40625
PHY-3002 : Step(135): len = 30196.6, overlap = 5.21875
PHY-3002 : Step(136): len = 30236.8, overlap = 5.03125
PHY-3002 : Step(137): len = 30206.6, overlap = 4.8125
PHY-3002 : Step(138): len = 30147.6, overlap = 4.53125
PHY-3002 : Step(139): len = 30097.8, overlap = 4.53125
PHY-3002 : Step(140): len = 30081.4, overlap = 4.25
PHY-3002 : Step(141): len = 30110.2, overlap = 4.25
PHY-3002 : Step(142): len = 30109, overlap = 3.375
PHY-3002 : Step(143): len = 30137.3, overlap = 2.53125
PHY-3002 : Step(144): len = 30083.5, overlap = 1
PHY-3002 : Step(145): len = 29989.5, overlap = 1
PHY-3002 : Step(146): len = 29989.5, overlap = 1
PHY-3002 : Step(147): len = 30009.1, overlap = 1
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 3%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1076 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.030424s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (102.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.69653e-05
PHY-3002 : Step(148): len = 29986.3, overlap = 21.5625
PHY-3002 : Step(149): len = 30438.4, overlap = 20.5312
PHY-3002 : Step(150): len = 30732, overlap = 21.2188
PHY-3002 : Step(151): len = 30837.2, overlap = 21.5625
PHY-3002 : Step(152): len = 31052.2, overlap = 20.6562
PHY-3002 : Step(153): len = 31325.9, overlap = 19.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000153931
PHY-3002 : Step(154): len = 31424.5, overlap = 19.2188
PHY-3002 : Step(155): len = 31717.3, overlap = 17.7188
PHY-3002 : Step(156): len = 32298, overlap = 16.9688
PHY-3002 : Step(157): len = 32490.3, overlap = 16.7188
PHY-3002 : Step(158): len = 32164.1, overlap = 17.2188
PHY-3002 : Step(159): len = 32106.7, overlap = 17.125
PHY-3002 : Step(160): len = 32112.1, overlap = 16.4688
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000307861
PHY-3002 : Step(161): len = 32152.5, overlap = 16.6562
PHY-3002 : Step(162): len = 32331.7, overlap = 16.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000615722
PHY-3002 : Step(163): len = 32375.4, overlap = 16.5625
PHY-3002 : Step(164): len = 32573.2, overlap = 15.2188
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 3729, tnet num: 1076, tinst num: 825, tnode num: 5110, tedge num: 6186.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 41.03 peak overflow 4.28
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/1078.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 35216, over cnt = 101(0%), over = 366, worst = 14
PHY-1001 : End global iterations;  0.035776s wall, 0.015625s user + 0.046875s system = 0.062500s CPU (174.7%)

PHY-1001 : Congestion index: top1 = 28.71, top5 = 15.17, top10 = 9.06, top15 = 6.44.
PHY-1001 : End incremental global routing;  0.080092s wall, 0.062500s user + 0.046875s system = 0.109375s CPU (136.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1076 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.029969s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (104.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.123665s wall, 0.093750s user + 0.046875s system = 0.140625s CPU (113.7%)

OPT-1001 : Current memory(MB): used = 187, reserve = 153, peak = 187.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 805/1078.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 35216, over cnt = 101(0%), over = 366, worst = 14
PHY-1002 : len = 37944, over cnt = 59(0%), over = 112, worst = 7
PHY-1002 : len = 38520, over cnt = 23(0%), over = 52, worst = 6
PHY-1002 : len = 39064, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 39080, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.043901s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (142.4%)

PHY-1001 : Congestion index: top1 = 27.31, top5 = 15.36, top10 = 9.77, top15 = 7.07.
OPT-1001 : End congestion update;  0.083917s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (130.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1076 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.024085s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (64.9%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.109312s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (114.4%)

OPT-1001 : Current memory(MB): used = 189, reserve = 155, peak = 189.
OPT-1001 : End physical optimization;  0.422881s wall, 0.406250s user + 0.062500s system = 0.468750s CPU (110.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 231 LUT to BLE ...
SYN-4008 : Packed 231 LUT and 130 SEQ to BLE.
SYN-4003 : Packing 308 remaining SEQ's ...
SYN-4005 : Packed 45 SEQ with LUT/SLICE
SYN-4006 : 72 single LUT's are left
SYN-4006 : 263 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 494/716 primitive instances ...
PHY-3001 : End packing;  0.021719s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (71.9%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 428 instances
RUN-1001 : 191 mslices, 192 lslices, 37 pads, 2 brams, 1 dsps
RUN-1001 : There are total 955 nets
RUN-1001 : 669 nets have 2 pins
RUN-1001 : 204 nets have [3 - 5] pins
RUN-1001 : 60 nets have [6 - 10] pins
RUN-1001 : 15 nets have [11 - 20] pins
RUN-1001 : 5 nets have [21 - 99] pins
RUN-1001 : 2 nets have 100+ pins
PHY-3001 : design contains 426 instances, 383 slices, 16 macros(113 instances: 73 mslices 40 lslices)
PHY-3001 : Cell area utilization is 5%
PHY-3001 : After packing: Len = 32198.8, Over = 26.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 5%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 3150, tnet num: 953, tinst num: 426, tnode num: 4205, tedge num: 5476.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 953 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.202205s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (100.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.24825e-05
PHY-3002 : Step(165): len = 32178.4, overlap = 25
PHY-3002 : Step(166): len = 32378.5, overlap = 25.25
PHY-3002 : Step(167): len = 32492.6, overlap = 21.75
PHY-3002 : Step(168): len = 32386.5, overlap = 20.75
PHY-3002 : Step(169): len = 32383.9, overlap = 20.25
PHY-3002 : Step(170): len = 32485.9, overlap = 19.25
PHY-3002 : Step(171): len = 32413.7, overlap = 18.75
PHY-3002 : Step(172): len = 32245.9, overlap = 19.25
PHY-3002 : Step(173): len = 32267.5, overlap = 21
PHY-3002 : Step(174): len = 32331.6, overlap = 19.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000144965
PHY-3002 : Step(175): len = 32360.7, overlap = 19.75
PHY-3002 : Step(176): len = 32428.5, overlap = 18.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00028993
PHY-3002 : Step(177): len = 32573, overlap = 17.5
PHY-3002 : Step(178): len = 32768.9, overlap = 17
PHY-3002 : Step(179): len = 32828.2, overlap = 18
PHY-3001 : Before Legalized: Len = 32828.2
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.078115s wall, 0.062500s user + 0.062500s system = 0.125000s CPU (160.0%)

PHY-3001 : After Legalized: Len = 39298.4, Over = 0
PHY-3001 : Trial Legalized: Len = 39298.4
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 5%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 953 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.022790s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (137.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.584847
PHY-3002 : Step(180): len = 38146.7, overlap = 0.75
PHY-3002 : Step(181): len = 37520.8, overlap = 1
PHY-3002 : Step(182): len = 36955.2, overlap = 1.75
PHY-3002 : Step(183): len = 36303.2, overlap = 2.5
PHY-3002 : Step(184): len = 36230.1, overlap = 2.5
PHY-3002 : Step(185): len = 35894.6, overlap = 2.75
PHY-3002 : Step(186): len = 35625.3, overlap = 3.25
PHY-3002 : Step(187): len = 35595.9, overlap = 3.75
PHY-3002 : Step(188): len = 35453.1, overlap = 3.25
PHY-3001 : Before Legalized: Len = 35453.1
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004409s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 37138.7, Over = 0
PHY-3001 : Legalized: Len = 37138.7, Over = 0
PHY-3001 : Spreading special nets. 2 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.003445s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 2 instances has been re-located, deltaX = 0, deltaY = 2, maxDist = 1.
PHY-3001 : Final: Len = 37162.7, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 3150, tnet num: 953, tinst num: 426, tnode num: 4205, tedge num: 5476.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 82/955.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 39976, over cnt = 38(0%), over = 55, worst = 4
PHY-1002 : len = 40208, over cnt = 18(0%), over = 24, worst = 3
PHY-1002 : len = 40440, over cnt = 6(0%), over = 8, worst = 2
PHY-1002 : len = 40568, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.059371s wall, 0.078125s user + 0.015625s system = 0.093750s CPU (157.9%)

PHY-1001 : Congestion index: top1 = 21.27, top5 = 13.83, top10 = 9.44, top15 = 7.02.
PHY-1001 : End incremental global routing;  0.102604s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (152.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 953 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.028949s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (54.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.145251s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (129.1%)

OPT-1001 : Current memory(MB): used = 191, reserve = 157, peak = 192.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 809/955.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 40568, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.003756s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 21.27, top5 = 13.83, top10 = 9.44, top15 = 7.02.
OPT-1001 : End congestion update;  0.044464s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (105.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 953 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.026604s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (117.5%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.072025s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (108.5%)

OPT-1001 : Current memory(MB): used = 192, reserve = 159, peak = 192.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 953 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.028692s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (108.9%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 809/955.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 40568, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.003785s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 21.27, top5 = 13.83, top10 = 9.44, top15 = 7.02.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 953 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.024909s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (62.7%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 20.827586
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.511535s wall, 0.546875s user + 0.015625s system = 0.562500s CPU (110.0%)

RUN-1003 : finish command "place" in  4.513635s wall, 5.828125s user + 3.421875s system = 9.250000s CPU (204.9%)

RUN-1004 : used memory is 180 MB, reserved memory is 146 MB, peak memory is 192 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 428 instances
RUN-1001 : 191 mslices, 192 lslices, 37 pads, 2 brams, 1 dsps
RUN-1001 : There are total 955 nets
RUN-1001 : 669 nets have 2 pins
RUN-1001 : 204 nets have [3 - 5] pins
RUN-1001 : 60 nets have [6 - 10] pins
RUN-1001 : 15 nets have [11 - 20] pins
RUN-1001 : 5 nets have [21 - 99] pins
RUN-1001 : 2 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 3150, tnet num: 953, tinst num: 426, tnode num: 4205, tedge num: 5476.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 191 mslices, 192 lslices, 37 pads, 2 brams, 1 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 953 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 39624, over cnt = 42(0%), over = 58, worst = 4
PHY-1002 : len = 39864, over cnt = 18(0%), over = 23, worst = 3
PHY-1002 : len = 40112, over cnt = 4(0%), over = 5, worst = 2
PHY-1002 : len = 40192, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.059179s wall, 0.078125s user + 0.031250s system = 0.109375s CPU (184.8%)

PHY-1001 : Congestion index: top1 = 20.73, top5 = 13.68, top10 = 9.34, top15 = 6.94.
PHY-1001 : End global routing;  0.100866s wall, 0.125000s user + 0.031250s system = 0.156250s CPU (154.9%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 209, reserve = 176, peak = 212.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 472, reserve = 442, peak = 472.
PHY-1001 : End build detailed router design. 4.547380s wall, 4.406250s user + 0.109375s system = 4.515625s CPU (99.3%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 23808, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.759668s wall, 1.765625s user + 0.031250s system = 1.796875s CPU (102.1%)

PHY-1001 : Current memory(MB): used = 505, reserve = 475, peak = 506.
PHY-1001 : End phase 1; 1.769539s wall, 1.765625s user + 0.031250s system = 1.796875s CPU (101.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 35% nets.
PHY-1001 : Routed 40% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 62% nets.
PHY-1001 : Routed 85% nets.
PHY-1022 : len = 103072, over cnt = 8(0%), over = 8, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 505, reserve = 475, peak = 506.
PHY-1001 : End initial routed; 1.711266s wall, 2.562500s user + 0.156250s system = 2.718750s CPU (158.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/819(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.640   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.086   |  -0.143   |   4   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.438431s wall, 0.437500s user + 0.000000s system = 0.437500s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 507, reserve = 477, peak = 507.
PHY-1001 : End phase 2; 2.149900s wall, 3.000000s user + 0.156250s system = 3.156250s CPU (146.8%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 103072, over cnt = 8(0%), over = 8, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.013258s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (117.8%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 103000, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.026997s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (57.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 103016, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.027756s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (112.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/819(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.640   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.086   |  -0.143   |   4   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.458774s wall, 0.468750s user + 0.000000s system = 0.468750s CPU (102.2%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 2 feed throughs used by 2 nets
PHY-1001 : End commit to database; 0.103247s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (75.7%)

PHY-1001 : Current memory(MB): used = 519, reserve = 489, peak = 519.
PHY-1001 : End phase 3; 0.847448s wall, 0.812500s user + 0.015625s system = 0.828125s CPU (97.7%)

PHY-1003 : Routed, final wirelength = 103016
PHY-1001 : Current memory(MB): used = 519, reserve = 489, peak = 519.
PHY-1001 : End export database. 0.010184s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (153.4%)

PHY-1001 : End detail routing;  9.628726s wall, 10.296875s user + 0.328125s system = 10.625000s CPU (110.3%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 3150, tnet num: 953, tinst num: 426, tnode num: 4205, tedge num: 5476.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/asy_w_rst1 endpin signal_process/demodu/fifo/full_flag_reg_syn_5.sr slack -48ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[4] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[4] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[6] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[16] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[7] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[19] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dia[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[21] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dia[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[24] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dia[6] slack -86ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[25] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dia[7] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 3174, tnet num: 965, tinst num: 438, tnode num: 4229, tedge num: 5500.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  3.823992s wall, 3.890625s user + 0.250000s system = 4.140625s CPU (108.3%)

RUN-1003 : finish command "route" in  13.794884s wall, 14.562500s user + 0.609375s system = 15.171875s CPU (110.0%)

RUN-1004 : used memory is 476 MB, reserved memory is 449 MB, peak memory is 519 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      481   out of  19600    2.45%
#reg                      471   out of  19600    2.40%
#le                       744
  #lut only               273   out of    744   36.69%
  #reg only               263   out of    744   35.35%
  #lut&reg                208   out of    744   27.96%
#dsp                        1   out of     29    3.45%
#bram                       2   out of     64    3.12%
  #bram9k                   2
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         219
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         68
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    26
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |744    |368     |113     |504     |2       |1       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |523    |196     |66      |393     |2       |1       |
|    ctrl_signal             |SignalGenerator                                  |48     |39      |9       |22      |0       |0       |
|    demodu                  |Demodulation                                     |261    |82      |25      |202     |2       |0       |
|      fifo                  |Asys_fifo56X16                                   |55     |30      |6       |45      |2       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |2       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |6       |0       |16      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |11      |0       |16      |0       |0       |
|    integ                   |Integration                                      |67     |8       |7       |55      |0       |0       |
|    modu                    |Modulation                                       |110    |39      |16      |95      |0       |1       |
|    rs422                   |Rs422Output                                      |18     |9       |9       |2       |0       |0       |
|    trans                   |SquareWaveGenerator                              |19     |19      |0       |17      |0       |0       |
|  u_uart                    |UART_Control                                     |57     |48      |7       |37      |0       |0       |
|    U0                      |speed_select_Tx                                  |28     |20      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |17     |16      |0       |11      |0       |0       |
|    U2                      |Ctrl_Data                                        |12     |12      |0       |10      |0       |0       |
|  wendu                     |DS18B20                                          |164    |124     |40      |41      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout    Nets  
    #1         1       642   
    #2         2       129   
    #3         3        60   
    #4         4        15   
    #5        5-10      60   
    #6       11-50      16   
    #7       51-100     1    
  Average     2.01           

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 3174, tnet num: 965, tinst num: 438, tnode num: 4229, tedge num: 5500.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 965 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 438
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 967, pip num: 7265
BIT-1002 : Init feedthrough completely, num: 2
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1132 valid insts, and 20051 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101011010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.109199s wall, 14.578125s user + 0.140625s system = 14.718750s CPU (473.4%)

RUN-1004 : used memory is 489 MB, reserved memory is 458 MB, peak memory is 641 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250813_173531.log"
