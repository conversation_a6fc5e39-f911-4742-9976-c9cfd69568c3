============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Tue Aug  5 10:29:07 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1586 instances
RUN-0007 : 376 luts, 967 seqs, 122 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2119 nets
RUN-1001 : 1542 nets have 2 pins
RUN-1001 : 474 nets have [3 - 5] pins
RUN-1001 : 61 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     263     
RUN-1001 :   No   |  No   |  Yes  |     125     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1584 instances, 376 luts, 967 seqs, 192 slices, 19 macros(192 instances: 122 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7552, tnet num: 2117, tinst num: 1584, tnode num: 10721, tedge num: 12796.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2117 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.262418s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (101.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 526537
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1584.
PHY-3001 : End clustering;  0.000021s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 449449, overlap = 20.25
PHY-3002 : Step(2): len = 426278, overlap = 20.25
PHY-3002 : Step(3): len = 410439, overlap = 18
PHY-3002 : Step(4): len = 401580, overlap = 13.5
PHY-3002 : Step(5): len = 386459, overlap = 11.25
PHY-3002 : Step(6): len = 374521, overlap = 13.5
PHY-3002 : Step(7): len = 366870, overlap = 13.5
PHY-3002 : Step(8): len = 356961, overlap = 18
PHY-3002 : Step(9): len = 347482, overlap = 15.75
PHY-3002 : Step(10): len = 340442, overlap = 15.75
PHY-3002 : Step(11): len = 332975, overlap = 15.75
PHY-3002 : Step(12): len = 324457, overlap = 13.5
PHY-3002 : Step(13): len = 317103, overlap = 13.5
PHY-3002 : Step(14): len = 310730, overlap = 13.5
PHY-3002 : Step(15): len = 303257, overlap = 15.75
PHY-3002 : Step(16): len = 296197, overlap = 15.75
PHY-3002 : Step(17): len = 290990, overlap = 15.75
PHY-3002 : Step(18): len = 283706, overlap = 15.75
PHY-3002 : Step(19): len = 276286, overlap = 15.75
PHY-3002 : Step(20): len = 272009, overlap = 15.75
PHY-3002 : Step(21): len = 266193, overlap = 13.5
PHY-3002 : Step(22): len = 260279, overlap = 13.5
PHY-3002 : Step(23): len = 255749, overlap = 13.5
PHY-3002 : Step(24): len = 249958, overlap = 13.5
PHY-3002 : Step(25): len = 244612, overlap = 13.5
PHY-3002 : Step(26): len = 238729, overlap = 18
PHY-3002 : Step(27): len = 234527, overlap = 20.25
PHY-3002 : Step(28): len = 227917, overlap = 20.25
PHY-3002 : Step(29): len = 223075, overlap = 20.25
PHY-3002 : Step(30): len = 218762, overlap = 20.25
PHY-3002 : Step(31): len = 212703, overlap = 20.25
PHY-3002 : Step(32): len = 207164, overlap = 20.25
PHY-3002 : Step(33): len = 203950, overlap = 20.25
PHY-3002 : Step(34): len = 198939, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000143326
PHY-3002 : Step(35): len = 201361, overlap = 13.5
PHY-3002 : Step(36): len = 199702, overlap = 11.25
PHY-3002 : Step(37): len = 198184, overlap = 13.5
PHY-3002 : Step(38): len = 196098, overlap = 11.25
PHY-3002 : Step(39): len = 190509, overlap = 11.25
PHY-3002 : Step(40): len = 185414, overlap = 11.25
PHY-3002 : Step(41): len = 182789, overlap = 9
PHY-3002 : Step(42): len = 174335, overlap = 6.75
PHY-3002 : Step(43): len = 167343, overlap = 11.25
PHY-3002 : Step(44): len = 165150, overlap = 6.75
PHY-3002 : Step(45): len = 162824, overlap = 2.25
PHY-3002 : Step(46): len = 160026, overlap = 6.75
PHY-3002 : Step(47): len = 154575, overlap = 9
PHY-3002 : Step(48): len = 153279, overlap = 9
PHY-3002 : Step(49): len = 149981, overlap = 4.5
PHY-3002 : Step(50): len = 145903, overlap = 4.5
PHY-3002 : Step(51): len = 143277, overlap = 4.5
PHY-3002 : Step(52): len = 142246, overlap = 2.25
PHY-3002 : Step(53): len = 139213, overlap = 6.75
PHY-3002 : Step(54): len = 132845, overlap = 4.5
PHY-3002 : Step(55): len = 131328, overlap = 6.75
PHY-3002 : Step(56): len = 128586, overlap = 6.75
PHY-3002 : Step(57): len = 125249, overlap = 2.25
PHY-3002 : Step(58): len = 123361, overlap = 2.25
PHY-3002 : Step(59): len = 119826, overlap = 4.5
PHY-3002 : Step(60): len = 115561, overlap = 4.5
PHY-3002 : Step(61): len = 114646, overlap = 4.5
PHY-3002 : Step(62): len = 113441, overlap = 4.5
PHY-3002 : Step(63): len = 110074, overlap = 9
PHY-3002 : Step(64): len = 106383, overlap = 9
PHY-3002 : Step(65): len = 104428, overlap = 9
PHY-3002 : Step(66): len = 103275, overlap = 9
PHY-3002 : Step(67): len = 100582, overlap = 6.75
PHY-3002 : Step(68): len = 98317.2, overlap = 6.75
PHY-3002 : Step(69): len = 95679.6, overlap = 6.75
PHY-3002 : Step(70): len = 94725.2, overlap = 6.75
PHY-3002 : Step(71): len = 93269.1, overlap = 6.75
PHY-3002 : Step(72): len = 92453.1, overlap = 9
PHY-3002 : Step(73): len = 89367.1, overlap = 9
PHY-3002 : Step(74): len = 88050.6, overlap = 11.25
PHY-3002 : Step(75): len = 85840.7, overlap = 11.25
PHY-3002 : Step(76): len = 84987.3, overlap = 11.25
PHY-3002 : Step(77): len = 81055.2, overlap = 4.5
PHY-3002 : Step(78): len = 79952.1, overlap = 6.75
PHY-3002 : Step(79): len = 78668.1, overlap = 9
PHY-3002 : Step(80): len = 78133, overlap = 9.4375
PHY-3002 : Step(81): len = 75644.7, overlap = 12.3125
PHY-3002 : Step(82): len = 75295.5, overlap = 12.375
PHY-3002 : Step(83): len = 72371.5, overlap = 8.375
PHY-3002 : Step(84): len = 71221, overlap = 6.375
PHY-3002 : Step(85): len = 70075.3, overlap = 8.8125
PHY-3002 : Step(86): len = 69612.8, overlap = 11.1875
PHY-3002 : Step(87): len = 68671.2, overlap = 11.125
PHY-3002 : Step(88): len = 67577.7, overlap = 11
PHY-3002 : Step(89): len = 67225.2, overlap = 11
PHY-3002 : Step(90): len = 66161.1, overlap = 8.5625
PHY-3002 : Step(91): len = 65372.5, overlap = 8.3125
PHY-3002 : Step(92): len = 65090.7, overlap = 10.4375
PHY-3002 : Step(93): len = 64642.1, overlap = 10.4375
PHY-3002 : Step(94): len = 63702.3, overlap = 8.1875
PHY-3002 : Step(95): len = 63372.3, overlap = 8
PHY-3002 : Step(96): len = 62303.9, overlap = 10.0625
PHY-3002 : Step(97): len = 61697.8, overlap = 7.5
PHY-3002 : Step(98): len = 60632.9, overlap = 7.25
PHY-3002 : Step(99): len = 59967.9, overlap = 5.0625
PHY-3002 : Step(100): len = 59698, overlap = 6.9375
PHY-3002 : Step(101): len = 58341.9, overlap = 6.75
PHY-3002 : Step(102): len = 57684.6, overlap = 9
PHY-3002 : Step(103): len = 56821.3, overlap = 9
PHY-3002 : Step(104): len = 56489.2, overlap = 9
PHY-3002 : Step(105): len = 55923.5, overlap = 9
PHY-3002 : Step(106): len = 55259.8, overlap = 9.1875
PHY-3002 : Step(107): len = 54977, overlap = 4.5
PHY-3002 : Step(108): len = 53898.9, overlap = 6.75
PHY-3002 : Step(109): len = 53828.9, overlap = 9
PHY-3002 : Step(110): len = 53394.8, overlap = 9
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000286653
PHY-3002 : Step(111): len = 53541.8, overlap = 9
PHY-3002 : Step(112): len = 53571.8, overlap = 6.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000573305
PHY-3002 : Step(113): len = 53667.2, overlap = 6.75
PHY-3002 : Step(114): len = 53648.7, overlap = 6.75
PHY-3001 : Before Legalized: Len = 53648.7
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007062s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (221.2%)

PHY-3001 : After Legalized: Len = 55924.6, Over = 0
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2117 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.061573s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00244574
PHY-3002 : Step(115): len = 56080, overlap = 7.3125
PHY-3002 : Step(116): len = 54946.4, overlap = 9.75
PHY-3002 : Step(117): len = 54293, overlap = 10.6875
PHY-3002 : Step(118): len = 53637.6, overlap = 11.875
PHY-3002 : Step(119): len = 52983.8, overlap = 13.625
PHY-3002 : Step(120): len = 52301.8, overlap = 14.1875
PHY-3002 : Step(121): len = 51501.7, overlap = 14.5
PHY-3002 : Step(122): len = 51248.4, overlap = 13.0625
PHY-3002 : Step(123): len = 50812.9, overlap = 13.25
PHY-3002 : Step(124): len = 50013.1, overlap = 14.3438
PHY-3002 : Step(125): len = 49801.6, overlap = 14.9375
PHY-3002 : Step(126): len = 49431.4, overlap = 14.3125
PHY-3002 : Step(127): len = 48936.7, overlap = 14.625
PHY-3002 : Step(128): len = 48506.3, overlap = 14.4688
PHY-3002 : Step(129): len = 48454.8, overlap = 14.6875
PHY-3002 : Step(130): len = 47757.7, overlap = 15.5
PHY-3002 : Step(131): len = 47068.5, overlap = 15.3125
PHY-3002 : Step(132): len = 46679.9, overlap = 15.5312
PHY-3002 : Step(133): len = 46312.1, overlap = 16.2812
PHY-3002 : Step(134): len = 45767.2, overlap = 16.1562
PHY-3002 : Step(135): len = 45147.4, overlap = 15.6562
PHY-3002 : Step(136): len = 44623, overlap = 16.375
PHY-3002 : Step(137): len = 44081.2, overlap = 16.6875
PHY-3002 : Step(138): len = 44108, overlap = 16.8125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00489148
PHY-3002 : Step(139): len = 44130.7, overlap = 17
PHY-3002 : Step(140): len = 44148.2, overlap = 17.3438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00978296
PHY-3002 : Step(141): len = 44080.5, overlap = 16.9688
PHY-3002 : Step(142): len = 44083.6, overlap = 17.0312
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2117 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.073886s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (105.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.44129e-05
PHY-3002 : Step(143): len = 44242.3, overlap = 67.5625
PHY-3002 : Step(144): len = 45211.2, overlap = 64.7812
PHY-3002 : Step(145): len = 45908.3, overlap = 64.875
PHY-3002 : Step(146): len = 45615.7, overlap = 64.5625
PHY-3002 : Step(147): len = 45230.9, overlap = 63.1875
PHY-3002 : Step(148): len = 45204.1, overlap = 59.4375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000148826
PHY-3002 : Step(149): len = 45363.6, overlap = 59.0625
PHY-3002 : Step(150): len = 45694, overlap = 52.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000259726
PHY-3002 : Step(151): len = 45935.2, overlap = 51.0312
PHY-3002 : Step(152): len = 47459.1, overlap = 43.7188
PHY-3002 : Step(153): len = 49104.8, overlap = 34.9688
PHY-3002 : Step(154): len = 48885.1, overlap = 34.375
PHY-3002 : Step(155): len = 48798.3, overlap = 34.375
PHY-3002 : Step(156): len = 48556.9, overlap = 29.375
PHY-3002 : Step(157): len = 48628, overlap = 29.3438
PHY-3002 : Step(158): len = 48545.2, overlap = 26.6875
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000519453
PHY-3002 : Step(159): len = 48872.6, overlap = 26.1562
PHY-3002 : Step(160): len = 48840.7, overlap = 26.1562
PHY-3002 : Step(161): len = 49419.6, overlap = 23.7188
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7552, tnet num: 2117, tinst num: 1584, tnode num: 10721, tedge num: 12796.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 84.41 peak overflow 3.34
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2119.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52456, over cnt = 245(0%), over = 1128, worst = 25
PHY-1001 : End global iterations;  0.070785s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (176.6%)

PHY-1001 : Congestion index: top1 = 41.96, top5 = 25.45, top10 = 16.02, top15 = 11.38.
PHY-1001 : End incremental global routing;  0.123275s wall, 0.140625s user + 0.031250s system = 0.171875s CPU (139.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2117 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067513s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (92.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.221274s wall, 0.234375s user + 0.031250s system = 0.265625s CPU (120.0%)

OPT-1001 : Current memory(MB): used = 209, reserve = 176, peak = 209.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1652/2119.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52456, over cnt = 245(0%), over = 1128, worst = 25
PHY-1002 : len = 58800, over cnt = 191(0%), over = 532, worst = 12
PHY-1002 : len = 64184, over cnt = 47(0%), over = 63, worst = 3
PHY-1002 : len = 65176, over cnt = 12(0%), over = 14, worst = 2
PHY-1002 : len = 65992, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.105139s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (118.9%)

PHY-1001 : Congestion index: top1 = 35.78, top5 = 24.57, top10 = 17.72, top15 = 13.19.
OPT-1001 : End congestion update;  0.148872s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (115.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2117 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059374s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.3%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.211884s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (110.6%)

OPT-1001 : Current memory(MB): used = 212, reserve = 179, peak = 212.
OPT-1001 : End physical optimization;  0.670741s wall, 0.687500s user + 0.046875s system = 0.734375s CPU (109.5%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 376 LUT to BLE ...
SYN-4008 : Packed 376 LUT and 192 SEQ to BLE.
SYN-4003 : Packing 775 remaining SEQ's ...
SYN-4005 : Packed 67 SEQ with LUT/SLICE
SYN-4006 : 132 single LUT's are left
SYN-4006 : 708 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1084/1391 primitive instances ...
PHY-3001 : End packing;  0.047540s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.6%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 810 instances
RUN-1001 : 380 mslices, 379 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1935 nets
RUN-1001 : 1361 nets have 2 pins
RUN-1001 : 470 nets have [3 - 5] pins
RUN-1001 : 63 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 808 instances, 759 slices, 19 macros(192 instances: 122 mslices 70 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 49618.6, Over = 51.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6321, tnet num: 1933, tinst num: 808, tnode num: 8623, tedge num: 11161.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1933 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.286630s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (98.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.28241e-05
PHY-3002 : Step(162): len = 48839.8, overlap = 54.75
PHY-3002 : Step(163): len = 48239, overlap = 56
PHY-3002 : Step(164): len = 48093.5, overlap = 56
PHY-3002 : Step(165): len = 48283.5, overlap = 54.25
PHY-3002 : Step(166): len = 48064.2, overlap = 54.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.56482e-05
PHY-3002 : Step(167): len = 48239.4, overlap = 54.75
PHY-3002 : Step(168): len = 48426.8, overlap = 50.75
PHY-3002 : Step(169): len = 49217.2, overlap = 46
PHY-3002 : Step(170): len = 49697.1, overlap = 44.5
PHY-3002 : Step(171): len = 49617.7, overlap = 45.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000131296
PHY-3002 : Step(172): len = 49784.6, overlap = 45
PHY-3002 : Step(173): len = 50001.9, overlap = 45
PHY-3001 : Before Legalized: Len = 50001.9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.109958s wall, 0.078125s user + 0.140625s system = 0.218750s CPU (198.9%)

PHY-3001 : After Legalized: Len = 62765.3, Over = 0
PHY-3001 : Trial Legalized: Len = 62765.3
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1933 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.051172s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000778338
PHY-3002 : Step(174): len = 59239.7, overlap = 7
PHY-3002 : Step(175): len = 57969.7, overlap = 10
PHY-3002 : Step(176): len = 56526.3, overlap = 12
PHY-3002 : Step(177): len = 55780.4, overlap = 13.75
PHY-3002 : Step(178): len = 54907.7, overlap = 16.5
PHY-3002 : Step(179): len = 54616.8, overlap = 17.25
PHY-3002 : Step(180): len = 54609.7, overlap = 16.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00155668
PHY-3002 : Step(181): len = 54857.7, overlap = 15.5
PHY-3002 : Step(182): len = 54894, overlap = 15.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00311335
PHY-3002 : Step(183): len = 55086.9, overlap = 14.25
PHY-3002 : Step(184): len = 55086.9, overlap = 14.25
PHY-3001 : Before Legalized: Len = 55086.9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005333s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 59233.2, Over = 0
PHY-3001 : Legalized: Len = 59233.2, Over = 0
PHY-3001 : Spreading special nets. 14 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005512s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 21 instances has been re-located, deltaX = 10, deltaY = 10, maxDist = 2.
PHY-3001 : Final: Len = 59591.2, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6321, tnet num: 1933, tinst num: 808, tnode num: 8623, tedge num: 11161.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 30/1935.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 64928, over cnt = 129(0%), over = 207, worst = 7
PHY-1002 : len = 66176, over cnt = 68(0%), over = 76, worst = 2
PHY-1002 : len = 67056, over cnt = 10(0%), over = 10, worst = 1
PHY-1002 : len = 67160, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 67240, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.128940s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (109.1%)

PHY-1001 : Congestion index: top1 = 31.29, top5 = 22.56, top10 = 17.04, top15 = 13.14.
PHY-1001 : End incremental global routing;  0.180100s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (104.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1933 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061855s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.271125s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (103.7%)

OPT-1001 : Current memory(MB): used = 214, reserve = 181, peak = 214.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1695/1935.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 67240, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005872s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.29, top5 = 22.56, top10 = 17.04, top15 = 13.14.
OPT-1001 : End congestion update;  0.052268s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (89.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1933 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058818s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (106.3%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 768 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 808 instances, 759 slices, 19 macros(192 instances: 122 mslices 70 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 59606.6, Over = 0
PHY-3001 : End spreading;  0.005664s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (275.9%)

PHY-3001 : Final: Len = 59606.6, Over = 0
PHY-3001 : End incremental legalization;  0.036424s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (128.7%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.163007s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (105.4%)

OPT-1001 : Current memory(MB): used = 218, reserve = 186, peak = 218.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1933 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060163s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (77.9%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1691/1935.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 67232, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007553s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.29, top5 = 22.52, top10 = 17.02, top15 = 13.12.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1933 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052630s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (118.8%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.862069
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.880517s wall, 0.906250s user + 0.000000s system = 0.906250s CPU (102.9%)

RUN-1003 : finish command "place" in  5.714904s wall, 8.468750s user + 3.468750s system = 11.937500s CPU (208.9%)

RUN-1004 : used memory is 195 MB, reserved memory is 162 MB, peak memory is 218 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 810 instances
RUN-1001 : 380 mslices, 379 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1935 nets
RUN-1001 : 1361 nets have 2 pins
RUN-1001 : 470 nets have [3 - 5] pins
RUN-1001 : 63 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6321, tnet num: 1933, tinst num: 808, tnode num: 8623, tedge num: 11161.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 380 mslices, 379 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1933 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 64288, over cnt = 131(0%), over = 210, worst = 7
PHY-1002 : len = 65464, over cnt = 79(0%), over = 89, worst = 3
PHY-1002 : len = 66608, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 66720, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.126335s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (123.7%)

PHY-1001 : Congestion index: top1 = 30.97, top5 = 22.34, top10 = 16.90, top15 = 13.03.
PHY-1001 : End global routing;  0.181362s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (103.4%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 233, reserve = 201, peak = 233.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 491, reserve = 464, peak = 491.
PHY-1001 : End build detailed router design. 3.469585s wall, 3.421875s user + 0.000000s system = 3.421875s CPU (98.6%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 32672, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.079933s wall, 1.078125s user + 0.000000s system = 1.078125s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 523, reserve = 497, peak = 523.
PHY-1001 : End phase 1; 1.085785s wall, 1.093750s user + 0.000000s system = 1.093750s CPU (100.7%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 29% nets.
PHY-1001 : Routed 37% nets.
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 175464, over cnt = 29(0%), over = 29, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 526, reserve = 498, peak = 526.
PHY-1001 : End initial routed; 1.488959s wall, 2.500000s user + 0.093750s system = 2.593750s CPU (174.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1723(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.613   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.959   |  -45.336  |  21   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.390541s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 527, reserve = 499, peak = 527.
PHY-1001 : End phase 2; 1.879606s wall, 2.890625s user + 0.093750s system = 2.984375s CPU (158.8%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 175464, over cnt = 29(0%), over = 29, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.017513s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (89.2%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 175480, over cnt = 12(0%), over = 12, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.030480s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (102.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 175552, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.025351s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (123.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1723(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.613   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.959   |  -45.336  |  21   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.382473s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (102.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 2 feed throughs used by 2 nets
PHY-1001 : End commit to database; 0.254049s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (98.4%)

PHY-1001 : Current memory(MB): used = 542, reserve = 514, peak = 542.
PHY-1001 : End phase 3; 0.860892s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (99.8%)

PHY-1003 : Routed, final wirelength = 175552
PHY-1001 : Current memory(MB): used = 542, reserve = 514, peak = 542.
PHY-1001 : End export database. 0.011925s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (131.0%)

PHY-1001 : End detail routing;  7.494684s wall, 8.468750s user + 0.093750s system = 8.562500s CPU (114.2%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6321, tnet num: 1933, tinst num: 808, tnode num: 8623, tedge num: 11161.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[6] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[8] slack -86ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[47] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[2] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[5] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[52] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[7] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[54] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_66.dia[2] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[55] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_66.dia[5] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[7] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[13] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[4] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_63.mi[0] slack -2827ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_63.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_66.mi[0] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_66.mi[1] slack -2583ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_69.mi[0] slack -2959ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_69.mi[1] slack -2827ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_72.mi[0] slack -2959ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_72.mi[1] slack -2588ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_75.mi[0] slack -2947ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_75.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_78.mi[0] slack -2947ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_78.mi[1] slack -2719ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_81.mi[0] slack -2827ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_81.mi[1] slack -2959ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_83.mi[0] slack -2719ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/tx_data_dy_b[4]_syn_24.mi[0] slack -2801ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6441, tnet num: 1993, tinst num: 868, tnode num: 8743, tedge num: 11281.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/tx_data_dy_b[4]_syn_24_mi[0] slack -750ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_63_mi[1] slack -309ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_66_mi[1] slack -904ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_66_mi[0] slack -505ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_63_mi[0] slack -301ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg2_syn_72_mi[0] slack -371ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_69_mi[1] slack -406ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_69_mi[0] slack -506ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_72_mi[1] slack -587ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_75_mi[0] slack -51ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_78_mi[1] slack -396ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_75_mi[1] slack -482ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_78_mi[0] slack -889ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_81_mi[1] slack -395ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_81_mi[0] slack -718ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_83_mi[0] slack -504ps
RUN-1001 : End hold fix;  3.634082s wall, 3.625000s user + 0.390625s system = 4.015625s CPU (110.5%)

RUN-1003 : finish command "route" in  11.645866s wall, 12.625000s user + 0.484375s system = 13.109375s CPU (112.6%)

RUN-1004 : used memory is 531 MB, reserved memory is 505 MB, peak memory is 542 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      883   out of  19600    4.51%
#reg                     1053   out of  19600    5.37%
#le                      1591
  #lut only               538   out of   1591   33.82%
  #reg only               708   out of   1591   44.50%
  #lut&reg                345   out of   1591   21.68%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         482
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         100
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    42
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1591   |691     |192     |1086    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1081   |284     |128     |863     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |39     |35      |4       |23      |0       |0       |
|    demodu                  |Demodulation                                     |449    |98      |41      |353     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |55     |30      |6       |45      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |5       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |13     |8       |0       |13      |0       |0       |
|    integ                   |Integration                                      |138    |15      |15      |110     |0       |0       |
|    modu                    |Modulation                                       |98     |37      |14      |96      |0       |1       |
|    rs422                   |Rs422Output                                      |321    |71      |46      |259     |0       |4       |
|    trans                   |SquareWaveGenerator                              |36     |28      |8       |22      |0       |0       |
|  u_uart                    |UART_Control                                     |175    |129     |7       |118     |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |26     |19      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |112    |82      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |311    |266     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1382  
    #2          2       337   
    #3          3       109   
    #4          4        24   
    #5        5-10       67   
    #6        11-50      31   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6441, tnet num: 1993, tinst num: 868, tnode num: 8743, tedge num: 11281.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1993 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 868
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1995, pip num: 14420
BIT-1002 : Init feedthrough completely, num: 7
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1284 valid insts, and 38646 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  4.653053s wall, 24.921875s user + 0.109375s system = 25.031250s CPU (538.0%)

RUN-1004 : used memory is 544 MB, reserved memory is 514 MB, peak memory is 673 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250805_102907.log"
