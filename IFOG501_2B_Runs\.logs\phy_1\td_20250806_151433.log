============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Wed Aug  6 15:14:33 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1550 instances
RUN-0007 : 367 luts, 943 seqs, 119 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2069 nets
RUN-1001 : 1525 nets have 2 pins
RUN-1001 : 441 nets have [3 - 5] pins
RUN-1001 : 62 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     243     
RUN-1001 :   No   |  No   |  Yes  |     121     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1548 instances, 367 luts, 943 seqs, 189 slices, 20 macros(189 instances: 119 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7377, tnet num: 2067, tinst num: 1548, tnode num: 10476, tedge num: 12468.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2067 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.265458s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (94.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 514836
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1548.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 449298, overlap = 20.25
PHY-3002 : Step(2): len = 427075, overlap = 13.5
PHY-3002 : Step(3): len = 414305, overlap = 20.25
PHY-3002 : Step(4): len = 402686, overlap = 13.5
PHY-3002 : Step(5): len = 390387, overlap = 20.25
PHY-3002 : Step(6): len = 377351, overlap = 18
PHY-3002 : Step(7): len = 367356, overlap = 13.5
PHY-3002 : Step(8): len = 359479, overlap = 18
PHY-3002 : Step(9): len = 348624, overlap = 15.75
PHY-3002 : Step(10): len = 339364, overlap = 15.75
PHY-3002 : Step(11): len = 333478, overlap = 13.5
PHY-3002 : Step(12): len = 324047, overlap = 13.5
PHY-3002 : Step(13): len = 315324, overlap = 13.5
PHY-3002 : Step(14): len = 310035, overlap = 13.5
PHY-3002 : Step(15): len = 302200, overlap = 13.5
PHY-3002 : Step(16): len = 294834, overlap = 13.5
PHY-3002 : Step(17): len = 289641, overlap = 11.25
PHY-3002 : Step(18): len = 283602, overlap = 13.5
PHY-3002 : Step(19): len = 276595, overlap = 13.5
PHY-3002 : Step(20): len = 271562, overlap = 13.5
PHY-3002 : Step(21): len = 267187, overlap = 13.5
PHY-3002 : Step(22): len = 258281, overlap = 13.5
PHY-3002 : Step(23): len = 252971, overlap = 13.5
PHY-3002 : Step(24): len = 249712, overlap = 13.5
PHY-3002 : Step(25): len = 242611, overlap = 13.5
PHY-3002 : Step(26): len = 232661, overlap = 13.5
PHY-3002 : Step(27): len = 229455, overlap = 13.5
PHY-3002 : Step(28): len = 224819, overlap = 20.25
PHY-3002 : Step(29): len = 192606, overlap = 20.25
PHY-3002 : Step(30): len = 185435, overlap = 20.25
PHY-3002 : Step(31): len = 183990, overlap = 20.25
PHY-3002 : Step(32): len = 155326, overlap = 18
PHY-3002 : Step(33): len = 137468, overlap = 18
PHY-3002 : Step(34): len = 135157, overlap = 18
PHY-3002 : Step(35): len = 132852, overlap = 20.25
PHY-3002 : Step(36): len = 125222, overlap = 20.25
PHY-3002 : Step(37): len = 123932, overlap = 20.25
PHY-3002 : Step(38): len = 118394, overlap = 18
PHY-3002 : Step(39): len = 114062, overlap = 20.25
PHY-3002 : Step(40): len = 112424, overlap = 20.25
PHY-3002 : Step(41): len = 110433, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.42791e-05
PHY-3002 : Step(42): len = 110512, overlap = 13.5
PHY-3002 : Step(43): len = 109971, overlap = 13.5
PHY-3002 : Step(44): len = 109242, overlap = 15.75
PHY-3002 : Step(45): len = 108758, overlap = 13.5
PHY-3002 : Step(46): len = 106463, overlap = 11.25
PHY-3002 : Step(47): len = 104549, overlap = 11.25
PHY-3002 : Step(48): len = 103320, overlap = 9
PHY-3002 : Step(49): len = 101561, overlap = 9
PHY-3002 : Step(50): len = 98765.6, overlap = 11.25
PHY-3002 : Step(51): len = 94506.3, overlap = 11.25
PHY-3002 : Step(52): len = 93264.6, overlap = 11.25
PHY-3002 : Step(53): len = 92267.6, overlap = 11.25
PHY-3002 : Step(54): len = 90753.7, overlap = 13.5
PHY-3002 : Step(55): len = 90106.8, overlap = 13.5
PHY-3002 : Step(56): len = 89114.7, overlap = 11.25
PHY-3002 : Step(57): len = 86783.3, overlap = 11.25
PHY-3002 : Step(58): len = 82966.5, overlap = 11.25
PHY-3002 : Step(59): len = 80845.3, overlap = 15.75
PHY-3002 : Step(60): len = 80477.7, overlap = 13.5
PHY-3002 : Step(61): len = 79496.6, overlap = 13.5
PHY-3002 : Step(62): len = 78707.9, overlap = 15.75
PHY-3002 : Step(63): len = 76350.7, overlap = 13.5
PHY-3002 : Step(64): len = 74936, overlap = 18.25
PHY-3002 : Step(65): len = 73537.2, overlap = 16.5625
PHY-3002 : Step(66): len = 71666.4, overlap = 16.75
PHY-3002 : Step(67): len = 71331.5, overlap = 16.75
PHY-3002 : Step(68): len = 70566, overlap = 14.75
PHY-3002 : Step(69): len = 68836.7, overlap = 12.5625
PHY-3002 : Step(70): len = 67747.4, overlap = 17.0625
PHY-3002 : Step(71): len = 66266.1, overlap = 16.8125
PHY-3002 : Step(72): len = 66098.7, overlap = 16.6875
PHY-3002 : Step(73): len = 65545.4, overlap = 16.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000188558
PHY-3002 : Step(74): len = 65801.8, overlap = 16.5
PHY-3002 : Step(75): len = 65777.1, overlap = 16.5
PHY-3002 : Step(76): len = 65688.3, overlap = 16.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000377117
PHY-3002 : Step(77): len = 66105.2, overlap = 16.5
PHY-3002 : Step(78): len = 66117.9, overlap = 16.5
PHY-3002 : Step(79): len = 66212.6, overlap = 14.1875
PHY-3001 : Before Legalized: Len = 66212.6
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.023895s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 69837.6, Over = 0.6875
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2067 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.057151s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (109.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(80): len = 69700.8, overlap = 3.875
PHY-3002 : Step(81): len = 68478.7, overlap = 6.3125
PHY-3002 : Step(82): len = 67159.6, overlap = 8.8125
PHY-3002 : Step(83): len = 66387.5, overlap = 8.90625
PHY-3002 : Step(84): len = 65015.9, overlap = 9.34375
PHY-3002 : Step(85): len = 63897.4, overlap = 9.5625
PHY-3002 : Step(86): len = 62456.8, overlap = 10.8125
PHY-3002 : Step(87): len = 61336.6, overlap = 11.4375
PHY-3002 : Step(88): len = 60787.5, overlap = 11.9375
PHY-3002 : Step(89): len = 59896.4, overlap = 12.4375
PHY-3002 : Step(90): len = 59100.7, overlap = 12.5
PHY-3002 : Step(91): len = 58305.1, overlap = 12.375
PHY-3002 : Step(92): len = 57499.3, overlap = 10.8125
PHY-3002 : Step(93): len = 56656.5, overlap = 8.15625
PHY-3002 : Step(94): len = 55791.1, overlap = 8.34375
PHY-3002 : Step(95): len = 54978.9, overlap = 8.21875
PHY-3002 : Step(96): len = 54325.6, overlap = 8.46875
PHY-3002 : Step(97): len = 54091.3, overlap = 8.71875
PHY-3002 : Step(98): len = 53404.2, overlap = 10.25
PHY-3002 : Step(99): len = 52588.5, overlap = 13.0312
PHY-3002 : Step(100): len = 52010.7, overlap = 13.8438
PHY-3002 : Step(101): len = 51564.4, overlap = 14.1562
PHY-3002 : Step(102): len = 51104.1, overlap = 14.2812
PHY-3002 : Step(103): len = 50804, overlap = 15.2188
PHY-3002 : Step(104): len = 50780, overlap = 15.3438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0020335
PHY-3002 : Step(105): len = 50718.5, overlap = 15.3438
PHY-3002 : Step(106): len = 50757.1, overlap = 15.3438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.004067
PHY-3002 : Step(107): len = 50652.2, overlap = 15.3438
PHY-3002 : Step(108): len = 50617.1, overlap = 15.1875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2067 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.069506s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (112.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.32006e-05
PHY-3002 : Step(109): len = 50884, overlap = 45.2812
PHY-3002 : Step(110): len = 51729, overlap = 45.3438
PHY-3002 : Step(111): len = 52298.5, overlap = 44.625
PHY-3002 : Step(112): len = 52449.4, overlap = 44.0938
PHY-3002 : Step(113): len = 52377.7, overlap = 43.8438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000146401
PHY-3002 : Step(114): len = 52425.8, overlap = 42.25
PHY-3002 : Step(115): len = 53136, overlap = 36.0625
PHY-3002 : Step(116): len = 53473, overlap = 35.5
PHY-3002 : Step(117): len = 53580, overlap = 35.4062
PHY-3002 : Step(118): len = 53795.5, overlap = 32.4062
PHY-3002 : Step(119): len = 54504.8, overlap = 30.5
PHY-3002 : Step(120): len = 54870.4, overlap = 29.2812
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000292803
PHY-3002 : Step(121): len = 54942.6, overlap = 29.375
PHY-3002 : Step(122): len = 54932.3, overlap = 28.0625
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7377, tnet num: 2067, tinst num: 1548, tnode num: 10476, tedge num: 12468.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 85.75 peak overflow 2.62
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2069.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58776, over cnt = 256(0%), over = 1018, worst = 23
PHY-1001 : End global iterations;  0.075446s wall, 0.093750s user + 0.031250s system = 0.125000s CPU (165.7%)

PHY-1001 : Congestion index: top1 = 42.74, top5 = 25.98, top10 = 16.70, top15 = 11.89.
PHY-1001 : End incremental global routing;  0.125044s wall, 0.140625s user + 0.031250s system = 0.171875s CPU (137.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2067 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.066418s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (117.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.219540s wall, 0.234375s user + 0.031250s system = 0.265625s CPU (121.0%)

OPT-1001 : Current memory(MB): used = 208, reserve = 175, peak = 208.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1612/2069.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58776, over cnt = 256(0%), over = 1018, worst = 23
PHY-1002 : len = 66472, over cnt = 165(0%), over = 292, worst = 16
PHY-1002 : len = 69032, over cnt = 44(0%), over = 93, worst = 16
PHY-1002 : len = 69632, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 69856, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.088795s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (140.8%)

PHY-1001 : Congestion index: top1 = 35.62, top5 = 24.89, top10 = 18.10, top15 = 13.36.
OPT-1001 : End congestion update;  0.129659s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (132.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2067 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053267s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.0%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.185618s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (117.8%)

OPT-1001 : Current memory(MB): used = 211, reserve = 178, peak = 211.
OPT-1001 : End physical optimization;  0.667702s wall, 0.687500s user + 0.046875s system = 0.734375s CPU (110.0%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 367 LUT to BLE ...
SYN-4008 : Packed 367 LUT and 188 SEQ to BLE.
SYN-4003 : Packing 755 remaining SEQ's ...
SYN-4005 : Packed 100 SEQ with LUT/SLICE
SYN-4006 : 95 single LUT's are left
SYN-4006 : 655 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1022/1326 primitive instances ...
PHY-3001 : End packing;  0.047769s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.1%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 773 instances
RUN-1001 : 361 mslices, 361 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1889 nets
RUN-1001 : 1351 nets have 2 pins
RUN-1001 : 435 nets have [3 - 5] pins
RUN-1001 : 62 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 771 instances, 722 slices, 20 macros(189 instances: 119 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 54988.2, Over = 52.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6138, tnet num: 1887, tinst num: 771, tnode num: 8354, tedge num: 10811.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1887 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.307841s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (96.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.62709e-05
PHY-3002 : Step(123): len = 54376.8, overlap = 53.25
PHY-3002 : Step(124): len = 53991.2, overlap = 54.75
PHY-3002 : Step(125): len = 53938.3, overlap = 54
PHY-3002 : Step(126): len = 54013.8, overlap = 53.5
PHY-3002 : Step(127): len = 53830.5, overlap = 53.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.25418e-05
PHY-3002 : Step(128): len = 53925.7, overlap = 51.5
PHY-3002 : Step(129): len = 54323.6, overlap = 51.75
PHY-3002 : Step(130): len = 54689.3, overlap = 49
PHY-3002 : Step(131): len = 54979.2, overlap = 48.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000145084
PHY-3002 : Step(132): len = 55888.3, overlap = 42.75
PHY-3002 : Step(133): len = 56414.1, overlap = 42.25
PHY-3002 : Step(134): len = 56507.2, overlap = 41.25
PHY-3001 : Before Legalized: Len = 56507.2
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.090446s wall, 0.046875s user + 0.093750s system = 0.140625s CPU (155.5%)

PHY-3001 : After Legalized: Len = 69699.3, Over = 0
PHY-3001 : Trial Legalized: Len = 69699.3
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1887 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.050029s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00158134
PHY-3002 : Step(135): len = 66414.8, overlap = 6.75
PHY-3002 : Step(136): len = 64536.2, overlap = 11.5
PHY-3002 : Step(137): len = 62693.6, overlap = 13.5
PHY-3002 : Step(138): len = 61470, overlap = 14.25
PHY-3002 : Step(139): len = 60724.4, overlap = 16.25
PHY-3002 : Step(140): len = 60408.8, overlap = 16.5
PHY-3002 : Step(141): len = 59876.3, overlap = 20
PHY-3002 : Step(142): len = 59580.1, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0028903
PHY-3002 : Step(143): len = 59740.2, overlap = 20.25
PHY-3002 : Step(144): len = 59793.1, overlap = 20.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00578061
PHY-3002 : Step(145): len = 59804.6, overlap = 19.75
PHY-3002 : Step(146): len = 59804.6, overlap = 19.75
PHY-3001 : Before Legalized: Len = 59804.6
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005186s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 63440.5, Over = 0
PHY-3001 : Legalized: Len = 63440.5, Over = 0
PHY-3001 : Spreading special nets. 9 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005147s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 15 instances has been re-located, deltaX = 6, deltaY = 9, maxDist = 1.
PHY-3001 : Final: Len = 63762.5, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6138, tnet num: 1887, tinst num: 771, tnode num: 8354, tedge num: 10811.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 33/1889.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70360, over cnt = 134(0%), over = 207, worst = 6
PHY-1002 : len = 71488, over cnt = 58(0%), over = 69, worst = 2
PHY-1002 : len = 72144, over cnt = 15(0%), over = 16, worst = 2
PHY-1002 : len = 72368, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 72432, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.126415s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (111.2%)

PHY-1001 : Congestion index: top1 = 31.62, top5 = 22.73, top10 = 17.53, top15 = 13.65.
PHY-1001 : End incremental global routing;  0.177700s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (114.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1887 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058328s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (80.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.263339s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (106.8%)

OPT-1001 : Current memory(MB): used = 215, reserve = 182, peak = 215.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1680/1889.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72432, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005714s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.62, top5 = 22.73, top10 = 17.53, top15 = 13.65.
OPT-1001 : End congestion update;  0.050366s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1887 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057421s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.8%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 731 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 771 instances, 722 slices, 20 macros(189 instances: 119 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 63787.2, Over = 0
PHY-3001 : End spreading;  0.004722s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 63787.2, Over = 0
PHY-3001 : End incremental legalization;  0.032431s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (433.6%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.152806s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (173.8%)

OPT-1001 : Current memory(MB): used = 220, reserve = 187, peak = 220.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1887 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.045947s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (102.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1676/1889.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72432, over cnt = 2(0%), over = 3, worst = 2
PHY-1002 : len = 72432, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.015245s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (102.5%)

PHY-1001 : Congestion index: top1 = 31.62, top5 = 22.72, top10 = 17.53, top15 = 13.65.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1887 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051185s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.6%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.172414
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.851660s wall, 0.968750s user + 0.000000s system = 0.968750s CPU (113.7%)

RUN-1003 : finish command "place" in  4.931279s wall, 7.171875s user + 2.593750s system = 9.765625s CPU (198.0%)

RUN-1004 : used memory is 199 MB, reserved memory is 166 MB, peak memory is 220 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 773 instances
RUN-1001 : 361 mslices, 361 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1889 nets
RUN-1001 : 1351 nets have 2 pins
RUN-1001 : 435 nets have [3 - 5] pins
RUN-1001 : 62 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6138, tnet num: 1887, tinst num: 771, tnode num: 8354, tedge num: 10811.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 361 mslices, 361 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1887 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69880, over cnt = 138(0%), over = 208, worst = 6
PHY-1002 : len = 70992, over cnt = 65(0%), over = 71, worst = 2
PHY-1002 : len = 71816, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 71864, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.128788s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (157.7%)

PHY-1001 : Congestion index: top1 = 31.53, top5 = 22.60, top10 = 17.41, top15 = 13.53.
PHY-1001 : End global routing;  0.177069s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (141.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 231, reserve = 200, peak = 234.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 493, reserve = 465, peak = 493.
PHY-1001 : End build detailed router design. 3.169663s wall, 3.015625s user + 0.046875s system = 3.062500s CPU (96.6%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 30456, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.068526s wall, 1.078125s user + 0.000000s system = 1.078125s CPU (100.9%)

PHY-1001 : Current memory(MB): used = 525, reserve = 498, peak = 525.
PHY-1001 : End phase 1; 1.074634s wall, 1.078125s user + 0.000000s system = 1.078125s CPU (100.3%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 29% nets.
PHY-1001 : Routed 35% nets.
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 173272, over cnt = 31(0%), over = 31, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 527, reserve = 499, peak = 528.
PHY-1001 : End initial routed; 1.581645s wall, 2.375000s user + 0.078125s system = 2.453125s CPU (155.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1681(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.784   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -47.959  |  23   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.346787s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (94.6%)

PHY-1001 : Current memory(MB): used = 529, reserve = 500, peak = 529.
PHY-1001 : End phase 2; 1.928527s wall, 2.703125s user + 0.078125s system = 2.781250s CPU (144.2%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 173272, over cnt = 31(0%), over = 31, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014777s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (105.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 173160, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.030373s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (154.3%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 173208, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.024795s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (63.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1681(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.784   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -47.959  |  23   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.357350s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (100.6%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 8 feed throughs used by 7 nets
PHY-1001 : End commit to database; 0.165929s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (103.6%)

PHY-1001 : Current memory(MB): used = 543, reserve = 515, peak = 543.
PHY-1001 : End phase 3; 0.724558s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (103.5%)

PHY-1003 : Routed, final wirelength = 173208
PHY-1001 : Current memory(MB): used = 544, reserve = 515, peak = 544.
PHY-1001 : End export database. 0.010163s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (153.8%)

PHY-1001 : End detail routing;  7.084145s wall, 7.718750s user + 0.125000s system = 7.843750s CPU (110.7%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6138, tnet num: 1887, tinst num: 771, tnode num: 8354, tedge num: 10811.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : Too much hold violation, please check the src code.
RUN-1003 : finish command "route" in  7.960961s wall, 8.656250s user + 0.125000s system = 8.781250s CPU (110.3%)

RUN-1004 : used memory is 497 MB, reserved memory is 471 MB, peak memory is 544 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      748   out of  19600    3.82%
#reg                     1019   out of  19600    5.20%
#le                      1403
  #lut only               384   out of   1403   27.37%
  #reg only               655   out of   1403   46.69%
  #lut&reg                364   out of   1403   25.94%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         457
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         100
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    36
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1403   |559     |189     |1052    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1014   |259     |128     |829     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |25     |18      |7       |20      |0       |0       |
|    demodu                  |Demodulation                                     |432    |76      |45      |353     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |55     |28      |6       |45      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |17     |7       |0       |17      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |12     |5       |0       |12      |0       |0       |
|    integ                   |Integration                                      |140    |27      |15      |112     |0       |0       |
|    modu                    |Modulation                                       |65     |37      |7       |63      |0       |1       |
|    rs422                   |Rs422Output                                      |316    |73      |46      |260     |0       |4       |
|    trans                   |SquareWaveGenerator                              |36     |28      |8       |21      |0       |0       |
|  u_uart                    |UART_Control                                     |170    |135     |7       |115     |0       |0       |
|    U0                      |speed_select_Tx                                  |31     |24      |7       |14      |0       |0       |
|    U1                      |uart_tx                                          |27     |22      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |112    |89      |0       |85      |0       |0       |
|  wendu                     |DS18B20                                          |201    |156     |45      |74      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1312  
    #2          2       299   
    #3          3       111   
    #4          4        25   
    #5        5-10       67   
    #6        11-50      30   
    #7       101-500     1    
  Average     1.96            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6138, tnet num: 1887, tinst num: 771, tnode num: 8354, tedge num: 10811.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1887 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 771
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1889, pip num: 14215
BIT-1002 : Init feedthrough completely, num: 8
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1257 valid insts, and 37157 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.022396s wall, 16.875000s user + 0.046875s system = 16.921875s CPU (559.9%)

RUN-1004 : used memory is 513 MB, reserved memory is 486 MB, peak memory is 661 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250806_151433.log"
