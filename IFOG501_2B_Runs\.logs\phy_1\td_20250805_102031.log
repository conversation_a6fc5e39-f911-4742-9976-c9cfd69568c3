============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Tue Aug  5 10:20:31 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1585 instances
RUN-0007 : 373 luts, 965 seqs, 126 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2117 nets
RUN-1001 : 1538 nets have 2 pins
RUN-1001 : 479 nets have [3 - 5] pins
RUN-1001 : 58 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     263     
RUN-1001 :   No   |  No   |  Yes  |     123     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1583 instances, 373 luts, 965 seqs, 196 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7540, tnet num: 2115, tinst num: 1583, tnode num: 10706, tedge num: 12774.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2115 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.416627s wall, 0.375000s user + 0.015625s system = 0.390625s CPU (93.8%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 532826
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1583.
PHY-3001 : End clustering;  0.000092s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 465799, overlap = 18
PHY-3002 : Step(2): len = 430454, overlap = 20.25
PHY-3002 : Step(3): len = 411052, overlap = 20.25
PHY-3002 : Step(4): len = 401650, overlap = 15.75
PHY-3002 : Step(5): len = 384065, overlap = 9
PHY-3002 : Step(6): len = 372479, overlap = 18
PHY-3002 : Step(7): len = 360482, overlap = 11.25
PHY-3002 : Step(8): len = 352787, overlap = 11.25
PHY-3002 : Step(9): len = 340406, overlap = 13.5
PHY-3002 : Step(10): len = 333030, overlap = 11.25
PHY-3002 : Step(11): len = 326442, overlap = 13.5
PHY-3002 : Step(12): len = 317498, overlap = 11.25
PHY-3002 : Step(13): len = 311340, overlap = 11.25
PHY-3002 : Step(14): len = 305522, overlap = 11.25
PHY-3002 : Step(15): len = 298331, overlap = 11.25
PHY-3002 : Step(16): len = 291771, overlap = 11.25
PHY-3002 : Step(17): len = 286646, overlap = 13.5
PHY-3002 : Step(18): len = 280585, overlap = 15.75
PHY-3002 : Step(19): len = 274675, overlap = 15.75
PHY-3002 : Step(20): len = 269321, overlap = 15.75
PHY-3002 : Step(21): len = 263555, overlap = 15.75
PHY-3002 : Step(22): len = 258853, overlap = 15.75
PHY-3002 : Step(23): len = 253456, overlap = 15.75
PHY-3002 : Step(24): len = 247697, overlap = 15.75
PHY-3002 : Step(25): len = 242908, overlap = 15.75
PHY-3002 : Step(26): len = 237713, overlap = 15.75
PHY-3002 : Step(27): len = 232993, overlap = 15.75
PHY-3002 : Step(28): len = 227369, overlap = 13.5
PHY-3002 : Step(29): len = 222257, overlap = 18
PHY-3002 : Step(30): len = 217594, overlap = 20.25
PHY-3002 : Step(31): len = 213765, overlap = 20.25
PHY-3002 : Step(32): len = 205126, overlap = 18
PHY-3002 : Step(33): len = 200922, overlap = 20.25
PHY-3002 : Step(34): len = 198099, overlap = 20.25
PHY-3002 : Step(35): len = 190780, overlap = 20.25
PHY-3002 : Step(36): len = 172261, overlap = 18
PHY-3002 : Step(37): len = 168923, overlap = 20.25
PHY-3002 : Step(38): len = 167234, overlap = 20.25
PHY-3002 : Step(39): len = 116852, overlap = 18
PHY-3002 : Step(40): len = 111585, overlap = 15.75
PHY-3002 : Step(41): len = 110810, overlap = 13.5
PHY-3002 : Step(42): len = 108368, overlap = 15.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000102241
PHY-3002 : Step(43): len = 108893, overlap = 13.5
PHY-3002 : Step(44): len = 108437, overlap = 15.75
PHY-3002 : Step(45): len = 107375, overlap = 18
PHY-3002 : Step(46): len = 106803, overlap = 18
PHY-3002 : Step(47): len = 105639, overlap = 15.75
PHY-3002 : Step(48): len = 101954, overlap = 13.5
PHY-3002 : Step(49): len = 97943.5, overlap = 13.625
PHY-3002 : Step(50): len = 97608.9, overlap = 11.25
PHY-3002 : Step(51): len = 95765.2, overlap = 13.5
PHY-3002 : Step(52): len = 92242.6, overlap = 15.875
PHY-3002 : Step(53): len = 90777.6, overlap = 15.75
PHY-3002 : Step(54): len = 88767.7, overlap = 13.5
PHY-3002 : Step(55): len = 87960.9, overlap = 11.25
PHY-3002 : Step(56): len = 87303.5, overlap = 13.5
PHY-3002 : Step(57): len = 85509.7, overlap = 15.75
PHY-3002 : Step(58): len = 83912.4, overlap = 15.75
PHY-3002 : Step(59): len = 81862.9, overlap = 13.5
PHY-3002 : Step(60): len = 81329.2, overlap = 13.5
PHY-3002 : Step(61): len = 78826.3, overlap = 11.25
PHY-3002 : Step(62): len = 77955.5, overlap = 11.25
PHY-3002 : Step(63): len = 76953.2, overlap = 13.5
PHY-3002 : Step(64): len = 76054.8, overlap = 18
PHY-3002 : Step(65): len = 75532.5, overlap = 15.75
PHY-3002 : Step(66): len = 73568.8, overlap = 11.25
PHY-3002 : Step(67): len = 72691.7, overlap = 11.25
PHY-3002 : Step(68): len = 71489.5, overlap = 11.25
PHY-3002 : Step(69): len = 70738.8, overlap = 11.25
PHY-3002 : Step(70): len = 69769.7, overlap = 13.5
PHY-3002 : Step(71): len = 68982.6, overlap = 15.75
PHY-3002 : Step(72): len = 69074.1, overlap = 13.5
PHY-3002 : Step(73): len = 69305.8, overlap = 15.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000204482
PHY-3002 : Step(74): len = 69214.3, overlap = 13.5
PHY-3002 : Step(75): len = 69157.7, overlap = 13.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000408964
PHY-3002 : Step(76): len = 69318.5, overlap = 13.5
PHY-3002 : Step(77): len = 69399.1, overlap = 13.5
PHY-3001 : Before Legalized: Len = 69399.1
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.018850s wall, 0.015625s user + 0.031250s system = 0.046875s CPU (248.7%)

PHY-3001 : After Legalized: Len = 73310.2, Over = 0
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2115 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.129410s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (96.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(78): len = 73294.8, overlap = 5.625
PHY-3002 : Step(79): len = 72109, overlap = 3.375
PHY-3002 : Step(80): len = 71127.9, overlap = 3.6875
PHY-3002 : Step(81): len = 70086.7, overlap = 3.75
PHY-3002 : Step(82): len = 69156.7, overlap = 3.625
PHY-3002 : Step(83): len = 68398.7, overlap = 3.6875
PHY-3002 : Step(84): len = 66355.9, overlap = 3.5625
PHY-3002 : Step(85): len = 64647.3, overlap = 4
PHY-3002 : Step(86): len = 63668.7, overlap = 4.0625
PHY-3002 : Step(87): len = 63042.4, overlap = 4.1875
PHY-3002 : Step(88): len = 62112.6, overlap = 4.4375
PHY-3002 : Step(89): len = 61577.1, overlap = 3.5625
PHY-3002 : Step(90): len = 60245, overlap = 3.28125
PHY-3002 : Step(91): len = 59501.7, overlap = 3.78125
PHY-3002 : Step(92): len = 58405.2, overlap = 3.875
PHY-3002 : Step(93): len = 57456.1, overlap = 3.9375
PHY-3002 : Step(94): len = 56340.4, overlap = 5.5625
PHY-3002 : Step(95): len = 55504.5, overlap = 7.1875
PHY-3002 : Step(96): len = 55152.2, overlap = 9.375
PHY-3002 : Step(97): len = 54862.4, overlap = 9.625
PHY-3002 : Step(98): len = 54196.9, overlap = 10.0312
PHY-3002 : Step(99): len = 53986.8, overlap = 8.15625
PHY-3002 : Step(100): len = 53809.7, overlap = 9.21875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000491967
PHY-3002 : Step(101): len = 53650.3, overlap = 8.65625
PHY-3002 : Step(102): len = 53518.5, overlap = 8.71875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000983934
PHY-3002 : Step(103): len = 53432.5, overlap = 8.71875
PHY-3002 : Step(104): len = 53573.6, overlap = 8.28125
PHY-3002 : Step(105): len = 53488.3, overlap = 8.21875
PHY-3002 : Step(106): len = 53540.5, overlap = 8.59375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2115 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.121965s wall, 0.093750s user + 0.015625s system = 0.109375s CPU (89.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000169328
PHY-3002 : Step(107): len = 54275, overlap = 47.9062
PHY-3002 : Step(108): len = 54710.1, overlap = 49.25
PHY-3002 : Step(109): len = 54627.5, overlap = 44.1875
PHY-3002 : Step(110): len = 54534.9, overlap = 43.1562
PHY-3002 : Step(111): len = 54688.2, overlap = 39.5
PHY-3002 : Step(112): len = 55049.7, overlap = 32.3125
PHY-3002 : Step(113): len = 55067.6, overlap = 31.3438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000338656
PHY-3002 : Step(114): len = 55034.9, overlap = 31.6562
PHY-3002 : Step(115): len = 55079.1, overlap = 31.8438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000677312
PHY-3002 : Step(116): len = 55441.5, overlap = 30.9688
PHY-3002 : Step(117): len = 55989.8, overlap = 30.4688
PHY-3002 : Step(118): len = 56574.6, overlap = 30.0938
PHY-3002 : Step(119): len = 56551, overlap = 29.5312
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7540, tnet num: 2115, tinst num: 1583, tnode num: 10706, tedge num: 12774.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 85.44 peak overflow 2.25
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2117.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 59968, over cnt = 244(0%), over = 1025, worst = 18
PHY-1001 : End global iterations;  0.129684s wall, 0.234375s user + 0.062500s system = 0.296875s CPU (228.9%)

PHY-1001 : Congestion index: top1 = 43.77, top5 = 25.22, top10 = 17.05, top15 = 12.19.
PHY-1001 : End incremental global routing;  0.224144s wall, 0.296875s user + 0.078125s system = 0.375000s CPU (167.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2115 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.116128s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (107.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.388929s wall, 0.453125s user + 0.093750s system = 0.546875s CPU (140.6%)

OPT-1001 : Current memory(MB): used = 208, reserve = 175, peak = 208.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1674/2117.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 59968, over cnt = 244(0%), over = 1025, worst = 18
PHY-1002 : len = 68136, over cnt = 155(0%), over = 285, worst = 9
PHY-1002 : len = 71304, over cnt = 32(0%), over = 36, worst = 4
PHY-1002 : len = 71904, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 72096, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.368314s wall, 0.515625s user + 0.015625s system = 0.531250s CPU (144.2%)

PHY-1001 : Congestion index: top1 = 37.54, top5 = 24.60, top10 = 18.54, top15 = 13.92.
OPT-1001 : End congestion update;  0.495048s wall, 0.625000s user + 0.031250s system = 0.656250s CPU (132.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2115 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.192461s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (97.4%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.697064s wall, 0.828125s user + 0.031250s system = 0.859375s CPU (123.3%)

OPT-1001 : Current memory(MB): used = 212, reserve = 179, peak = 212.
OPT-1001 : End physical optimization;  1.585597s wall, 1.781250s user + 0.125000s system = 1.906250s CPU (120.2%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 373 LUT to BLE ...
SYN-4008 : Packed 373 LUT and 190 SEQ to BLE.
SYN-4003 : Packing 775 remaining SEQ's ...
SYN-4005 : Packed 93 SEQ with LUT/SLICE
SYN-4006 : 108 single LUT's are left
SYN-4006 : 682 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1055/1366 primitive instances ...
PHY-3001 : End packing;  0.188189s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (99.6%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 799 instances
RUN-1001 : 374 mslices, 374 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1935 nets
RUN-1001 : 1360 nets have 2 pins
RUN-1001 : 475 nets have [3 - 5] pins
RUN-1001 : 58 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 797 instances, 748 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 56526, Over = 56.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6316, tnet num: 1933, tinst num: 797, tnode num: 8606, tedge num: 11154.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1933 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.892694s wall, 0.890625s user + 0.000000s system = 0.890625s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.3013e-05
PHY-3002 : Step(120): len = 55334, overlap = 58
PHY-3002 : Step(121): len = 54767.6, overlap = 58.75
PHY-3002 : Step(122): len = 54463.5, overlap = 62.5
PHY-3002 : Step(123): len = 54452.2, overlap = 61.75
PHY-3002 : Step(124): len = 54428.2, overlap = 63.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.6026e-05
PHY-3002 : Step(125): len = 54671.3, overlap = 61.75
PHY-3002 : Step(126): len = 54794.8, overlap = 60.75
PHY-3002 : Step(127): len = 55380.5, overlap = 60.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000172052
PHY-3002 : Step(128): len = 55965.2, overlap = 59
PHY-3002 : Step(129): len = 56291.2, overlap = 55.5
PHY-3001 : Before Legalized: Len = 56291.2
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.212436s wall, 0.203125s user + 0.203125s system = 0.406250s CPU (191.2%)

PHY-3001 : After Legalized: Len = 69727.6, Over = 0
PHY-3001 : Trial Legalized: Len = 69727.6
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1933 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.153503s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (91.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00165704
PHY-3002 : Step(130): len = 65550.5, overlap = 8.5
PHY-3002 : Step(131): len = 64539.4, overlap = 11
PHY-3002 : Step(132): len = 63259.6, overlap = 14
PHY-3002 : Step(133): len = 61911, overlap = 15.75
PHY-3002 : Step(134): len = 60927.1, overlap = 19.25
PHY-3002 : Step(135): len = 60379.8, overlap = 22
PHY-3002 : Step(136): len = 59957, overlap = 23.25
PHY-3002 : Step(137): len = 59608.3, overlap = 24.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00331407
PHY-3002 : Step(138): len = 59775.8, overlap = 24.25
PHY-3002 : Step(139): len = 59823.4, overlap = 25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00662814
PHY-3002 : Step(140): len = 59859, overlap = 25
PHY-3002 : Step(141): len = 59877.9, overlap = 25.25
PHY-3001 : Before Legalized: Len = 59877.9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.023609s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (132.4%)

PHY-3001 : After Legalized: Len = 63961.9, Over = 0
PHY-3001 : Legalized: Len = 63961.9, Over = 0
PHY-3001 : Spreading special nets. 8 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.020950s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (74.6%)

PHY-3001 : 13 instances has been re-located, deltaX = 0, deltaY = 13, maxDist = 1.
PHY-3001 : Final: Len = 64051.9, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6316, tnet num: 1933, tinst num: 797, tnode num: 8606, tedge num: 11154.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 47/1935.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70504, over cnt = 155(0%), over = 231, worst = 7
PHY-1002 : len = 71608, over cnt = 79(0%), over = 98, worst = 5
PHY-1002 : len = 72856, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72888, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.347301s wall, 0.500000s user + 0.125000s system = 0.625000s CPU (180.0%)

PHY-1001 : Congestion index: top1 = 32.16, top5 = 23.08, top10 = 17.79, top15 = 13.88.
PHY-1001 : End incremental global routing;  0.482314s wall, 0.625000s user + 0.125000s system = 0.750000s CPU (155.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1933 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.168533s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (92.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.759630s wall, 0.875000s user + 0.140625s system = 1.015625s CPU (133.7%)

OPT-1001 : Current memory(MB): used = 216, reserve = 183, peak = 216.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1697/1935.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72888, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.015155s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (103.1%)

PHY-1001 : Congestion index: top1 = 32.16, top5 = 23.08, top10 = 17.79, top15 = 13.88.
OPT-1001 : End congestion update;  0.134860s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (104.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1933 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.141513s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (99.4%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.283204s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (104.8%)

OPT-1001 : Current memory(MB): used = 219, reserve = 185, peak = 219.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1933 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.128615s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (97.2%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1697/1935.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72888, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.013869s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.16, top5 = 23.08, top10 = 17.79, top15 = 13.88.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1933 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.114985s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (95.1%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.724138
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  2.311810s wall, 2.406250s user + 0.171875s system = 2.578125s CPU (111.5%)

RUN-1003 : finish command "place" in  11.890278s wall, 15.234375s user + 6.578125s system = 21.812500s CPU (183.4%)

RUN-1004 : used memory is 197 MB, reserved memory is 163 MB, peak memory is 220 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 799 instances
RUN-1001 : 374 mslices, 374 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1935 nets
RUN-1001 : 1360 nets have 2 pins
RUN-1001 : 475 nets have [3 - 5] pins
RUN-1001 : 58 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6316, tnet num: 1933, tinst num: 797, tnode num: 8606, tedge num: 11154.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 374 mslices, 374 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1933 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69960, over cnt = 166(0%), over = 245, worst = 7
PHY-1002 : len = 71128, over cnt = 94(0%), over = 115, worst = 5
PHY-1002 : len = 72512, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.245114s wall, 0.375000s user + 0.062500s system = 0.437500s CPU (178.5%)

PHY-1001 : Congestion index: top1 = 32.11, top5 = 23.01, top10 = 17.73, top15 = 13.84.
PHY-1001 : End global routing;  0.387268s wall, 0.500000s user + 0.062500s system = 0.562500s CPU (145.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 235, reserve = 202, peak = 235.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 496, reserve = 466, peak = 496.
PHY-1001 : End build detailed router design. 7.511582s wall, 7.437500s user + 0.046875s system = 7.484375s CPU (99.6%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 29456, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 2.092054s wall, 2.046875s user + 0.078125s system = 2.125000s CPU (101.6%)

PHY-1001 : Current memory(MB): used = 528, reserve = 499, peak = 528.
PHY-1001 : End phase 1; 2.100152s wall, 2.062500s user + 0.078125s system = 2.140625s CPU (101.9%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 179144, over cnt = 32(0%), over = 32, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 529, reserve = 500, peak = 529.
PHY-1001 : End initial routed; 2.819859s wall, 3.937500s user + 0.234375s system = 4.171875s CPU (147.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1720(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.095   |  -47.745  |  22   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.874996s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (98.2%)

PHY-1001 : Current memory(MB): used = 532, reserve = 503, peak = 532.
PHY-1001 : End phase 2; 3.695153s wall, 4.796875s user + 0.234375s system = 5.031250s CPU (136.2%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 179144, over cnt = 32(0%), over = 32, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.042065s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (74.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 179136, over cnt = 10(0%), over = 10, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.046631s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (134.0%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 179160, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.048289s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1720(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.095   |  -47.745  |  22   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.962609s wall, 0.968750s user + 0.000000s system = 0.968750s CPU (100.6%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.324097s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (96.4%)

PHY-1001 : Current memory(MB): used = 545, reserve = 517, peak = 545.
PHY-1001 : End phase 3; 1.672373s wall, 1.687500s user + 0.000000s system = 1.687500s CPU (100.9%)

PHY-1003 : Routed, final wirelength = 179160
PHY-1001 : Current memory(MB): used = 545, reserve = 517, peak = 545.
PHY-1001 : End export database. 0.019631s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (79.6%)

PHY-1001 : End detail routing;  15.393124s wall, 16.390625s user + 0.375000s system = 16.765625s CPU (108.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6316, tnet num: 1933, tinst num: 797, tnode num: 8606, tedge num: 11154.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : Too much hold violation, please check the src code.
RUN-1003 : finish command "route" in  17.487995s wall, 18.609375s user + 0.437500s system = 19.046875s CPU (108.9%)

RUN-1004 : used memory is 499 MB, reserved memory is 470 MB, peak memory is 545 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      771   out of  19600    3.93%
#reg                     1052   out of  19600    5.37%
#le                      1453
  #lut only               401   out of   1453   27.60%
  #reg only               682   out of   1453   46.94%
  #lut&reg                370   out of   1453   25.46%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         479
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         100
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    39
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1453   |575     |196     |1085    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1048   |266     |133     |862     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |32     |27      |5       |23      |0       |0       |
|    demodu                  |Demodulation                                     |429    |82      |45      |352     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |55     |34      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |9       |0       |16      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |13     |7       |0       |13      |0       |0       |
|    integ                   |Integration                                      |137    |28      |15      |109     |0       |0       |
|    modu                    |Modulation                                       |98     |31      |14      |96      |0       |1       |
|    rs422                   |Rs422Output                                      |317    |71      |46      |262     |0       |4       |
|    trans                   |SquareWaveGenerator                              |35     |27      |8       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |181    |141     |7       |115     |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |25     |25      |0       |15      |0       |0       |
|    U2                      |Ctrl_Data                                        |119    |88      |0       |84      |0       |0       |
|  wendu                     |DS18B20                                          |202    |157     |45      |74      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1321  
    #2          2       332   
    #3          3       117   
    #4          4        26   
    #5        5-10       62   
    #6        11-50      32   
    #7       101-500     1    
  Average     1.97            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6316, tnet num: 1933, tinst num: 797, tnode num: 8606, tedge num: 11154.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1933 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 797
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1935, pip num: 14594
BIT-1002 : Init feedthrough completely, num: 4
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1298 valid insts, and 38184 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  6.415664s wall, 32.343750s user + 0.171875s system = 32.515625s CPU (506.8%)

RUN-1004 : used memory is 515 MB, reserved memory is 485 MB, peak memory is 663 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250805_102031.log"
