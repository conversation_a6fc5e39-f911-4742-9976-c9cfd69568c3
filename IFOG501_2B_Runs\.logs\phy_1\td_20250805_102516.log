============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Tue Aug  5 10:25:16 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1586 instances
RUN-0007 : 373 luts, 965 seqs, 127 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2118 nets
RUN-1001 : 1543 nets have 2 pins
RUN-1001 : 468 nets have [3 - 5] pins
RUN-1001 : 65 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     263     
RUN-1001 :   No   |  No   |  Yes  |     123     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1584 instances, 373 luts, 965 seqs, 197 slices, 20 macros(197 instances: 127 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7556, tnet num: 2116, tinst num: 1584, tnode num: 10722, tedge num: 12804.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2116 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.260522s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (102.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 532432
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1584.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 468017, overlap = 18
PHY-3002 : Step(2): len = 434809, overlap = 20.25
PHY-3002 : Step(3): len = 415592, overlap = 20.25
PHY-3002 : Step(4): len = 406034, overlap = 13.5
PHY-3002 : Step(5): len = 386932, overlap = 11.25
PHY-3002 : Step(6): len = 376232, overlap = 18
PHY-3002 : Step(7): len = 365718, overlap = 9
PHY-3002 : Step(8): len = 356811, overlap = 13.5
PHY-3002 : Step(9): len = 346999, overlap = 13.5
PHY-3002 : Step(10): len = 339455, overlap = 13.5
PHY-3002 : Step(11): len = 332269, overlap = 13.5
PHY-3002 : Step(12): len = 325472, overlap = 13.5
PHY-3002 : Step(13): len = 318945, overlap = 13.5
PHY-3002 : Step(14): len = 312266, overlap = 13.5
PHY-3002 : Step(15): len = 306155, overlap = 13.5
PHY-3002 : Step(16): len = 299682, overlap = 13.5
PHY-3002 : Step(17): len = 292148, overlap = 13.5
PHY-3002 : Step(18): len = 286227, overlap = 15.75
PHY-3002 : Step(19): len = 281746, overlap = 15.75
PHY-3002 : Step(20): len = 272629, overlap = 15.75
PHY-3002 : Step(21): len = 266520, overlap = 15.75
PHY-3002 : Step(22): len = 263196, overlap = 15.75
PHY-3002 : Step(23): len = 254773, overlap = 13.5
PHY-3002 : Step(24): len = 246717, overlap = 13.5
PHY-3002 : Step(25): len = 243501, overlap = 13.5
PHY-3002 : Step(26): len = 238480, overlap = 13.5
PHY-3002 : Step(27): len = 209135, overlap = 20.25
PHY-3002 : Step(28): len = 201430, overlap = 20.25
PHY-3002 : Step(29): len = 199661, overlap = 20.25
PHY-3002 : Step(30): len = 183333, overlap = 15.75
PHY-3002 : Step(31): len = 155893, overlap = 18
PHY-3002 : Step(32): len = 153632, overlap = 18
PHY-3002 : Step(33): len = 149748, overlap = 15.75
PHY-3002 : Step(34): len = 142316, overlap = 18
PHY-3002 : Step(35): len = 140590, overlap = 15.75
PHY-3002 : Step(36): len = 137007, overlap = 18
PHY-3002 : Step(37): len = 134794, overlap = 18
PHY-3002 : Step(38): len = 132363, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000108562
PHY-3002 : Step(39): len = 132869, overlap = 11.25
PHY-3002 : Step(40): len = 131834, overlap = 11.25
PHY-3002 : Step(41): len = 130867, overlap = 11.25
PHY-3002 : Step(42): len = 128933, overlap = 15.75
PHY-3002 : Step(43): len = 125665, overlap = 13.5
PHY-3002 : Step(44): len = 123567, overlap = 13.5
PHY-3002 : Step(45): len = 120505, overlap = 11.25
PHY-3002 : Step(46): len = 119246, overlap = 11.25
PHY-3002 : Step(47): len = 116738, overlap = 13.5
PHY-3002 : Step(48): len = 112685, overlap = 15.75
PHY-3002 : Step(49): len = 109874, overlap = 15.75
PHY-3002 : Step(50): len = 108667, overlap = 15.75
PHY-3002 : Step(51): len = 106728, overlap = 13.5
PHY-3002 : Step(52): len = 102748, overlap = 11.25
PHY-3002 : Step(53): len = 98890.3, overlap = 11.25
PHY-3002 : Step(54): len = 98152.9, overlap = 13.5
PHY-3002 : Step(55): len = 96245.3, overlap = 11.25
PHY-3002 : Step(56): len = 93573.8, overlap = 15.75
PHY-3002 : Step(57): len = 92003.5, overlap = 15.75
PHY-3002 : Step(58): len = 90828.5, overlap = 13.5
PHY-3002 : Step(59): len = 89210.4, overlap = 11.25
PHY-3002 : Step(60): len = 88466.2, overlap = 11.25
PHY-3002 : Step(61): len = 87374, overlap = 13.5
PHY-3002 : Step(62): len = 85937.5, overlap = 15.75
PHY-3002 : Step(63): len = 83223.9, overlap = 13.5
PHY-3002 : Step(64): len = 83009.3, overlap = 13.5
PHY-3002 : Step(65): len = 81419.5, overlap = 15.75
PHY-3002 : Step(66): len = 80223.5, overlap = 13.5
PHY-3002 : Step(67): len = 79820.8, overlap = 13.5
PHY-3002 : Step(68): len = 78950.1, overlap = 15.75
PHY-3002 : Step(69): len = 78435.8, overlap = 15.75
PHY-3002 : Step(70): len = 77110, overlap = 15.75
PHY-3002 : Step(71): len = 76137.6, overlap = 13.5
PHY-3002 : Step(72): len = 75579.7, overlap = 13.5
PHY-3002 : Step(73): len = 74933.2, overlap = 13.5
PHY-3002 : Step(74): len = 74176.2, overlap = 15.75
PHY-3002 : Step(75): len = 73939.6, overlap = 15.75
PHY-3002 : Step(76): len = 73824.5, overlap = 13.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000217124
PHY-3002 : Step(77): len = 74028.7, overlap = 13.5
PHY-3002 : Step(78): len = 74123.1, overlap = 15.75
PHY-3002 : Step(79): len = 74102.4, overlap = 15.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000434249
PHY-3002 : Step(80): len = 74336.4, overlap = 15.75
PHY-3002 : Step(81): len = 74386.8, overlap = 13.5
PHY-3001 : Before Legalized: Len = 74386.8
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007489s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (208.6%)

PHY-3001 : After Legalized: Len = 79040.9, Over = 0
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2116 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.063298s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (123.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(82): len = 78840, overlap = 2.25
PHY-3002 : Step(83): len = 77027, overlap = 2.6875
PHY-3002 : Step(84): len = 75116.3, overlap = 2.625
PHY-3002 : Step(85): len = 73302.4, overlap = 2.25
PHY-3002 : Step(86): len = 71955.5, overlap = 2.375
PHY-3002 : Step(87): len = 70370.5, overlap = 2.75
PHY-3002 : Step(88): len = 68973.5, overlap = 3.4375
PHY-3002 : Step(89): len = 67336.5, overlap = 2.75
PHY-3002 : Step(90): len = 66414.8, overlap = 2.75
PHY-3002 : Step(91): len = 64306.1, overlap = 3.4375
PHY-3002 : Step(92): len = 63020.2, overlap = 4.375
PHY-3002 : Step(93): len = 61942.9, overlap = 4.28125
PHY-3002 : Step(94): len = 60706, overlap = 4.25
PHY-3002 : Step(95): len = 59530.2, overlap = 5
PHY-3002 : Step(96): len = 58185.4, overlap = 5.5625
PHY-3002 : Step(97): len = 57352.2, overlap = 8.21875
PHY-3002 : Step(98): len = 56568.6, overlap = 11.625
PHY-3002 : Step(99): len = 55869.2, overlap = 11.9062
PHY-3002 : Step(100): len = 55071.6, overlap = 11.9375
PHY-3002 : Step(101): len = 54883.6, overlap = 11.8125
PHY-3002 : Step(102): len = 54393.7, overlap = 12.5312
PHY-3002 : Step(103): len = 54000.5, overlap = 12.7812
PHY-3002 : Step(104): len = 53755.5, overlap = 13.5
PHY-3002 : Step(105): len = 53365, overlap = 13.5938
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000633712
PHY-3002 : Step(106): len = 52927, overlap = 11.4375
PHY-3002 : Step(107): len = 52982.4, overlap = 11.4375
PHY-3002 : Step(108): len = 52953.5, overlap = 11.375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00126742
PHY-3002 : Step(109): len = 52624.2, overlap = 11.0625
PHY-3002 : Step(110): len = 52293, overlap = 11.9375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2116 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.068102s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (91.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000106835
PHY-3002 : Step(111): len = 53505.2, overlap = 45.7812
PHY-3002 : Step(112): len = 54246.1, overlap = 44.3125
PHY-3002 : Step(113): len = 54166.4, overlap = 43.2812
PHY-3002 : Step(114): len = 53832.6, overlap = 42.9688
PHY-3002 : Step(115): len = 53589.9, overlap = 42.4375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000213669
PHY-3002 : Step(116): len = 54018.3, overlap = 42.25
PHY-3002 : Step(117): len = 54711.4, overlap = 39.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000427339
PHY-3002 : Step(118): len = 54674.9, overlap = 38.25
PHY-3002 : Step(119): len = 54956, overlap = 36.7188
PHY-3002 : Step(120): len = 55673, overlap = 33.3438
PHY-3002 : Step(121): len = 56345.3, overlap = 31.2188
PHY-3002 : Step(122): len = 56417.2, overlap = 31.0938
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7556, tnet num: 2116, tinst num: 1584, tnode num: 10722, tedge num: 12804.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 84.88 peak overflow 2.62
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2118.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 59496, over cnt = 248(0%), over = 989, worst = 21
PHY-1001 : End global iterations;  0.074479s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (188.8%)

PHY-1001 : Congestion index: top1 = 43.99, top5 = 25.62, top10 = 16.55, top15 = 11.86.
PHY-1001 : End incremental global routing;  0.124453s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (150.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2116 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.069234s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (112.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.222784s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (133.3%)

OPT-1001 : Current memory(MB): used = 210, reserve = 177, peak = 210.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1607/2118.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 59496, over cnt = 248(0%), over = 989, worst = 21
PHY-1002 : len = 66976, over cnt = 179(0%), over = 332, worst = 11
PHY-1002 : len = 70240, over cnt = 30(0%), over = 45, worst = 5
PHY-1002 : len = 70560, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 70720, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.103351s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (120.9%)

PHY-1001 : Congestion index: top1 = 37.22, top5 = 24.71, top10 = 17.99, top15 = 13.43.
OPT-1001 : End congestion update;  0.146972s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (106.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2116 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057125s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (109.4%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.207127s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (105.6%)

OPT-1001 : Current memory(MB): used = 213, reserve = 180, peak = 213.
OPT-1001 : End physical optimization;  0.679353s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (110.4%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 373 LUT to BLE ...
SYN-4008 : Packed 373 LUT and 190 SEQ to BLE.
SYN-4003 : Packing 775 remaining SEQ's ...
SYN-4005 : Packed 92 SEQ with LUT/SLICE
SYN-4006 : 106 single LUT's are left
SYN-4006 : 683 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1056/1368 primitive instances ...
PHY-3001 : End packing;  0.046399s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (101.0%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 797 instances
RUN-1001 : 373 mslices, 373 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1936 nets
RUN-1001 : 1366 nets have 2 pins
RUN-1001 : 463 nets have [3 - 5] pins
RUN-1001 : 66 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 795 instances, 746 slices, 20 macros(197 instances: 127 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 56117, Over = 52
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6314, tnet num: 1934, tinst num: 795, tnode num: 8602, tedge num: 11149.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.292315s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (96.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.95642e-05
PHY-3002 : Step(123): len = 55242.7, overlap = 53.75
PHY-3002 : Step(124): len = 54695, overlap = 52.75
PHY-3002 : Step(125): len = 54279.6, overlap = 52.5
PHY-3002 : Step(126): len = 54207.1, overlap = 54.5
PHY-3002 : Step(127): len = 54294.6, overlap = 53
PHY-3002 : Step(128): len = 54083.5, overlap = 52.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.91284e-05
PHY-3002 : Step(129): len = 54274.5, overlap = 53
PHY-3002 : Step(130): len = 55127.1, overlap = 53.5
PHY-3002 : Step(131): len = 55311.9, overlap = 53
PHY-3002 : Step(132): len = 55537, overlap = 50.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000158257
PHY-3002 : Step(133): len = 55762.8, overlap = 49
PHY-3002 : Step(134): len = 56251.5, overlap = 50
PHY-3001 : Before Legalized: Len = 56251.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.104433s wall, 0.031250s user + 0.109375s system = 0.140625s CPU (134.7%)

PHY-3001 : After Legalized: Len = 69709.7, Over = 0
PHY-3001 : Trial Legalized: Len = 69709.7
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.051473s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00249898
PHY-3002 : Step(135): len = 67394.7, overlap = 3.25
PHY-3002 : Step(136): len = 65883.3, overlap = 7.5
PHY-3002 : Step(137): len = 64504.5, overlap = 9
PHY-3002 : Step(138): len = 62689.8, overlap = 12
PHY-3002 : Step(139): len = 62089.7, overlap = 14
PHY-3002 : Step(140): len = 61684.6, overlap = 13.75
PHY-3002 : Step(141): len = 61105.7, overlap = 15.75
PHY-3002 : Step(142): len = 60414.6, overlap = 16.25
PHY-3002 : Step(143): len = 59900.4, overlap = 15.75
PHY-3002 : Step(144): len = 59674.4, overlap = 17
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0046048
PHY-3002 : Step(145): len = 59868, overlap = 18
PHY-3002 : Step(146): len = 59936.2, overlap = 17.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00920961
PHY-3002 : Step(147): len = 59916.6, overlap = 18
PHY-3002 : Step(148): len = 59909.6, overlap = 18
PHY-3001 : Before Legalized: Len = 59909.6
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005314s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 63706, Over = 0
PHY-3001 : Legalized: Len = 63706, Over = 0
PHY-3001 : Spreading special nets. 8 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005568s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 15 instances has been re-located, deltaX = 4, deltaY = 12, maxDist = 2.
PHY-3001 : Final: Len = 63690, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6314, tnet num: 1934, tinst num: 795, tnode num: 8602, tedge num: 11149.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 69/1936.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69872, over cnt = 142(0%), over = 229, worst = 8
PHY-1002 : len = 70992, over cnt = 76(0%), over = 90, worst = 3
PHY-1002 : len = 71944, over cnt = 9(0%), over = 10, worst = 2
PHY-1002 : len = 72064, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 72144, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.117971s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (119.2%)

PHY-1001 : Congestion index: top1 = 32.28, top5 = 22.97, top10 = 17.66, top15 = 13.72.
PHY-1001 : End incremental global routing;  0.170415s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (119.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060623s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.1%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.260750s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (113.9%)

OPT-1001 : Current memory(MB): used = 214, reserve = 182, peak = 215.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1703/1936.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72144, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.010168s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (153.7%)

PHY-1001 : Congestion index: top1 = 32.28, top5 = 22.97, top10 = 17.66, top15 = 13.72.
OPT-1001 : End congestion update;  0.058629s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (106.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047774s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.1%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 755 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 795 instances, 746 slices, 20 macros(197 instances: 127 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 63642, Over = 0
PHY-3001 : End spreading;  0.004729s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 63642, Over = 0
PHY-3001 : End incremental legalization;  0.033800s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (92.5%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 2 cells processed and 150 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.153337s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (101.9%)

OPT-1001 : Current memory(MB): used = 218, reserve = 186, peak = 218.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049350s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1695/1936.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72072, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72072, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72088, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.024257s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (64.4%)

PHY-1001 : Congestion index: top1 = 32.24, top5 = 22.90, top10 = 17.59, top15 = 13.68.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051998s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (90.1%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.896552
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.863824s wall, 0.875000s user + 0.015625s system = 0.890625s CPU (103.1%)

RUN-1003 : finish command "place" in  5.172377s wall, 7.171875s user + 2.765625s system = 9.937500s CPU (192.1%)

RUN-1004 : used memory is 195 MB, reserved memory is 161 MB, peak memory is 218 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 797 instances
RUN-1001 : 373 mslices, 373 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1936 nets
RUN-1001 : 1366 nets have 2 pins
RUN-1001 : 463 nets have [3 - 5] pins
RUN-1001 : 66 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6314, tnet num: 1934, tinst num: 795, tnode num: 8602, tedge num: 11149.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 373 mslices, 373 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68872, over cnt = 149(0%), over = 235, worst = 7
PHY-1002 : len = 70088, over cnt = 86(0%), over = 100, worst = 3
PHY-1002 : len = 71368, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 71448, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.121410s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (180.2%)

PHY-1001 : Congestion index: top1 = 32.07, top5 = 22.71, top10 = 17.48, top15 = 13.60.
PHY-1001 : End global routing;  0.177163s wall, 0.234375s user + 0.031250s system = 0.265625s CPU (149.9%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 233, reserve = 202, peak = 233.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 494, reserve = 467, peak = 494.
PHY-1001 : End build detailed router design. 3.145542s wall, 3.062500s user + 0.046875s system = 3.109375s CPU (98.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 29496, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.061085s wall, 1.031250s user + 0.031250s system = 1.062500s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 526, reserve = 501, peak = 526.
PHY-1001 : End phase 1; 1.069260s wall, 1.046875s user + 0.031250s system = 1.078125s CPU (100.8%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 180648, over cnt = 30(0%), over = 30, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 529, reserve = 502, peak = 529.
PHY-1001 : End initial routed; 1.622257s wall, 2.656250s user + 0.125000s system = 2.781250s CPU (171.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1720(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.095   |  -47.872  |  22   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.336690s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (102.1%)

PHY-1001 : Current memory(MB): used = 531, reserve = 504, peak = 531.
PHY-1001 : End phase 2; 1.959040s wall, 3.000000s user + 0.125000s system = 3.125000s CPU (159.5%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 180648, over cnt = 30(0%), over = 30, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015831s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (98.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 180512, over cnt = 13(0%), over = 13, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.024413s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (192.0%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 180440, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.028935s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (108.0%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 180472, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.020504s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (76.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1720(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.095   |  -47.872  |  22   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.341392s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (100.7%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 6 feed throughs used by 6 nets
PHY-1001 : End commit to database; 0.167087s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (102.9%)

PHY-1001 : Current memory(MB): used = 547, reserve = 519, peak = 547.
PHY-1001 : End phase 3; 0.734364s wall, 0.718750s user + 0.031250s system = 0.750000s CPU (102.1%)

PHY-1003 : Routed, final wirelength = 180472
PHY-1001 : Current memory(MB): used = 547, reserve = 519, peak = 547.
PHY-1001 : End export database. 0.009995s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (156.3%)

PHY-1001 : End detail routing;  7.092374s wall, 8.000000s user + 0.250000s system = 8.250000s CPU (116.3%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6314, tnet num: 1934, tinst num: 795, tnode num: 8602, tedge num: 11149.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[21] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dia[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[33] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dib[6] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[35] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dib[8] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[6] slack -86ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[47] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[2] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[52] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[7] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[7] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[2] slack -86ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[13] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[4] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_52.mi[0] slack -3095ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_52.mi[1] slack -2963ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_55.mi[0] slack -2937ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_55.mi[1] slack -2963ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_58.mi[0] slack -2963ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_58.mi[1] slack -2951ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_61.mi[0] slack -2724ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_61.mi[1] slack -2855ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_64.mi[0] slack -3095ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_64.mi[1] slack -2963ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_67.mi[0] slack -3095ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_67.mi[1] slack -2855ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_70.mi[0] slack -3083ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_70.mi[1] slack -3095ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_73.mi[0] slack -3083ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_73.mi[1] slack -2855ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6434, tnet num: 1994, tinst num: 855, tnode num: 8722, tedge num: 11269.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_70_mi[0] slack -751ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_70_mi[1] slack -892ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_61_mi[1] slack -589ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_64_mi[1] slack -665ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_64_mi[0] slack -506ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_67_mi[1] slack -655ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg2_syn_67_mi[0] slack -33ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_61_mi[0] slack -602ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_52_mi[1] slack -218ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_52_mi[0] slack -881ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_55_mi[1] slack -742ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_55_mi[0] slack -621ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_58_mi[1] slack -431ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_58_mi[0] slack -337ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_73_mi[1] slack -347ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_73_mi[0] slack -1052ps
RUN-1001 : End hold fix;  3.108814s wall, 3.031250s user + 0.296875s system = 3.328125s CPU (107.1%)

RUN-1003 : finish command "route" in  10.707114s wall, 11.578125s user + 0.593750s system = 12.171875s CPU (113.7%)

RUN-1004 : used memory is 536 MB, reserved memory is 511 MB, peak memory is 547 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      889   out of  19600    4.54%
#reg                     1052   out of  19600    5.37%
#le                      1572
  #lut only               520   out of   1572   33.08%
  #reg only               683   out of   1572   43.45%
  #lut&reg                369   out of   1572   23.47%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         476
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         100
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q0    41
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1572   |692     |197     |1085    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1071   |291     |134     |862     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |24     |19      |5       |21      |0       |0       |
|    demodu                  |Demodulation                                     |455    |101     |46      |350     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |55     |30      |6       |45      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |14     |6       |0       |14      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |13     |6       |0       |13      |0       |0       |
|    integ                   |Integration                                      |146    |31      |15      |118     |0       |0       |
|    modu                    |Modulation                                       |97     |44      |14      |95      |0       |1       |
|    rs422                   |Rs422Output                                      |313    |68      |46      |258     |0       |4       |
|    trans                   |SquareWaveGenerator                              |36     |28      |8       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |174    |130     |7       |117     |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |24     |18      |0       |15      |0       |0       |
|    U2                      |Ctrl_Data                                        |113    |84      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |305    |260     |45      |72      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1387  
    #2          2       328   
    #3          3       112   
    #4          4        23   
    #5        5-10       70   
    #6        11-50      31   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6434, tnet num: 1994, tinst num: 855, tnode num: 8722, tedge num: 11269.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1994 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 855
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1996, pip num: 14609
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 10
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1366 valid insts, and 39085 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.298447s wall, 18.500000s user + 0.062500s system = 18.562500s CPU (562.8%)

RUN-1004 : used memory is 548 MB, reserved memory is 518 MB, peak memory is 680 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250805_102516.log"
