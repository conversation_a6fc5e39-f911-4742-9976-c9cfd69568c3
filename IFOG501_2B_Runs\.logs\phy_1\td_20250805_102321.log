============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Tue Aug  5 10:23:21 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1582 instances
RUN-0007 : 370 luts, 965 seqs, 126 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2114 nets
RUN-1001 : 1540 nets have 2 pins
RUN-1001 : 473 nets have [3 - 5] pins
RUN-1001 : 59 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     263     
RUN-1001 :   No   |  No   |  Yes  |     123     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1580 instances, 370 luts, 965 seqs, 196 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7540, tnet num: 2112, tinst num: 1580, tnode num: 10706, tedge num: 12780.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2112 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.268060s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (99.1%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 513310
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1580.
PHY-3001 : End clustering;  0.000031s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 438244, overlap = 20.25
PHY-3002 : Step(2): len = 417935, overlap = 18
PHY-3002 : Step(3): len = 403414, overlap = 18
PHY-3002 : Step(4): len = 394240, overlap = 15.75
PHY-3002 : Step(5): len = 381909, overlap = 13.5
PHY-3002 : Step(6): len = 372239, overlap = 13.5
PHY-3002 : Step(7): len = 363582, overlap = 13.5
PHY-3002 : Step(8): len = 354374, overlap = 15.75
PHY-3002 : Step(9): len = 346662, overlap = 15.75
PHY-3002 : Step(10): len = 338271, overlap = 15.75
PHY-3002 : Step(11): len = 330667, overlap = 15.75
PHY-3002 : Step(12): len = 323310, overlap = 13.5
PHY-3002 : Step(13): len = 316237, overlap = 13.5
PHY-3002 : Step(14): len = 307818, overlap = 13.5
PHY-3002 : Step(15): len = 300574, overlap = 13.5
PHY-3002 : Step(16): len = 295171, overlap = 11.25
PHY-3002 : Step(17): len = 287452, overlap = 13.5
PHY-3002 : Step(18): len = 279668, overlap = 13.5
PHY-3002 : Step(19): len = 274562, overlap = 13.5
PHY-3002 : Step(20): len = 269333, overlap = 13.5
PHY-3002 : Step(21): len = 262342, overlap = 13.5
PHY-3002 : Step(22): len = 257497, overlap = 13.5
PHY-3002 : Step(23): len = 252607, overlap = 13.5
PHY-3002 : Step(24): len = 244787, overlap = 13.5
PHY-3002 : Step(25): len = 239464, overlap = 13.5
PHY-3002 : Step(26): len = 236241, overlap = 13.5
PHY-3002 : Step(27): len = 228050, overlap = 18
PHY-3002 : Step(28): len = 220092, overlap = 18
PHY-3002 : Step(29): len = 217227, overlap = 20.25
PHY-3002 : Step(30): len = 211252, overlap = 20.25
PHY-3002 : Step(31): len = 198368, overlap = 20.25
PHY-3002 : Step(32): len = 194245, overlap = 20.25
PHY-3002 : Step(33): len = 191932, overlap = 20.25
PHY-3002 : Step(34): len = 150572, overlap = 18
PHY-3002 : Step(35): len = 141881, overlap = 20.25
PHY-3002 : Step(36): len = 140942, overlap = 20.25
PHY-3002 : Step(37): len = 136521, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000104235
PHY-3002 : Step(38): len = 137780, overlap = 18
PHY-3002 : Step(39): len = 136964, overlap = 18
PHY-3002 : Step(40): len = 135614, overlap = 15.75
PHY-3002 : Step(41): len = 131529, overlap = 13.5
PHY-3002 : Step(42): len = 129309, overlap = 15.75
PHY-3002 : Step(43): len = 127223, overlap = 11.25
PHY-3002 : Step(44): len = 124741, overlap = 9
PHY-3002 : Step(45): len = 122123, overlap = 9
PHY-3002 : Step(46): len = 119225, overlap = 11.25
PHY-3002 : Step(47): len = 117524, overlap = 11.25
PHY-3002 : Step(48): len = 116162, overlap = 11.25
PHY-3002 : Step(49): len = 113765, overlap = 15.75
PHY-3002 : Step(50): len = 109387, overlap = 15.75
PHY-3002 : Step(51): len = 108550, overlap = 13.5
PHY-3002 : Step(52): len = 105465, overlap = 15.75
PHY-3002 : Step(53): len = 103040, overlap = 13.5
PHY-3002 : Step(54): len = 101564, overlap = 13.5
PHY-3002 : Step(55): len = 99910.3, overlap = 15.75
PHY-3002 : Step(56): len = 97762.2, overlap = 15.75
PHY-3002 : Step(57): len = 97061.1, overlap = 15.75
PHY-3002 : Step(58): len = 95717.3, overlap = 18
PHY-3002 : Step(59): len = 90491.1, overlap = 15.75
PHY-3002 : Step(60): len = 86845.5, overlap = 15.75
PHY-3002 : Step(61): len = 86246, overlap = 15.75
PHY-3002 : Step(62): len = 84857, overlap = 15.75
PHY-3002 : Step(63): len = 84144.4, overlap = 13.5
PHY-3002 : Step(64): len = 83698.2, overlap = 15.75
PHY-3002 : Step(65): len = 82200, overlap = 18
PHY-3002 : Step(66): len = 80303.1, overlap = 15.75
PHY-3002 : Step(67): len = 77508.3, overlap = 13.5
PHY-3002 : Step(68): len = 76285.7, overlap = 13.5
PHY-3002 : Step(69): len = 76042.3, overlap = 13.5
PHY-3002 : Step(70): len = 75306.1, overlap = 15.75
PHY-3002 : Step(71): len = 73502.3, overlap = 15.75
PHY-3002 : Step(72): len = 69518.1, overlap = 16.875
PHY-3002 : Step(73): len = 67407.6, overlap = 19.5
PHY-3002 : Step(74): len = 66705.1, overlap = 19.375
PHY-3002 : Step(75): len = 66477.7, overlap = 17.125
PHY-3002 : Step(76): len = 66533.7, overlap = 17.125
PHY-3002 : Step(77): len = 65990.3, overlap = 14.75
PHY-3002 : Step(78): len = 65825.4, overlap = 14.625
PHY-3002 : Step(79): len = 65624.7, overlap = 12.375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00020847
PHY-3002 : Step(80): len = 66628.7, overlap = 10.125
PHY-3002 : Step(81): len = 67255.8, overlap = 10
PHY-3002 : Step(82): len = 67419.6, overlap = 10
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000416941
PHY-3002 : Step(83): len = 67942.1, overlap = 10
PHY-3002 : Step(84): len = 68098.1, overlap = 10
PHY-3002 : Step(85): len = 68046.5, overlap = 10
PHY-3002 : Step(86): len = 68030.6, overlap = 10
PHY-3002 : Step(87): len = 68259, overlap = 10
PHY-3001 : Before Legalized: Len = 68259
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006962s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (224.4%)

PHY-3001 : After Legalized: Len = 72004.2, Over = 1
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2112 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.066528s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (117.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00106788
PHY-3002 : Step(88): len = 71892.1, overlap = 8.6875
PHY-3002 : Step(89): len = 70343.1, overlap = 8.4375
PHY-3002 : Step(90): len = 69424.8, overlap = 8.3125
PHY-3002 : Step(91): len = 68295.4, overlap = 7.5625
PHY-3002 : Step(92): len = 66709.6, overlap = 7.4375
PHY-3002 : Step(93): len = 65484.2, overlap = 6.5
PHY-3002 : Step(94): len = 64413.9, overlap = 5.8125
PHY-3002 : Step(95): len = 62680.2, overlap = 4
PHY-3002 : Step(96): len = 60518.2, overlap = 4.59375
PHY-3002 : Step(97): len = 58875.2, overlap = 6.71875
PHY-3002 : Step(98): len = 57527.2, overlap = 8.1875
PHY-3002 : Step(99): len = 55766.6, overlap = 9.65625
PHY-3002 : Step(100): len = 54043.4, overlap = 16.1562
PHY-3002 : Step(101): len = 53062.6, overlap = 17.25
PHY-3002 : Step(102): len = 51564.1, overlap = 22.5938
PHY-3002 : Step(103): len = 50359.6, overlap = 22.4688
PHY-3002 : Step(104): len = 49588.5, overlap = 19.0625
PHY-3002 : Step(105): len = 49033.2, overlap = 17.9062
PHY-3002 : Step(106): len = 48585.1, overlap = 15.4062
PHY-3002 : Step(107): len = 48070.8, overlap = 14.5312
PHY-3002 : Step(108): len = 47389.5, overlap = 13.4688
PHY-3002 : Step(109): len = 47030, overlap = 13.5312
PHY-3002 : Step(110): len = 46268, overlap = 12.4688
PHY-3002 : Step(111): len = 45718.8, overlap = 10.9062
PHY-3002 : Step(112): len = 45612.1, overlap = 11.1875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00213576
PHY-3002 : Step(113): len = 45403.2, overlap = 10.8438
PHY-3002 : Step(114): len = 45270.1, overlap = 10.7188
PHY-3002 : Step(115): len = 45300.9, overlap = 10.6562
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00427152
PHY-3002 : Step(116): len = 45189.8, overlap = 10.5312
PHY-3002 : Step(117): len = 45189.8, overlap = 10.5312
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2112 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.071593s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (87.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.23941e-05
PHY-3002 : Step(118): len = 46117.3, overlap = 51.25
PHY-3002 : Step(119): len = 46598.4, overlap = 47.625
PHY-3002 : Step(120): len = 47410, overlap = 45.75
PHY-3002 : Step(121): len = 47803.8, overlap = 36.4375
PHY-3002 : Step(122): len = 47849.7, overlap = 36.1875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000144788
PHY-3002 : Step(123): len = 47696.1, overlap = 36.125
PHY-3002 : Step(124): len = 48083.2, overlap = 36.4688
PHY-3002 : Step(125): len = 48366.6, overlap = 33.8438
PHY-3002 : Step(126): len = 48321.2, overlap = 33.3125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000289577
PHY-3002 : Step(127): len = 48331.9, overlap = 32.9375
PHY-3002 : Step(128): len = 48434.1, overlap = 32.5625
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7540, tnet num: 2112, tinst num: 1580, tnode num: 10706, tedge num: 12780.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 97.72 peak overflow 3.53
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2114.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 50520, over cnt = 215(0%), over = 968, worst = 26
PHY-1001 : End global iterations;  0.062148s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (150.9%)

PHY-1001 : Congestion index: top1 = 43.28, top5 = 23.69, top10 = 14.82, top15 = 10.55.
PHY-1001 : End incremental global routing;  0.112811s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (124.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2112 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.068335s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (91.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.211328s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (110.9%)

OPT-1001 : Current memory(MB): used = 209, reserve = 176, peak = 209.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1582/2114.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 50520, over cnt = 215(0%), over = 968, worst = 26
PHY-1002 : len = 58184, over cnt = 164(0%), over = 411, worst = 25
PHY-1002 : len = 62976, over cnt = 56(0%), over = 107, worst = 12
PHY-1002 : len = 64264, over cnt = 16(0%), over = 16, worst = 1
PHY-1002 : len = 65200, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.096706s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (145.4%)

PHY-1001 : Congestion index: top1 = 38.28, top5 = 23.64, top10 = 16.85, top15 = 12.59.
OPT-1001 : End congestion update;  0.139536s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (134.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2112 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057933s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (107.9%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.200765s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (124.5%)

OPT-1001 : Current memory(MB): used = 212, reserve = 179, peak = 212.
OPT-1001 : End physical optimization;  0.673196s wall, 0.812500s user + 0.031250s system = 0.843750s CPU (125.3%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 370 LUT to BLE ...
SYN-4008 : Packed 370 LUT and 190 SEQ to BLE.
SYN-4003 : Packing 775 remaining SEQ's ...
SYN-4005 : Packed 77 SEQ with LUT/SLICE
SYN-4006 : 118 single LUT's are left
SYN-4006 : 698 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1068/1379 primitive instances ...
PHY-3001 : End packing;  0.047937s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.8%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 804 instances
RUN-1001 : 376 mslices, 377 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1932 nets
RUN-1001 : 1365 nets have 2 pins
RUN-1001 : 462 nets have [3 - 5] pins
RUN-1001 : 63 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 802 instances, 753 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 48592.4, Over = 59.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6306, tnet num: 1930, tinst num: 802, tnode num: 8586, tedge num: 11143.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1930 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.292698s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (101.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.92987e-05
PHY-3002 : Step(129): len = 48369.1, overlap = 59.5
PHY-3002 : Step(130): len = 48075.8, overlap = 60.25
PHY-3002 : Step(131): len = 48128.8, overlap = 56.75
PHY-3002 : Step(132): len = 48236.5, overlap = 53.5
PHY-3002 : Step(133): len = 47894, overlap = 53.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.85974e-05
PHY-3002 : Step(134): len = 48233.5, overlap = 52.5
PHY-3002 : Step(135): len = 48711.4, overlap = 49
PHY-3002 : Step(136): len = 49021.2, overlap = 48.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000117195
PHY-3002 : Step(137): len = 49376.7, overlap = 47
PHY-3002 : Step(138): len = 49642.7, overlap = 48.5
PHY-3002 : Step(139): len = 50280.2, overlap = 48
PHY-3002 : Step(140): len = 50450.5, overlap = 43.75
PHY-3001 : Before Legalized: Len = 50450.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.077611s wall, 0.078125s user + 0.093750s system = 0.171875s CPU (221.5%)

PHY-3001 : After Legalized: Len = 63393.4, Over = 0
PHY-3001 : Trial Legalized: Len = 63393.4
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1930 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.049793s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00126557
PHY-3002 : Step(141): len = 60650.9, overlap = 6.5
PHY-3002 : Step(142): len = 58880.7, overlap = 9.5
PHY-3002 : Step(143): len = 57798.9, overlap = 10.75
PHY-3002 : Step(144): len = 56191.1, overlap = 16.25
PHY-3002 : Step(145): len = 55520.7, overlap = 19.5
PHY-3002 : Step(146): len = 55337.1, overlap = 18.5
PHY-3002 : Step(147): len = 54986.9, overlap = 18.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00253114
PHY-3002 : Step(148): len = 55027.2, overlap = 18.75
PHY-3002 : Step(149): len = 54999.6, overlap = 19
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00506228
PHY-3002 : Step(150): len = 55110.3, overlap = 19.25
PHY-3002 : Step(151): len = 55114.3, overlap = 19.75
PHY-3001 : Before Legalized: Len = 55114.3
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005159s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 59323.7, Over = 0
PHY-3001 : Legalized: Len = 59323.7, Over = 0
PHY-3001 : Spreading special nets. 7 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005381s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 13 instances has been re-located, deltaX = 8, deltaY = 5, maxDist = 1.
PHY-3001 : Final: Len = 59329.7, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6306, tnet num: 1930, tinst num: 802, tnode num: 8586, tedge num: 11143.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 61/1932.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 65000, over cnt = 151(0%), over = 230, worst = 4
PHY-1002 : len = 65984, over cnt = 82(0%), over = 95, worst = 4
PHY-1002 : len = 66944, over cnt = 24(0%), over = 26, worst = 2
PHY-1002 : len = 67296, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.127159s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (172.0%)

PHY-1001 : Congestion index: top1 = 31.44, top5 = 21.93, top10 = 16.76, top15 = 13.09.
PHY-1001 : End incremental global routing;  0.177811s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (158.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1930 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058805s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (106.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.267791s wall, 0.359375s user + 0.015625s system = 0.375000s CPU (140.0%)

OPT-1001 : Current memory(MB): used = 214, reserve = 182, peak = 215.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1692/1932.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 67296, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006237s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.44, top5 = 21.93, top10 = 16.76, top15 = 13.09.
OPT-1001 : End congestion update;  0.055234s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (84.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1930 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056344s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (110.9%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 762 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 802 instances, 753 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 59454.4, Over = 0
PHY-3001 : End spreading;  0.004601s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 59454.4, Over = 0
PHY-3001 : End incremental legalization;  0.035467s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (88.1%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 2 cells processed and 200 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.163308s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (95.7%)

OPT-1001 : Current memory(MB): used = 218, reserve = 185, peak = 218.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1930 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058525s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (106.8%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1683/1932.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 67376, over cnt = 3(0%), over = 4, worst = 2
PHY-1002 : len = 67384, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 67400, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.027413s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (57.0%)

PHY-1001 : Congestion index: top1 = 31.44, top5 = 21.95, top10 = 16.78, top15 = 13.11.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1930 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048687s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (96.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.965517
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.880054s wall, 0.968750s user + 0.015625s system = 0.984375s CPU (111.9%)

RUN-1003 : finish command "place" in  5.058404s wall, 6.812500s user + 2.359375s system = 9.171875s CPU (181.3%)

RUN-1004 : used memory is 199 MB, reserved memory is 166 MB, peak memory is 218 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 804 instances
RUN-1001 : 376 mslices, 377 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1932 nets
RUN-1001 : 1365 nets have 2 pins
RUN-1001 : 462 nets have [3 - 5] pins
RUN-1001 : 63 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6306, tnet num: 1930, tinst num: 802, tnode num: 8586, tedge num: 11143.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 376 mslices, 377 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1930 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 64288, over cnt = 144(0%), over = 223, worst = 4
PHY-1002 : len = 65400, over cnt = 66(0%), over = 76, worst = 3
PHY-1002 : len = 66304, over cnt = 12(0%), over = 13, worst = 2
PHY-1002 : len = 66512, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.113766s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (151.1%)

PHY-1001 : Congestion index: top1 = 30.67, top5 = 21.67, top10 = 16.59, top15 = 12.96.
PHY-1001 : End global routing;  0.168535s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (129.8%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 234, reserve = 202, peak = 234.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 494, reserve = 465, peak = 494.
PHY-1001 : End build detailed router design. 3.150528s wall, 3.000000s user + 0.093750s system = 3.093750s CPU (98.2%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 31768, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.091799s wall, 1.062500s user + 0.015625s system = 1.078125s CPU (98.7%)

PHY-1001 : Current memory(MB): used = 526, reserve = 499, peak = 526.
PHY-1001 : End phase 1; 1.097570s wall, 1.062500s user + 0.015625s system = 1.078125s CPU (98.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 29% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 174168, over cnt = 34(0%), over = 34, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 528, reserve = 500, peak = 529.
PHY-1001 : End initial routed; 1.344942s wall, 2.328125s user + 0.156250s system = 2.484375s CPU (184.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1717(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.760   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -46.683  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.345761s wall, 0.343750s user + 0.015625s system = 0.359375s CPU (103.9%)

PHY-1001 : Current memory(MB): used = 529, reserve = 501, peak = 529.
PHY-1001 : End phase 2; 1.690796s wall, 2.671875s user + 0.171875s system = 2.843750s CPU (168.2%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 174168, over cnt = 34(0%), over = 34, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014977s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (104.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 174096, over cnt = 12(0%), over = 12, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.028530s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (164.3%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 174120, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.023530s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (132.8%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 174136, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.021431s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (72.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1717(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.760   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -46.683  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.333097s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (98.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 8 feed throughs used by 8 nets
PHY-1001 : End commit to database; 0.165610s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (94.3%)

PHY-1001 : Current memory(MB): used = 544, reserve = 515, peak = 544.
PHY-1001 : End phase 3; 0.716776s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (100.3%)

PHY-1003 : Routed, final wirelength = 174136
PHY-1001 : Current memory(MB): used = 544, reserve = 516, peak = 544.
PHY-1001 : End export database. 0.010116s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  6.847950s wall, 7.640625s user + 0.281250s system = 7.921875s CPU (115.7%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6306, tnet num: 1930, tinst num: 802, tnode num: 8586, tedge num: 11143.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[29] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dib[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[36] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[0] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[38] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[2] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[41] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[6] slack -86ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[8] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[3] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[6] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[52] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[55] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_66.dia[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_63.mi[0] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_63.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_66.mi[0] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_66.mi[1] slack -2679ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_69.mi[0] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_69.mi[1] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_72.mi[0] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_72.mi[1] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_75.mi[0] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_75.mi[1] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_78.mi[0] slack -2911ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_78.mi[1] slack -2897ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_81.mi[0] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_81.mi[1] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_83.mi[0] slack -2679ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/tx_data_dy_b[4]_syn_24.mi[0] slack -2679ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6428, tnet num: 1991, tinst num: 863, tnode num: 8708, tedge num: 11265.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/tx_data_dy_b[4]_syn_24_mi[0] slack -6ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_69_mi[0] slack -409ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_69_mi[1] slack -608ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_75_mi[1] slack -789ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_75_mi[0] slack -494ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_72_mi[1] slack -184ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_72_mi[0] slack -222ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_81_mi[1] slack -631ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_78_mi[0] slack -280ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_78_mi[1] slack -866ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_81_mi[0] slack -484ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_83_mi[0] slack -740ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_63_mi[0] slack -726ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_63_mi[1] slack -359ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_66_mi[0] slack -87ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_66_mi[1] slack -352ps
RUN-1001 : End hold fix;  3.089579s wall, 3.328125s user + 0.296875s system = 3.625000s CPU (117.3%)

RUN-1003 : finish command "route" in  10.440074s wall, 11.515625s user + 0.578125s system = 12.093750s CPU (115.8%)

RUN-1004 : used memory is 522 MB, reserved memory is 494 MB, peak memory is 544 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      890   out of  19600    4.54%
#reg                     1052   out of  19600    5.37%
#le                      1588
  #lut only               536   out of   1588   33.75%
  #reg only               698   out of   1588   43.95%
  #lut&reg                354   out of   1588   22.29%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                      Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0       477
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3       100
#3        wendu/clk_us                    GCLK               lslice             wendu/cnt_b[2]_syn_10.q0    38
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4       1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di             1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1588   |694     |196     |1085    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1084   |290     |133     |862     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |29     |24      |5       |21      |0       |0       |
|    demodu                  |Demodulation                                     |456    |104     |45      |352     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |56     |30      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |6       |0       |16      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |13     |7       |0       |13      |0       |0       |
|    integ                   |Integration                                      |140    |17      |15      |112     |0       |0       |
|    modu                    |Modulation                                       |98     |42      |14      |96      |0       |1       |
|    rs422                   |Rs422Output                                      |327    |77      |46      |260     |0       |4       |
|    trans                   |SquareWaveGenerator                              |34     |26      |8       |21      |0       |0       |
|  u_uart                    |UART_Control                                     |176    |132     |7       |118     |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |27     |19      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |112    |85      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |306    |261     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1387  
    #2          2       321   
    #3          3       117   
    #4          4        24   
    #5        5-10       67   
    #6        11-50      32   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6428, tnet num: 1991, tinst num: 863, tnode num: 8708, tedge num: 11265.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1991 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 863
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1993, pip num: 14372
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 11
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1296 valid insts, and 38691 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  4.455238s wall, 22.781250s user + 0.093750s system = 22.875000s CPU (513.4%)

RUN-1004 : used memory is 544 MB, reserved memory is 515 MB, peak memory is 670 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250805_102321.log"
