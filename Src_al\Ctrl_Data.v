`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////
// Company 			: I-NAV
// Engineer			: Andrew
// Create Date		: 15:33:33 30/04/2024 
// Design Name		: fog_4s
// Module Name		: Ctrl_Data
// Project Name		: fog_4s
// Target Devices	: All fiber optic gyroscopes 
// Tool versions	: TD5.6.2-64bit
// Description		: 串口数据处理逻辑
// Revision			: V1.01
// Remarks			: /* synthesis keep */
//////////////////////////////////////////////////////////////////
module Ctrl_Data
#(
	parameter iWID_RS422=32  //RS422 DATA width
)
(
    input 					clk,		//80Mhz
    input 					rst_n,    
    input 					rd_done,    //温度数据读完
    input 					transmit, 	//rs422数据已经准备好
	input					tx_done,	//串口8bit数据发送完成
	//input  [7:0]			RxTr_cnt,   //帧数据
	input [iWID_RS422-1:0] data_Packet,//数据来自FMC总线，需要发数据
	input [15:0] 			temp_data,  //转换后得到的温度数据   
	output reg 				tx_wr,		//发送串口数据状态位
	output reg 	[7:0] 		tx_data  	//转化8bit串口发送的数据
);

reg     	tx_wrdy;
reg [7:0] 	tx_data_dy;
reg [7:0] 	crc_data_speed;     // 角速度数据校验
reg [7:0] 	crc_data_temp;      // 温度数据校验
//reg [7:0] 	RxTr_cntdy;
reg [7:0] 	transmitdy;
reg [15:0] 	temp_datady;
reg [31:0] 	data_Packetdy;
reg [3:0] 	tx_state;
localparam	tx_idle 	= 4'h0 ;
localparam	tx_Header 	= 4'h1 ;
localparam	tx_First 	= 4'h2 ;
localparam	tx_Second 	= 4'h3 ;
localparam	tx_Third 	= 4'h4 ;
localparam	tx_Fourth 	= 4'h5 ;
localparam	tx_Fifth 	= 4'h6 ;
localparam	tx_Sixth 	= 4'h7 ;
localparam	tx_Seventh 	= 4'h8 ;
localparam	tx_Eighth 	= 4'h9 ;
localparam	tx_Ninth 	= 4'hA ;
localparam	tx_Tenth 	= 4'hB ;
localparam	tx_end 		= 4'hC ;

//transmit:获取transmit上升沿时序逻辑
always @(posedge clk)begin
	transmitdy <= {transmitdy[6:0],transmit};
end

////统一锁存RxTr_cnt
//always @(posedge clk)begin
//	RxTr_cntdy <= (transmitdy[1:0]==2'b01)?RxTr_cnt : RxTr_cntdy;
//end

//统一锁存temp_data
always @(posedge clk)begin
	temp_datady <= (transmitdy[1:0]==2'b01)?temp_data : temp_datady;
end

//统一锁存data_Packet
always @(posedge clk)begin
	data_Packetdy <= (transmitdy[1:0]==2'b01)?data_Packet : data_Packetdy;
end

//统一锁存角速度数据校验 - 第7字节为校验字节，是数据包内第2字节至第6字节数据的字节XOR异或值
always @(posedge clk)begin
	if(transmitdy[7:6]==2'b01)
		crc_data_speed <= { data_Packetdy[31]^data_Packetdy[23]^data_Packetdy[15]^data_Packetdy[7],
						    data_Packetdy[30]^data_Packetdy[22]^data_Packetdy[14]^data_Packetdy[6],
						    data_Packetdy[29]^data_Packetdy[21]^data_Packetdy[13]^data_Packetdy[5],
						    data_Packetdy[28]^data_Packetdy[20]^data_Packetdy[12]^data_Packetdy[4],
						    data_Packetdy[27]^data_Packetdy[19]^data_Packetdy[11]^data_Packetdy[3],
						    data_Packetdy[26]^data_Packetdy[18]^data_Packetdy[10]^data_Packetdy[2],
						    data_Packetdy[25]^data_Packetdy[17]^data_Packetdy[9]^data_Packetdy[1],
						    data_Packetdy[24]^data_Packetdy[16]^data_Packetdy[8]^data_Packetdy[0]
						    };
	else
		crc_data_speed <= crc_data_speed;
end

//统一锁存温度数据校验 - 第10字节为校验字节，是数据包内第2字节至第9字节数据的XOR值
always @(posedge clk)begin
	if(transmitdy[7:6]==2'b01)
		crc_data_temp <= { data_Packetdy[31]^data_Packetdy[23]^data_Packetdy[15]^data_Packetdy[7]^temp_datady[15]^temp_datady[7]^crc_data_speed[7],
						   data_Packetdy[30]^data_Packetdy[22]^data_Packetdy[14]^data_Packetdy[6]^temp_datady[14]^temp_datady[6]^crc_data_speed[6],
						   data_Packetdy[29]^data_Packetdy[21]^data_Packetdy[13]^data_Packetdy[5]^temp_datady[13]^temp_datady[5]^crc_data_speed[5],
						   data_Packetdy[28]^data_Packetdy[20]^data_Packetdy[12]^data_Packetdy[4]^temp_datady[12]^temp_datady[4]^crc_data_speed[4],
						   data_Packetdy[27]^data_Packetdy[19]^data_Packetdy[11]^data_Packetdy[3]^temp_datady[11]^temp_datady[3]^crc_data_speed[3],
						   data_Packetdy[26]^data_Packetdy[18]^data_Packetdy[10]^data_Packetdy[2]^temp_datady[10]^temp_datady[2]^crc_data_speed[2],
						   data_Packetdy[25]^data_Packetdy[17]^data_Packetdy[9]^data_Packetdy[1]^temp_datady[9]^temp_datady[1]^crc_data_speed[1],
						   data_Packetdy[24]^data_Packetdy[16]^data_Packetdy[8]^data_Packetdy[0]^temp_datady[8]^temp_datady[0]^crc_data_speed[0]
						   };
	else
		crc_data_temp <= crc_data_temp;
end


always @(posedge clk or negedge rst_n)begin
    if(rst_n == 1'b0)begin
        tx_wrdy 		<= 1'b0;
        tx_state 	<= tx_idle;
		tx_data_dy 	<= 8'h00;
    end
	else begin
		case(tx_state)
			tx_idle:begin
						tx_wrdy <= 1'b0;
						if(transmitdy[3:2] == 2'b01)//rs422数据准备好
							tx_state <= tx_Header;
						else
							tx_state <= tx_idle;
					end
			tx_Header:begin
							tx_wrdy 		<= 1'b1;
							tx_data_dy 	<= 8'h80;  // 新协议帧头为80
							tx_state 	<= tx_First;
					end
			tx_First:begin  // 第2字节：32bit角速度数据的最高字节
						if(tx_done == 1'b1)begin
							tx_wrdy 		<= 1'b1;
							tx_data_dy 	<= data_Packetdy[31:24];
							tx_state 	<= tx_Second;
						end
						else begin
							tx_wrdy 		<= 1'b0;
							tx_data_dy 	<= tx_data_dy;
							tx_state 	<= tx_First;
						end
					end
			tx_Second:begin  // 第3字节：32bit角速度数据
						if(tx_done == 1'b1)begin
							tx_wrdy 		<= 1'b1;
							tx_data_dy 	<= data_Packetdy[23:16];
							tx_state 	<= tx_Third;
						end
						else begin
							tx_wrdy 		<= 1'b0;
							tx_data_dy 	<= tx_data_dy;
							tx_state 	<= tx_Second;
						end
					end
			tx_Third:begin  // 第4字节：32bit角速度数据
						if(tx_done == 1'b1)begin
							tx_wrdy 		<= 1'b1;
							tx_data_dy 	<= data_Packetdy[15:8];
							tx_state 	<= tx_Fourth;
						end
						else begin
							tx_wrdy 		<= 1'b0;
							tx_data_dy 	<= tx_data_dy;
							tx_state 	<= tx_Third;
						end
					end
			tx_Fourth:begin  // 第5字节：32bit角速度数据的最低字节
						if(tx_done == 1'b1)begin
							tx_wrdy 		<= 1'b1;
							tx_data_dy 	<= data_Packetdy[7:0];
							tx_state 	<= tx_Fifth;
						end
						else begin
							tx_wrdy 		<= 1'b0;
							tx_data_dy 	<= tx_data_dy;
							tx_state 	<= tx_Fourth;
						end
					end
 			tx_Fifth:begin  // 第6字节：保留字节，设为0
						if(tx_done == 1'b1)begin
							tx_wrdy 		<= 1'b1;
							tx_data_dy 	<= 8'h00;  // 保留字节
							tx_state 	<= tx_Sixth;
						end
						else begin
							tx_wrdy 		<= 1'b0;
							tx_data_dy 	<= tx_data_dy;
							tx_state 	<= tx_Fifth;
						end
					end
			tx_Sixth:begin  // 第7字节：角速度数据校验XOR
						if(tx_done == 1'b1)begin
							tx_wrdy 		<= 1'b1;
							tx_data_dy 	<= crc_data_speed;
							tx_state 	<= tx_Seventh;
						end
						else begin
							tx_wrdy 		<= 1'b0;
							tx_data_dy 	<= tx_data_dy;
							tx_state 	<= tx_Sixth;
						end
					end
			tx_Seventh:begin  // 第8字节：16bit温度数据的高字节
						if(tx_done == 1'b1)begin
							tx_wrdy <= 1'b1;
							tx_data_dy <= temp_datady[15:8];
							tx_state <= tx_Eighth;
						end
						else begin
							tx_wrdy 		<= 1'b0;
							tx_data_dy 	<= tx_data_dy;
							tx_state 	<= tx_Seventh;
						end
					end
			tx_Eighth:begin  // 第9字节：16bit温度数据的低字节
						if(tx_done == 1'b1)begin
							tx_wrdy <= 1'b1;
							tx_data_dy <= temp_datady[7:0];
							tx_state <= tx_Ninth;
						end
						else begin
							tx_wrdy 		<= 1'b0;
							tx_data_dy 	<= tx_data_dy;
							tx_state 	<= tx_Eighth;
						end
					end
			tx_Ninth:begin  // 第10字节：温度数据校验XOR
						if(tx_done == 1'b1)begin
							tx_wrdy <= 1'b1;
							tx_data_dy <= crc_data_temp;
							tx_state <= tx_end;
						end
						else begin
							tx_wrdy 		<= 1'b0;
							tx_data_dy 	<= tx_data_dy;
							tx_state 	<= tx_Ninth;
						end
					end
			tx_end:begin
						tx_wrdy <= 1'b0;
						if(tx_done == 1'b1)begin
							tx_state <= tx_idle;	
						end
						else begin
							tx_state <= tx_end;												
						end
					end
			default: tx_state <= tx_idle;
		endcase
	end 
end

//tx_data_dy:Delay one beat to make it easy to latch the data
always@(posedge clk)begin
	tx_data <= tx_data_dy;
	tx_wr	<= tx_wrdy;
end

endmodule

