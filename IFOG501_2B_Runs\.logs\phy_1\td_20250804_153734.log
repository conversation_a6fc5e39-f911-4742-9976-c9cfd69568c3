============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Mon Aug  4 15:37:34 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1582 instances
RUN-0007 : 378 luts, 954 seqs, 129 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2126 nets
RUN-1001 : 1544 nets have 2 pins
RUN-1001 : 480 nets have [3 - 5] pins
RUN-1001 : 57 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     251     
RUN-1001 :   No   |  No   |  Yes  |     124     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1580 instances, 378 luts, 954 seqs, 199 slices, 20 macros(199 instances: 129 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7567, tnet num: 2124, tinst num: 1580, tnode num: 10729, tedge num: 12850.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2124 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.262856s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (101.1%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 535100
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1580.
PHY-3001 : End clustering;  0.000020s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 459383, overlap = 20.25
PHY-3002 : Step(2): len = 431449, overlap = 15.75
PHY-3002 : Step(3): len = 416314, overlap = 20.25
PHY-3002 : Step(4): len = 403245, overlap = 13.5
PHY-3002 : Step(5): len = 389203, overlap = 18
PHY-3002 : Step(6): len = 370649, overlap = 13.5
PHY-3002 : Step(7): len = 363708, overlap = 15.75
PHY-3002 : Step(8): len = 356680, overlap = 15.75
PHY-3002 : Step(9): len = 338983, overlap = 13.5
PHY-3002 : Step(10): len = 333543, overlap = 11.25
PHY-3002 : Step(11): len = 326633, overlap = 13.5
PHY-3002 : Step(12): len = 316662, overlap = 11.25
PHY-3002 : Step(13): len = 308809, overlap = 15.75
PHY-3002 : Step(14): len = 305200, overlap = 18
PHY-3002 : Step(15): len = 291860, overlap = 15.75
PHY-3002 : Step(16): len = 284487, overlap = 15.75
PHY-3002 : Step(17): len = 279729, overlap = 15.75
PHY-3002 : Step(18): len = 273958, overlap = 15.75
PHY-3002 : Step(19): len = 261827, overlap = 15.75
PHY-3002 : Step(20): len = 258379, overlap = 15.75
PHY-3002 : Step(21): len = 253403, overlap = 20.25
PHY-3002 : Step(22): len = 241513, overlap = 20.25
PHY-3002 : Step(23): len = 231292, overlap = 20.25
PHY-3002 : Step(24): len = 229350, overlap = 20.25
PHY-3002 : Step(25): len = 218184, overlap = 20.25
PHY-3002 : Step(26): len = 200867, overlap = 20.25
PHY-3002 : Step(27): len = 196037, overlap = 20.25
PHY-3002 : Step(28): len = 193580, overlap = 20.25
PHY-3002 : Step(29): len = 137596, overlap = 18
PHY-3002 : Step(30): len = 134955, overlap = 20.25
PHY-3002 : Step(31): len = 132742, overlap = 20.25
PHY-3002 : Step(32): len = 129575, overlap = 20.25
PHY-3002 : Step(33): len = 127263, overlap = 20.25
PHY-3002 : Step(34): len = 125265, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.90846e-05
PHY-3002 : Step(35): len = 125520, overlap = 15.75
PHY-3002 : Step(36): len = 123821, overlap = 18
PHY-3002 : Step(37): len = 122683, overlap = 18
PHY-3002 : Step(38): len = 120626, overlap = 13.5
PHY-3002 : Step(39): len = 114617, overlap = 13.5
PHY-3002 : Step(40): len = 112222, overlap = 11.25
PHY-3002 : Step(41): len = 110797, overlap = 11.25
PHY-3002 : Step(42): len = 108297, overlap = 11.25
PHY-3002 : Step(43): len = 104913, overlap = 15.75
PHY-3002 : Step(44): len = 102584, overlap = 15.75
PHY-3002 : Step(45): len = 101421, overlap = 13.5
PHY-3002 : Step(46): len = 99029.2, overlap = 11.25
PHY-3002 : Step(47): len = 96159.6, overlap = 13.5
PHY-3002 : Step(48): len = 95704.2, overlap = 11.25
PHY-3002 : Step(49): len = 92944.2, overlap = 13.5
PHY-3002 : Step(50): len = 90724.3, overlap = 13.5
PHY-3002 : Step(51): len = 90081.6, overlap = 15.75
PHY-3002 : Step(52): len = 88838.5, overlap = 15.75
PHY-3002 : Step(53): len = 87456.1, overlap = 18
PHY-3002 : Step(54): len = 86554.9, overlap = 18
PHY-3002 : Step(55): len = 84616.7, overlap = 13.5
PHY-3002 : Step(56): len = 83127.1, overlap = 13.5
PHY-3002 : Step(57): len = 81987.4, overlap = 15.75
PHY-3002 : Step(58): len = 81217.9, overlap = 15.75
PHY-3002 : Step(59): len = 78865.2, overlap = 15.75
PHY-3002 : Step(60): len = 77615.3, overlap = 15.75
PHY-3002 : Step(61): len = 75680.9, overlap = 18
PHY-3002 : Step(62): len = 74421.6, overlap = 18
PHY-3002 : Step(63): len = 72476.1, overlap = 15.75
PHY-3002 : Step(64): len = 70669.6, overlap = 15
PHY-3002 : Step(65): len = 69117, overlap = 15.4375
PHY-3002 : Step(66): len = 68599.5, overlap = 15.75
PHY-3002 : Step(67): len = 67285.3, overlap = 15.8125
PHY-3002 : Step(68): len = 66508.7, overlap = 18.1875
PHY-3002 : Step(69): len = 63854.7, overlap = 11.8125
PHY-3002 : Step(70): len = 62458.2, overlap = 16.1875
PHY-3002 : Step(71): len = 62154.6, overlap = 16.3125
PHY-3002 : Step(72): len = 62025.5, overlap = 16.3125
PHY-3002 : Step(73): len = 62012, overlap = 13.9375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000178169
PHY-3002 : Step(74): len = 62074.2, overlap = 16.1875
PHY-3002 : Step(75): len = 62000.8, overlap = 16.1875
PHY-3002 : Step(76): len = 61839, overlap = 16.1875
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000356339
PHY-3002 : Step(77): len = 62069.3, overlap = 16.1875
PHY-3002 : Step(78): len = 62108.3, overlap = 16.1875
PHY-3001 : Before Legalized: Len = 62108.3
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006891s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 65661, Over = 2.6875
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2124 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.059717s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (78.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(79): len = 65498.4, overlap = 7.625
PHY-3002 : Step(80): len = 64283.7, overlap = 5.75
PHY-3002 : Step(81): len = 63374.3, overlap = 6.5
PHY-3002 : Step(82): len = 62674, overlap = 6.8125
PHY-3002 : Step(83): len = 61906.1, overlap = 6.9375
PHY-3002 : Step(84): len = 60907, overlap = 7.25
PHY-3002 : Step(85): len = 59733.4, overlap = 12.5625
PHY-3002 : Step(86): len = 58194, overlap = 16.5938
PHY-3002 : Step(87): len = 57686.3, overlap = 17.25
PHY-3002 : Step(88): len = 56810.3, overlap = 17.375
PHY-3002 : Step(89): len = 56397.9, overlap = 17.3125
PHY-3002 : Step(90): len = 55615.7, overlap = 18.0625
PHY-3002 : Step(91): len = 55083.5, overlap = 16.2188
PHY-3002 : Step(92): len = 54947.4, overlap = 15.875
PHY-3002 : Step(93): len = 54278.4, overlap = 15.375
PHY-3002 : Step(94): len = 53497.8, overlap = 15.2188
PHY-3002 : Step(95): len = 52801.6, overlap = 16.7812
PHY-3002 : Step(96): len = 51622, overlap = 20.3438
PHY-3002 : Step(97): len = 51274, overlap = 20.8125
PHY-3002 : Step(98): len = 50571.7, overlap = 22.1562
PHY-3002 : Step(99): len = 50100, overlap = 24.0938
PHY-3002 : Step(100): len = 49623.1, overlap = 23.7188
PHY-3002 : Step(101): len = 49324.7, overlap = 23.7188
PHY-3002 : Step(102): len = 48955, overlap = 23.8438
PHY-3002 : Step(103): len = 48881, overlap = 23.5625
PHY-3002 : Step(104): len = 48875.7, overlap = 24
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000183025
PHY-3002 : Step(105): len = 48672.7, overlap = 23.7188
PHY-3002 : Step(106): len = 48571.3, overlap = 23.2812
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00036605
PHY-3002 : Step(107): len = 48533, overlap = 23.0312
PHY-3002 : Step(108): len = 48513.8, overlap = 24.4375
PHY-3002 : Step(109): len = 48548.7, overlap = 25.0625
PHY-3002 : Step(110): len = 48411.4, overlap = 25.125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2124 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.068139s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (91.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.2558e-05
PHY-3002 : Step(111): len = 48943.5, overlap = 49.625
PHY-3002 : Step(112): len = 49403, overlap = 52.5
PHY-3002 : Step(113): len = 49962.8, overlap = 43.375
PHY-3002 : Step(114): len = 50085.6, overlap = 44.6562
PHY-3002 : Step(115): len = 50285, overlap = 43.6875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000125116
PHY-3002 : Step(116): len = 50255.2, overlap = 43.4375
PHY-3002 : Step(117): len = 50777.5, overlap = 40.5625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000250232
PHY-3002 : Step(118): len = 50733, overlap = 40.4375
PHY-3002 : Step(119): len = 51861.6, overlap = 38.3438
PHY-3002 : Step(120): len = 52980.9, overlap = 33.9688
PHY-3002 : Step(121): len = 52913.6, overlap = 33.1875
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7567, tnet num: 2124, tinst num: 1580, tnode num: 10729, tedge num: 12850.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 84.88 peak overflow 2.81
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2126.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55416, over cnt = 232(0%), over = 1020, worst = 16
PHY-1001 : End global iterations;  0.070508s wall, 0.093750s user + 0.015625s system = 0.109375s CPU (155.1%)

PHY-1001 : Congestion index: top1 = 46.23, top5 = 25.97, top10 = 16.15, top15 = 11.40.
PHY-1001 : End incremental global routing;  0.121112s wall, 0.125000s user + 0.031250s system = 0.156250s CPU (129.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2124 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.069812s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (111.9%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.220702s wall, 0.234375s user + 0.031250s system = 0.265625s CPU (120.4%)

OPT-1001 : Current memory(MB): used = 208, reserve = 175, peak = 208.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1598/2126.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55416, over cnt = 232(0%), over = 1020, worst = 16
PHY-1002 : len = 63304, over cnt = 162(0%), over = 403, worst = 15
PHY-1002 : len = 67368, over cnt = 53(0%), over = 122, worst = 15
PHY-1002 : len = 68552, over cnt = 15(0%), over = 15, worst = 1
PHY-1002 : len = 69496, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.090690s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (155.1%)

PHY-1001 : Congestion index: top1 = 39.18, top5 = 25.74, top10 = 18.19, top15 = 13.34.
OPT-1001 : End congestion update;  0.135008s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (127.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2124 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057427s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.8%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.196082s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (119.5%)

OPT-1001 : Current memory(MB): used = 212, reserve = 179, peak = 212.
OPT-1001 : End physical optimization;  0.668748s wall, 0.671875s user + 0.062500s system = 0.734375s CPU (109.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 378 LUT to BLE ...
SYN-4008 : Packed 378 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 765 remaining SEQ's ...
SYN-4005 : Packed 96 SEQ with LUT/SLICE
SYN-4006 : 108 single LUT's are left
SYN-4006 : 669 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1047/1361 primitive instances ...
PHY-3001 : End packing;  0.050116s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (124.7%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 802 instances
RUN-1001 : 375 mslices, 376 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1945 nets
RUN-1001 : 1363 nets have 2 pins
RUN-1001 : 476 nets have [3 - 5] pins
RUN-1001 : 61 nets have [6 - 10] pins
RUN-1001 : 24 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 800 instances, 751 slices, 20 macros(199 instances: 129 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 52964.4, Over = 62.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6362, tnet num: 1943, tinst num: 800, tnode num: 8657, tedge num: 11259.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1943 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.288935s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (97.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.92182e-05
PHY-3002 : Step(122): len = 52394.6, overlap = 65.75
PHY-3002 : Step(123): len = 52109.5, overlap = 68.75
PHY-3002 : Step(124): len = 51680, overlap = 73
PHY-3002 : Step(125): len = 51744, overlap = 73
PHY-3002 : Step(126): len = 51447.2, overlap = 74.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.84363e-05
PHY-3002 : Step(127): len = 51997, overlap = 72
PHY-3002 : Step(128): len = 52609.7, overlap = 68.25
PHY-3002 : Step(129): len = 53356, overlap = 60.75
PHY-3002 : Step(130): len = 53582, overlap = 56.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000116873
PHY-3002 : Step(131): len = 54366.5, overlap = 54.25
PHY-3002 : Step(132): len = 55204.2, overlap = 51.5
PHY-3001 : Before Legalized: Len = 55204.2
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.075201s wall, 0.062500s user + 0.093750s system = 0.156250s CPU (207.8%)

PHY-3001 : After Legalized: Len = 69353.6, Over = 0
PHY-3001 : Trial Legalized: Len = 69353.6
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1943 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.051479s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00101677
PHY-3002 : Step(133): len = 65409.6, overlap = 6.75
PHY-3002 : Step(134): len = 63960, overlap = 9
PHY-3002 : Step(135): len = 61656, overlap = 14.75
PHY-3002 : Step(136): len = 60708.5, overlap = 16.5
PHY-3002 : Step(137): len = 59933.5, overlap = 19.25
PHY-3002 : Step(138): len = 59532.5, overlap = 20.25
PHY-3002 : Step(139): len = 59304.3, overlap = 20.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00203354
PHY-3002 : Step(140): len = 59594.4, overlap = 21
PHY-3002 : Step(141): len = 59692.5, overlap = 20.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00406708
PHY-3002 : Step(142): len = 59930, overlap = 19.75
PHY-3002 : Step(143): len = 59930, overlap = 19.75
PHY-3001 : Before Legalized: Len = 59930
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005255s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (297.3%)

PHY-3001 : After Legalized: Len = 64164.9, Over = 0
PHY-3001 : Legalized: Len = 64164.9, Over = 0
PHY-3001 : Spreading special nets. 6 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005128s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 10 instances has been re-located, deltaX = 6, deltaY = 4, maxDist = 1.
PHY-3001 : Final: Len = 64330.9, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6362, tnet num: 1943, tinst num: 800, tnode num: 8657, tedge num: 11259.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 80/1945.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71632, over cnt = 145(0%), over = 221, worst = 7
PHY-1002 : len = 72640, over cnt = 75(0%), over = 91, worst = 3
PHY-1002 : len = 73592, over cnt = 15(0%), over = 19, worst = 2
PHY-1002 : len = 73872, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 73936, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.132221s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (177.3%)

PHY-1001 : Congestion index: top1 = 32.95, top5 = 23.46, top10 = 18.14, top15 = 14.12.
PHY-1001 : End incremental global routing;  0.189926s wall, 0.234375s user + 0.046875s system = 0.281250s CPU (148.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1943 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061434s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.282173s wall, 0.328125s user + 0.046875s system = 0.375000s CPU (132.9%)

OPT-1001 : Current memory(MB): used = 213, reserve = 180, peak = 214.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1717/1945.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73936, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006720s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (232.5%)

PHY-1001 : Congestion index: top1 = 32.95, top5 = 23.46, top10 = 18.14, top15 = 14.12.
OPT-1001 : End congestion update;  0.056613s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (110.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1943 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051510s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.0%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 760 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 800 instances, 751 slices, 20 macros(199 instances: 129 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 64446.4, Over = 0
PHY-3001 : End spreading;  0.005318s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (293.8%)

PHY-3001 : Final: Len = 64446.4, Over = 0
PHY-3001 : End incremental legalization;  0.036241s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (129.3%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.159577s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (97.9%)

OPT-1001 : Current memory(MB): used = 218, reserve = 185, peak = 218.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1943 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050681s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (123.3%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1708/1945.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73984, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 73984, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.017653s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (88.5%)

PHY-1001 : Congestion index: top1 = 32.97, top5 = 23.44, top10 = 18.11, top15 = 14.10.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1943 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049884s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 32.482759
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.891950s wall, 0.937500s user + 0.046875s system = 0.984375s CPU (110.4%)

RUN-1003 : finish command "place" in  4.992963s wall, 6.390625s user + 2.718750s system = 9.109375s CPU (182.4%)

RUN-1004 : used memory is 197 MB, reserved memory is 164 MB, peak memory is 218 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 802 instances
RUN-1001 : 375 mslices, 376 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1945 nets
RUN-1001 : 1363 nets have 2 pins
RUN-1001 : 476 nets have [3 - 5] pins
RUN-1001 : 61 nets have [6 - 10] pins
RUN-1001 : 24 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6362, tnet num: 1943, tinst num: 800, tnode num: 8657, tedge num: 11259.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 375 mslices, 376 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1943 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70120, over cnt = 153(0%), over = 232, worst = 7
PHY-1002 : len = 70992, over cnt = 91(0%), over = 125, worst = 7
PHY-1002 : len = 72592, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 72624, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.134811s wall, 0.156250s user + 0.031250s system = 0.187500s CPU (139.1%)

PHY-1001 : Congestion index: top1 = 32.16, top5 = 22.98, top10 = 17.81, top15 = 13.85.
PHY-1001 : End global routing;  0.185627s wall, 0.218750s user + 0.031250s system = 0.250000s CPU (134.7%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 237, reserve = 205, peak = 237.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 495, reserve = 467, peak = 495.
PHY-1001 : End build detailed router design. 3.235556s wall, 3.171875s user + 0.062500s system = 3.234375s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 30664, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.111337s wall, 1.093750s user + 0.015625s system = 1.109375s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 527, reserve = 501, peak = 527.
PHY-1001 : End phase 1; 1.117690s wall, 1.109375s user + 0.015625s system = 1.125000s CPU (100.7%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 30% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 180728, over cnt = 35(0%), over = 35, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 530, reserve = 501, peak = 531.
PHY-1001 : End initial routed; 1.588810s wall, 2.390625s user + 0.093750s system = 2.484375s CPU (156.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1727(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.959   |  -46.093  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.349067s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (98.5%)

PHY-1001 : Current memory(MB): used = 532, reserve = 503, peak = 532.
PHY-1001 : End phase 2; 1.937996s wall, 2.734375s user + 0.093750s system = 2.828125s CPU (145.9%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 180728, over cnt = 35(0%), over = 35, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015796s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (98.9%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 180712, over cnt = 10(0%), over = 10, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.031588s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (148.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 180776, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.024044s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (130.0%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 180808, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.019652s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (79.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1727(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.959   |  -46.187  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.342174s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (95.9%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 5 feed throughs used by 5 nets
PHY-1001 : End commit to database; 0.187160s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (100.2%)

PHY-1001 : Current memory(MB): used = 545, reserve = 516, peak = 545.
PHY-1001 : End phase 3; 0.757486s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (99.0%)

PHY-1003 : Routed, final wirelength = 180808
PHY-1001 : Current memory(MB): used = 545, reserve = 517, peak = 545.
PHY-1001 : End export database. 0.011753s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (132.9%)

PHY-1001 : End detail routing;  7.239257s wall, 7.953125s user + 0.171875s system = 8.125000s CPU (112.2%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6362, tnet num: 1943, tinst num: 800, tnode num: 8657, tedge num: 11259.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[31] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dib[4] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[35] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dib[8] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/asy_w_rst1 endpin signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_15.sr slack -65ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/asy_w_rst1 endpin signal_process/demodu/fifo/rd_to_wr_cross_inst/reg2_syn_24.sr slack -65ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg1_syn_184.mi[0] slack -2959ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_70.mi[0] slack -2801ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_70.mi[1] slack -2947ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_73.mi[0] slack -2719ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_73.mi[1] slack -2959ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_76.mi[1] slack -2588ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_79.mi[0] slack -2947ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_79.mi[1] slack -2813ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_83.mi[0] slack -2959ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_83.mi[1] slack -2813ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_86.mi[0] slack -2959ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_86.mi[1] slack -2827ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_89.mi[0] slack -2959ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_89.mi[1] slack -2959ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_91.mi[0] slack -2947ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/tx_data_dy_b[4]_syn_23.mi[0] slack -2827ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6470, tnet num: 1997, tinst num: 854, tnode num: 8765, tedge num: 11367.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_89_mi[0] slack -465ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_89_mi[1] slack -272ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_91_mi[0] slack -784ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_86_mi[1] slack -589ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_86_mi[0] slack -480ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_83_mi[1] slack -632ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_83_mi[0] slack -621ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_73_mi[1] slack -548ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/tx_data_dy_b[4]_syn_23_mi[0] slack -635ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_70_mi[0] slack -494ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg1_syn_184_mi[0] slack -216ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg2_syn_76_mi[1] slack -749ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_79_mi[1] slack -286ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_79_mi[0] slack -483ps
RUN-1001 : End hold fix;  3.102223s wall, 3.281250s user + 0.156250s system = 3.437500s CPU (110.8%)

RUN-1003 : finish command "route" in  10.874480s wall, 11.812500s user + 0.359375s system = 12.171875s CPU (111.9%)

RUN-1004 : used memory is 501 MB, reserved memory is 475 MB, peak memory is 545 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      892   out of  19600    4.55%
#reg                     1053   out of  19600    5.37%
#le                      1561
  #lut only               508   out of   1561   32.54%
  #reg only               669   out of   1561   42.86%
  #lut&reg                384   out of   1561   24.60%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         478
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         100
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q0    42
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1561   |693     |199     |1086    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1051   |281     |136     |863     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |21     |16      |5       |19      |0       |0       |
|    demodu                  |Demodulation                                     |437    |85      |41      |353     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |59     |32      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |5       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |14     |7       |0       |14      |0       |0       |
|    integ                   |Integration                                      |139    |37      |15      |111     |0       |0       |
|    modu                    |Modulation                                       |105    |50      |21      |101     |0       |1       |
|    rs422                   |Rs422Output                                      |314    |66      |46      |259     |0       |4       |
|    trans                   |SquareWaveGenerator                              |35     |27      |8       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |175    |133     |7       |118     |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |26     |19      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |112    |86      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |313    |268     |45      |72      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1378  
    #2          2       341   
    #3          3       107   
    #4          4        28   
    #5        5-10       68   
    #6        11-50      32   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6470, tnet num: 1997, tinst num: 854, tnode num: 8765, tedge num: 11367.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1997 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 854
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1999, pip num: 14803
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 9
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1351 valid insts, and 39525 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.265021s wall, 18.906250s user + 0.093750s system = 19.000000s CPU (581.9%)

RUN-1004 : used memory is 521 MB, reserved memory is 492 MB, peak memory is 668 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250804_153734.log"
