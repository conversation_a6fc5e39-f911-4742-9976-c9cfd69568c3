============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Mon Aug 11 14:43:58 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "config_chipwatcher ../../sim/test.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 1 view nodes, 16 trigger nets, 16 data nets.
KIT-1004 : Chipwatcher code = 0010010100101001
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir C:/Anlogic/TD_5.6.5_SP3_151.449/cw/ -file IFOG501_2B_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file C:/Anlogic/TD_5.6.5_SP3_151.449/cw\bus_det.v
HDL-1007 : analyze verilog file C:/Anlogic/TD_5.6.5_SP3_151.449/cw\bus_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD_5.6.5_SP3_151.449/cw\cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD_5.6.5_SP3_151.449/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD_5.6.5_SP3_151.449/cw\cwc_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD_5.6.5_SP3_151.449/cw\detect_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD_5.6.5_SP3_151.449/cw\detect_non_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD_5.6.5_SP3_151.449/cw\emb_ctrl.v
HDL-1007 : analyze verilog file C:/Anlogic/TD_5.6.5_SP3_151.449/cw\register.v
HDL-1007 : analyze verilog file C:/Anlogic/TD_5.6.5_SP3_151.449/cw\tap.v
HDL-1007 : analyze verilog file C:/Anlogic/TD_5.6.5_SP3_151.449/cw\trigger.sv
HDL-1007 : analyze verilog file IFOG501_2B_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in IFOG501_2B_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=1,BUS_DIN_NUM=16,BUS_CTRL_NUM=36,BUS_WIDTH='{32'sb010000},BUS_DIN_POS='{32'sb0},BUS_CTRL_POS='{32'sb0}) in C:/Anlogic/TD_5.6.5_SP3_151.449/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=58) in C:/Anlogic/TD_5.6.5_SP3_151.449/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=58) in C:/Anlogic/TD_5.6.5_SP3_151.449/cw\register.v(21)
HDL-1007 : elaborate module tap in C:/Anlogic/TD_5.6.5_SP3_151.449/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=1,BUS_DIN_NUM=16,BUS_CTRL_NUM=36,BUS_WIDTH='{32'sb010000},BUS_DIN_POS='{32'sb0},BUS_CTRL_POS='{32'sb0}) in C:/Anlogic/TD_5.6.5_SP3_151.449/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=1,BUS_DIN_NUM=16,BUS_CTRL_NUM=36,BUS_WIDTH='{32'sb010000},BUS_DIN_POS='{32'sb0},BUS_CTRL_POS='{32'sb0}) in C:/Anlogic/TD_5.6.5_SP3_151.449/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in C:/Anlogic/TD_5.6.5_SP3_151.449/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in C:/Anlogic/TD_5.6.5_SP3_151.449/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=1,BUS_DIN_NUM=16,BUS_CTRL_NUM=36,BUS_WIDTH='{32'sb010000},BUS_DIN_POS='{32'sb0},BUS_CTRL_POS='{32'sb0})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=58)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=58)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=1,BUS_DIN_NUM=16,BUS_CTRL_NUM=36,BUS_WIDTH='{32'sb010000},BUS_DIN_POS='{32'sb0},BUS_CTRL_POS='{32'sb0})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=1,BUS_DIN_NUM=16,BUS_CTRL_NUM=36,BUS_WIDTH='{32'sb010000},BUS_DIN_POS='{32'sb0},BUS_CTRL_POS='{32'sb0})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 3034/8 useful/useless nets, 1919/2 useful/useless insts
SYN-1016 : Merged 12 instances.
SYN-1032 : 2867/2 useful/useless nets, 1752/2 useful/useless insts
SYN-1032 : 2847/20 useful/useless nets, 2192/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 1 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 1 mux instances.
SYN-1015 : Optimize round 1, 225 better
SYN-1014 : Optimize round 2
SYN-1032 : 2698/15 useful/useless nets, 2043/16 useful/useless insts
SYN-1015 : Optimize round 2, 32 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 37 IOs to PADs
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 2710/84 useful/useless nets, 2069/21 useful/useless insts
SYN-1016 : Merged 9 instances.
SYN-2571 : Optimize after map_dsp, round 1, 114 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 16 instances.
SYN-2501 : Optimize round 1, 32 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 12 macro adder
SYN-1019 : Optimized 5 mux instances.
SYN-1016 : Merged 9 instances.
SYN-1032 : 3042/5 useful/useless nets, 2401/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1002 : start command "get_pins config_inst.jtck"
RUN-1002 : start command "create_clock -name jtck -period 100 "
RUN-1102 : create_clock: clock name: jtck, type: 0, period: 100000, rise: 0, fall: 50000.
RUN-1002 : start command "get_clocks jtck"
RUN-1002 : start command "set_clock_uncertainty -hold 0.1 "
RUN-1002 : start command "get_clocks jtck"
RUN-1002 : start command "set_clock_groups -exclusive -group "
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 10851, tnet num: 3042, tinst num: 2400, tnode num: 14765, tedge num: 17638.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 3042 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 7 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 13 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 144 (3.74), #lev = 6 (1.85)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 144 (3.74), #lev = 6 (1.85)
SYN-3001 : Logic optimization runtime opt =   0.02 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 326 instances into 144 LUTs, name keeping = 76%.
SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 57 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 239 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 88 adder to BLE ...
SYN-4008 : Packed 88 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  1.166667s wall, 1.125000s user + 0.046875s system = 1.171875s CPU (100.4%)

RUN-1004 : used memory is 170 MB, reserved memory is 133 MB, peak memory is 201 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (135 clock/control pins, 0 other pins).
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 2028 instances
RUN-0007 : 515 luts, 1175 seqs, 166 mslices, 105 lslices, 37 pads, 19 brams, 5 dsps
RUN-1001 : There are total 2669 nets
RUN-1001 : 1863 nets have 2 pins
RUN-1001 : 649 nets have [3 - 5] pins
RUN-1001 : 87 nets have [6 - 10] pins
RUN-1001 : 25 nets have [11 - 20] pins
RUN-1001 : 38 nets have [21 - 99] pins
RUN-1001 : 7 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     250     
RUN-1001 :   No   |  No   |  Yes  |     215     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     260     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  18   |     9      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 28
PHY-3001 : Initial placement ...
PHY-3001 : design contains 2026 instances, 515 luts, 1175 seqs, 271 slices, 32 macros(271 instances: 166 mslices 105 lslices)
PHY-0007 : Cell area utilization is 6%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 10070, tnet num: 2667, tinst num: 2026, tnode num: 14079, tedge num: 17133.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2667 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 7 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 13 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.328042s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (100.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 700993
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 2026.
PHY-3001 : End clustering;  0.000030s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 627768, overlap = 54
PHY-3002 : Step(2): len = 590568, overlap = 45
PHY-3002 : Step(3): len = 567724, overlap = 54
PHY-3002 : Step(4): len = 542447, overlap = 47.25
PHY-3002 : Step(5): len = 530706, overlap = 51.75
PHY-3002 : Step(6): len = 454095, overlap = 47.25
PHY-3002 : Step(7): len = 428475, overlap = 42.75
PHY-3002 : Step(8): len = 419736, overlap = 42.75
PHY-3002 : Step(9): len = 409470, overlap = 42.75
PHY-3002 : Step(10): len = 397970, overlap = 45
PHY-3002 : Step(11): len = 386264, overlap = 42.75
PHY-3002 : Step(12): len = 380311, overlap = 47.25
PHY-3002 : Step(13): len = 365993, overlap = 47.25
PHY-3002 : Step(14): len = 355484, overlap = 47.25
PHY-3002 : Step(15): len = 350242, overlap = 47.25
PHY-3002 : Step(16): len = 338978, overlap = 47.25
PHY-3002 : Step(17): len = 325136, overlap = 45
PHY-3002 : Step(18): len = 319805, overlap = 47.25
PHY-3002 : Step(19): len = 314209, overlap = 45
PHY-3002 : Step(20): len = 280410, overlap = 47.25
PHY-3002 : Step(21): len = 271410, overlap = 54
PHY-3002 : Step(22): len = 268723, overlap = 54
PHY-3002 : Step(23): len = 239346, overlap = 54
PHY-3002 : Step(24): len = 224310, overlap = 54
PHY-3002 : Step(25): len = 221433, overlap = 54
PHY-3002 : Step(26): len = 210622, overlap = 54.5625
PHY-3002 : Step(27): len = 206521, overlap = 52.5
PHY-3002 : Step(28): len = 204640, overlap = 52.5625
PHY-3002 : Step(29): len = 194230, overlap = 50.3125
PHY-3002 : Step(30): len = 184996, overlap = 50.375
PHY-3002 : Step(31): len = 182396, overlap = 50.375
PHY-3002 : Step(32): len = 175304, overlap = 50.625
PHY-3002 : Step(33): len = 171441, overlap = 50.625
PHY-3002 : Step(34): len = 169396, overlap = 50.875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.6408e-05
PHY-3002 : Step(35): len = 172124, overlap = 53.0625
PHY-3002 : Step(36): len = 169977, overlap = 46.3125
PHY-3002 : Step(37): len = 168418, overlap = 48.5625
PHY-3002 : Step(38): len = 168984, overlap = 32.875
PHY-3002 : Step(39): len = 166479, overlap = 39.625
PHY-3002 : Step(40): len = 164673, overlap = 35.125
PHY-3002 : Step(41): len = 162242, overlap = 39.625
PHY-3002 : Step(42): len = 158856, overlap = 39.5625
PHY-3002 : Step(43): len = 155429, overlap = 39.4375
PHY-3002 : Step(44): len = 153871, overlap = 39.1875
PHY-3002 : Step(45): len = 147976, overlap = 39.25
PHY-3002 : Step(46): len = 143900, overlap = 34.9375
PHY-3002 : Step(47): len = 141863, overlap = 34.8125
PHY-3002 : Step(48): len = 140618, overlap = 34.5
PHY-3002 : Step(49): len = 127819, overlap = 42.75
PHY-3002 : Step(50): len = 125965, overlap = 40.5
PHY-3002 : Step(51): len = 124640, overlap = 40.5
PHY-3002 : Step(52): len = 121642, overlap = 38.25
PHY-3002 : Step(53): len = 120629, overlap = 36
PHY-3002 : Step(54): len = 117698, overlap = 36
PHY-3002 : Step(55): len = 114894, overlap = 36
PHY-3002 : Step(56): len = 112914, overlap = 38.25
PHY-3002 : Step(57): len = 111499, overlap = 38.25
PHY-3002 : Step(58): len = 108620, overlap = 38.25
PHY-3002 : Step(59): len = 107480, overlap = 33.75
PHY-3002 : Step(60): len = 106231, overlap = 38.25
PHY-3002 : Step(61): len = 104364, overlap = 36
PHY-3002 : Step(62): len = 99853.8, overlap = 38.25
PHY-3002 : Step(63): len = 98729.1, overlap = 40.5
PHY-3002 : Step(64): len = 95917.1, overlap = 36
PHY-3002 : Step(65): len = 94907.8, overlap = 33.75
PHY-3002 : Step(66): len = 93824, overlap = 38.25
PHY-3002 : Step(67): len = 92217.9, overlap = 33.75
PHY-3002 : Step(68): len = 85132.1, overlap = 38.25
PHY-3002 : Step(69): len = 82962.6, overlap = 36
PHY-3002 : Step(70): len = 82159.4, overlap = 40.5
PHY-3002 : Step(71): len = 81419.7, overlap = 38.25
PHY-3002 : Step(72): len = 80982.9, overlap = 36
PHY-3002 : Step(73): len = 80694.5, overlap = 36
PHY-3002 : Step(74): len = 79786.9, overlap = 31.5
PHY-3002 : Step(75): len = 77337, overlap = 31.5
PHY-3002 : Step(76): len = 75696.1, overlap = 29.25
PHY-3002 : Step(77): len = 75365, overlap = 29.25
PHY-3002 : Step(78): len = 74633.3, overlap = 31.5
PHY-3002 : Step(79): len = 73472, overlap = 33.75
PHY-3002 : Step(80): len = 73491.1, overlap = 31.5
PHY-3002 : Step(81): len = 72661.8, overlap = 31.5
PHY-3002 : Step(82): len = 72198, overlap = 31.5625
PHY-3002 : Step(83): len = 71898.7, overlap = 29.5
PHY-3002 : Step(84): len = 71155.6, overlap = 32.3125
PHY-3002 : Step(85): len = 70812.1, overlap = 33.4375
PHY-3002 : Step(86): len = 70560.6, overlap = 30.6875
PHY-3002 : Step(87): len = 70203.4, overlap = 33.7812
PHY-3002 : Step(88): len = 69986.8, overlap = 33.7812
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000132816
PHY-3002 : Step(89): len = 70561, overlap = 33.9688
PHY-3002 : Step(90): len = 70831.6, overlap = 34.0625
PHY-3002 : Step(91): len = 70817.4, overlap = 34.0625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000265632
PHY-3002 : Step(92): len = 71185.9, overlap = 34.1562
PHY-3002 : Step(93): len = 71579.7, overlap = 34.1562
PHY-3002 : Step(94): len = 71790.9, overlap = 34.1562
PHY-3001 : Before Legalized: Len = 71790.9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.010678s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 77264.7, Over = 4.90625
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 8%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2667 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.092575s wall, 0.078125s user + 0.015625s system = 0.093750s CPU (101.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000331441
PHY-3002 : Step(95): len = 78032.3, overlap = 14.9688
PHY-3002 : Step(96): len = 77752.1, overlap = 13.5938
PHY-3002 : Step(97): len = 76220.9, overlap = 13.4688
PHY-3002 : Step(98): len = 75855.4, overlap = 14.2812
PHY-3002 : Step(99): len = 74463.2, overlap = 16.5
PHY-3002 : Step(100): len = 73650.2, overlap = 17.9688
PHY-3002 : Step(101): len = 71908.4, overlap = 20.3438
PHY-3002 : Step(102): len = 71045.7, overlap = 26.5312
PHY-3002 : Step(103): len = 70399, overlap = 27.8125
PHY-3002 : Step(104): len = 69756.2, overlap = 27.6562
PHY-3002 : Step(105): len = 68363.9, overlap = 32.875
PHY-3002 : Step(106): len = 68274.8, overlap = 33.8125
PHY-3002 : Step(107): len = 67596, overlap = 34.875
PHY-3002 : Step(108): len = 66917.9, overlap = 34.8438
PHY-3002 : Step(109): len = 66557.3, overlap = 35.0625
PHY-3002 : Step(110): len = 65861.8, overlap = 35.0312
PHY-3002 : Step(111): len = 65677.2, overlap = 34.9688
PHY-3002 : Step(112): len = 65458.8, overlap = 35.4688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000662881
PHY-3002 : Step(113): len = 65132.5, overlap = 33.8438
PHY-3002 : Step(114): len = 65235.8, overlap = 34.3438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00132576
PHY-3002 : Step(115): len = 65268, overlap = 33.7188
PHY-3002 : Step(116): len = 65280.4, overlap = 33.2812
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00265152
PHY-3002 : Step(117): len = 65450, overlap = 31.7188
PHY-3002 : Step(118): len = 65351.3, overlap = 31.1875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 8%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2667 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.092098s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (101.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.70561e-05
PHY-3002 : Step(119): len = 67017.8, overlap = 69.7812
PHY-3002 : Step(120): len = 68397.2, overlap = 67.375
PHY-3002 : Step(121): len = 68617.3, overlap = 53.7188
PHY-3002 : Step(122): len = 68429.4, overlap = 53.0938
PHY-3002 : Step(123): len = 67756, overlap = 52.9688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000154112
PHY-3002 : Step(124): len = 67624.3, overlap = 53.0625
PHY-3002 : Step(125): len = 67756.4, overlap = 52.8438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000308224
PHY-3002 : Step(126): len = 67761.8, overlap = 49.5312
PHY-3002 : Step(127): len = 68617.6, overlap = 43.8438
PHY-3002 : Step(128): len = 69085.2, overlap = 44
PHY-3002 : Step(129): len = 69296.9, overlap = 47.5312
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000616448
PHY-3002 : Step(130): len = 69172.5, overlap = 47
PHY-3002 : Step(131): len = 68908.6, overlap = 45.5312
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.0012329
PHY-3002 : Step(132): len = 69930.2, overlap = 45.125
PHY-3002 : Step(133): len = 70437.2, overlap = 45
PHY-3002 : Step(134): len = 70933.4, overlap = 41.5312
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.00246579
PHY-3002 : Step(135): len = 70808.5, overlap = 41.0625
PHY-3002 : Step(136): len = 70763.3, overlap = 36.5
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 10070, tnet num: 2667, tinst num: 2026, tnode num: 14079, tedge num: 17133.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 109.81 peak overflow 2.59
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2669.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 77600, over cnt = 304(0%), over = 1267, worst = 20
PHY-1001 : End global iterations;  0.116500s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (107.3%)

PHY-1001 : Congestion index: top1 = 47.35, top5 = 29.43, top10 = 20.35, top15 = 15.05.
PHY-1001 : End incremental global routing;  0.178684s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (113.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2667 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 7 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 13 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.104385s wall, 0.093750s user + 0.015625s system = 0.109375s CPU (104.8%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1984 has valid locations, 5 needs to be replaced
PHY-3001 : design contains 2030 instances, 515 luts, 1179 seqs, 271 slices, 32 macros(271 instances: 166 mslices 105 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 71086.8
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 8%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 10082, tnet num: 2671, tinst num: 2030, tnode num: 14099, tedge num: 17149.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2671 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 7 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 13 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.383963s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (97.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(137): len = 71104.2, overlap = 0.875
PHY-3002 : Step(138): len = 71104.2, overlap = 0.875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 8%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2671 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.103452s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (90.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.0017369
PHY-3002 : Step(139): len = 71133.4, overlap = 36.5
PHY-3002 : Step(140): len = 71133.4, overlap = 36.5
PHY-3001 : Final: Len = 71133.4, Over = 36.5
PHY-3001 : End incremental placement;  0.571144s wall, 0.703125s user + 0.062500s system = 0.765625s CPU (134.1%)

OPT-1001 : Total overflow 109.81 peak overflow 2.59
OPT-1001 : End high-fanout net optimization;  0.906644s wall, 1.046875s user + 0.109375s system = 1.156250s CPU (127.5%)

OPT-1001 : Current memory(MB): used = 242, reserve = 202, peak = 242.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2022/2673.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 77736, over cnt = 304(0%), over = 1261, worst = 20
PHY-1002 : len = 86824, over cnt = 208(0%), over = 500, worst = 14
PHY-1002 : len = 90136, over cnt = 83(0%), over = 183, worst = 13
PHY-1002 : len = 91416, over cnt = 22(0%), over = 41, worst = 7
PHY-1002 : len = 92048, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.166019s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (122.4%)

PHY-1001 : Congestion index: top1 = 41.27, top5 = 28.53, top10 = 21.70, top15 = 16.96.
OPT-1001 : End congestion update;  0.220752s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (120.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2671 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.100949s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (92.9%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.329606s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (113.8%)

OPT-1001 : Current memory(MB): used = 239, reserve = 199, peak = 242.
OPT-1001 : End physical optimization;  1.524080s wall, 1.687500s user + 0.109375s system = 1.796875s CPU (117.9%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 515 LUT to BLE ...
SYN-4008 : Packed 515 LUT and 245 SEQ to BLE.
SYN-4003 : Packing 934 remaining SEQ's ...
SYN-4005 : Packed 222 SEQ with LUT/SLICE
SYN-4006 : 78 single LUT's are left
SYN-4006 : 712 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1227/1629 primitive instances ...
PHY-3001 : End packing;  0.065314s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (119.6%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 997 instances
RUN-1001 : 465 mslices, 465 lslices, 37 pads, 19 brams, 5 dsps
RUN-1001 : There are total 2437 nets
RUN-1001 : 1627 nets have 2 pins
RUN-1001 : 649 nets have [3 - 5] pins
RUN-1001 : 90 nets have [6 - 10] pins
RUN-1001 : 27 nets have [11 - 20] pins
RUN-1001 : 39 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 995 instances, 930 slices, 32 macros(271 instances: 166 mslices 105 lslices)
PHY-3001 : Cell area utilization is 12%
PHY-3001 : After packing: Len = 71378.6, Over = 54.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 12%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 8512, tnet num: 2435, tinst num: 995, tnode num: 11400, tedge num: 15038.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2435 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 7 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 13 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.354952s wall, 0.343750s user + 0.015625s system = 0.359375s CPU (101.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.81671e-05
PHY-3002 : Step(141): len = 70324.3, overlap = 51
PHY-3002 : Step(142): len = 69781.3, overlap = 56.25
PHY-3002 : Step(143): len = 69019, overlap = 53.5
PHY-3002 : Step(144): len = 68657.5, overlap = 57.5
PHY-3002 : Step(145): len = 68411.4, overlap = 58.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.63341e-05
PHY-3002 : Step(146): len = 68682.2, overlap = 56.25
PHY-3002 : Step(147): len = 69707.6, overlap = 54.5
PHY-3002 : Step(148): len = 70231.1, overlap = 54.75
PHY-3002 : Step(149): len = 70610.7, overlap = 52.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000192668
PHY-3002 : Step(150): len = 70972.6, overlap = 51
PHY-3002 : Step(151): len = 71407.9, overlap = 47.75
PHY-3001 : Before Legalized: Len = 71407.9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.088849s wall, 0.078125s user + 0.125000s system = 0.203125s CPU (228.6%)

PHY-3001 : After Legalized: Len = 87974.8, Over = 0
PHY-3001 : Trial Legalized: Len = 87974.8
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 12%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2435 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.067800s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (115.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000837092
PHY-3002 : Step(152): len = 84178.1, overlap = 7.5
PHY-3002 : Step(153): len = 81563.9, overlap = 12.25
PHY-3002 : Step(154): len = 79542, overlap = 19
PHY-3002 : Step(155): len = 78260.5, overlap = 22
PHY-3002 : Step(156): len = 77455.4, overlap = 23.75
PHY-3002 : Step(157): len = 76727.3, overlap = 25
PHY-3002 : Step(158): len = 76241, overlap = 25.75
PHY-3002 : Step(159): len = 75797.2, overlap = 27.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00157595
PHY-3002 : Step(160): len = 76135.6, overlap = 26.25
PHY-3002 : Step(161): len = 76217.7, overlap = 25.5
PHY-3002 : Step(162): len = 76217.7, overlap = 25.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0031519
PHY-3002 : Step(163): len = 76436.4, overlap = 24.75
PHY-3002 : Step(164): len = 76436.4, overlap = 24.75
PHY-3001 : Before Legalized: Len = 76436.4
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005280s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 82697.1, Over = 0
PHY-3001 : Legalized: Len = 82697.1, Over = 0
PHY-3001 : Spreading special nets. 31 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.008938s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (174.8%)

PHY-3001 : 46 instances has been re-located, deltaX = 33, deltaY = 23, maxDist = 2.
PHY-3001 : Final: Len = 83645.1, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 8512, tnet num: 2435, tinst num: 995, tnode num: 11400, tedge num: 15038.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 95/2437.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 94440, over cnt = 228(0%), over = 407, worst = 7
PHY-1002 : len = 96376, over cnt = 137(0%), over = 185, worst = 5
PHY-1002 : len = 97528, over cnt = 67(0%), over = 82, worst = 3
PHY-1002 : len = 98352, over cnt = 22(0%), over = 23, worst = 2
PHY-1002 : len = 98720, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.189227s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (115.6%)

PHY-1001 : Congestion index: top1 = 34.81, top5 = 26.37, top10 = 21.34, top15 = 17.55.
PHY-1001 : End incremental global routing;  0.245790s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (114.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2435 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 7 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 13 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.088256s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (106.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.373541s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (108.8%)

OPT-1001 : Current memory(MB): used = 239, reserve = 201, peak = 242.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2113/2437.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 98720, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007276s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (214.7%)

PHY-1001 : Congestion index: top1 = 34.81, top5 = 26.37, top10 = 21.34, top15 = 17.55.
OPT-1001 : End congestion update;  0.056302s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (111.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2435 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.068305s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (91.5%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 954 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 995 instances, 930 slices, 32 macros(271 instances: 166 mslices 105 lslices)
PHY-3001 : Cell area utilization is 12%
PHY-3001 : Initial: Len = 83565.6, Over = 0
PHY-3001 : Spreading special nets. 1 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005546s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 1 instances has been re-located, deltaX = 0, deltaY = 0, maxDist = 0.
PHY-3001 : Final: Len = 83559.6, Over = 0
PHY-3001 : End incremental legalization;  0.040968s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (76.3%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.183469s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (93.7%)

OPT-1001 : Current memory(MB): used = 245, reserve = 207, peak = 245.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2435 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.065770s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (95.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2099/2437.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 98664, over cnt = 4(0%), over = 5, worst = 2
PHY-1002 : len = 98664, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.024269s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (128.8%)

PHY-1001 : Congestion index: top1 = 34.78, top5 = 26.38, top10 = 21.33, top15 = 17.55.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2435 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.064468s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (96.9%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 34.379310
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  1.080338s wall, 1.093750s user + 0.000000s system = 1.093750s CPU (101.2%)

RUN-1003 : finish command "place" in  6.855226s wall, 9.281250s user + 3.234375s system = 12.515625s CPU (182.6%)

RUN-1004 : used memory is 223 MB, reserved memory is 183 MB, peak memory is 245 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 997 instances
RUN-1001 : 465 mslices, 465 lslices, 37 pads, 19 brams, 5 dsps
RUN-1001 : There are total 2437 nets
RUN-1001 : 1627 nets have 2 pins
RUN-1001 : 649 nets have [3 - 5] pins
RUN-1001 : 90 nets have [6 - 10] pins
RUN-1001 : 27 nets have [11 - 20] pins
RUN-1001 : 39 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 8512, tnet num: 2435, tinst num: 995, tnode num: 11400, tedge num: 15038.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 465 mslices, 465 lslices, 37 pads, 19 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2435 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 7 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 13 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 93304, over cnt = 220(0%), over = 392, worst = 7
PHY-1002 : len = 95568, over cnt = 125(0%), over = 156, worst = 3
PHY-1002 : len = 97304, over cnt = 22(0%), over = 22, worst = 1
PHY-1002 : len = 97648, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.197357s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (95.0%)

PHY-1001 : Congestion index: top1 = 34.42, top5 = 26.07, top10 = 21.09, top15 = 17.33.
PHY-1001 : End global routing;  0.252247s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (105.3%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 261, reserve = 223, peak = 264.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 523, reserve = 487, peak = 523.
PHY-1001 : End build detailed router design. 3.141306s wall, 3.109375s user + 0.046875s system = 3.156250s CPU (100.5%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 35224, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.293450s wall, 1.281250s user + 0.015625s system = 1.296875s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 555, reserve = 520, peak = 555.
PHY-1001 : End phase 1; 1.299151s wall, 1.281250s user + 0.015625s system = 1.296875s CPU (99.8%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 41% nets.
PHY-1001 : Routed 52% nets.
PHY-1001 : Routed 68% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 246808, over cnt = 67(0%), over = 67, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 556, reserve = 521, peak = 556.
PHY-1001 : End initial routed; 2.080979s wall, 3.515625s user + 0.109375s system = 3.625000s CPU (174.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/2159(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.629   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.695   |  -53.056  |  23   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.406327s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 559, reserve = 523, peak = 559.
PHY-1001 : End phase 2; 2.487399s wall, 3.921875s user + 0.109375s system = 4.031250s CPU (162.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 246808, over cnt = 67(0%), over = 67, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.018233s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (85.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 246488, over cnt = 15(0%), over = 15, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.066915s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (116.8%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 246464, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.041272s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (75.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/2159(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.629   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.695   |  -53.056  |  23   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.400456s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (101.4%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 12 feed throughs used by 11 nets
PHY-1001 : End commit to database; 0.220533s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (99.2%)

PHY-1001 : Current memory(MB): used = 574, reserve = 539, peak = 574.
PHY-1001 : End phase 3; 0.882262s wall, 0.890625s user + 0.000000s system = 0.890625s CPU (100.9%)

PHY-1003 : Routed, final wirelength = 246464
PHY-1001 : Current memory(MB): used = 575, reserve = 540, peak = 575.
PHY-1001 : End export database. 0.010405s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (150.2%)

PHY-1001 : End detail routing;  7.997896s wall, 9.375000s user + 0.171875s system = 9.546875s CPU (119.4%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 8512, tnet num: 2435, tinst num: 995, tnode num: 11400, tedge num: 15038.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net config_inst_syn_2 endpin cw_top/wrapper_cwc_top/cfg_int_inst/tap_inst/rst_reg_syn_13.sr slack -2447ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net config_inst_syn_2 endpin cw_top/wrapper_cwc_top/cfg_int_inst/tap_inst/rst_reg_syn_15.sr slack -2347ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net config_inst_syn_2 endpin cw_top/wrapper_cwc_top/cfg_int_inst/tap_inst/rst_reg_syn_17.sr slack -2227ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net config_inst_syn_2 endpin cw_top/wrapper_cwc_top/cfg_int_inst/tap_inst/rst_reg_syn_19.sr slack -2165ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net config_inst_syn_2 endpin cw_top/wrapper_cwc_top/cfg_int_inst/tap_inst/rst_reg_syn_21.sr slack -2125ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[8] slack -86ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[4] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[6] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[52] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[7] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[53] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[8] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg1_syn_221.mi[0] slack -2695ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_73.mi[1] slack -2683ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_75.mi[1] slack -2451ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_79.mi[0] slack -2695ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_79.mi[1] slack -2563ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_82.mi[0] slack -2695ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_82.mi[1] slack -2431ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_85.mi[0] slack -2683ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_85.mi[1] slack -2695ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_88.mi[0] slack -2456ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_88.mi[1] slack -2695ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_90.mi[0] slack -2587ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin wendu/add1_syn_166.mi[0] slack -2456ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin wendu/add1_syn_166.mi[1] slack -2695ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin wendu/lt2_syn_90.mi[0] slack -2587ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin wendu/lt2_syn_90.mi[1] slack -2587ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 8654, tnet num: 2506, tinst num: 1066, tnode num: 11542, tedge num: 15180.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin wendu/add1_syn_166_mi[0] slack -414ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin wendu/lt2_syn_90_mi[0] slack -462ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_75_mi[1] slack -541ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg1_syn_221_mi[0] slack -637ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_79_mi[0] slack -396ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_79_mi[1] slack -333ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_82_mi[1] slack -302ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_88_mi[1] slack -197ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_85_mi[0] slack -20ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_85_mi[1] slack -295ps
RUN-1001 : Hold violation on net config_inst_syn_2_holdbuf33 endpin cw_top/wrapper_cwc_top/cfg_int_inst/tap_inst/rst_reg_syn_21_sr slack -306ps
RUN-1001 : Hold violation on net config_inst_syn_2_holdbuf30 endpin cw_top/wrapper_cwc_top/cfg_int_inst/tap_inst/rst_reg_syn_15_sr slack -485ps
RUN-1001 : Hold violation on net config_inst_syn_2_holdbuf3 endpin cw_top/wrapper_cwc_top/cfg_int_inst/tap_inst/rst_reg_syn_13_sr slack -584ps
RUN-1001 : Hold violation on net config_inst_syn_2_holdbuf32 endpin cw_top/wrapper_cwc_top/cfg_int_inst/tap_inst/rst_reg_syn_19_sr slack -250ps
RUN-1001 : Hold violation on net config_inst_syn_2_holdbuf31 endpin cw_top/wrapper_cwc_top/cfg_int_inst/tap_inst/rst_reg_syn_17_sr slack -637ps
RUN-1001 : End hold fix;  3.725075s wall, 3.703125s user + 0.187500s system = 3.890625s CPU (104.4%)

RUN-1003 : finish command "route" in  12.367282s wall, 13.718750s user + 0.359375s system = 14.078125s CPU (113.8%)

RUN-1004 : used memory is 568 MB, reserved memory is 537 MB, peak memory is 575 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                     1209   out of  19600    6.17%
#reg                     1263   out of  19600    6.44%
#le                      1921
  #lut only               658   out of   1921   34.25%
  #reg only               712   out of   1921   37.06%
  #lut&reg                551   out of   1921   28.68%
#dsp                        5   out of     29   17.24%
#bram                      19   out of     64   29.69%
  #bram9k                  19
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         534
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         103
#3        config_inst_syn_9               GCLK               config             config_inst.jtck              79
#4        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q0    39
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-------------------------------------------------------------------------------------------------------------------------------------------+
|Instance                            |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-------------------------------------------------------------------------------------------------------------------------------------------+
|top                                 |IFOG501_2B                                       |1921   |938     |271     |1296    |19      |5       |
|  CLK120                            |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process                    |SignalProcessing                                 |1015   |299     |128     |823     |4       |5       |
|    ctrl_signal                     |SignalGenerator                                  |34     |30      |4       |23      |0       |0       |
|    demodu                          |Demodulation                                     |436    |100     |41      |349     |4       |0       |
|      fifo                          |Asys_fifo56X16                                   |52     |26      |6       |43      |4       |0       |
|        ram_inst                    |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst         |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |6       |0       |16      |0       |0       |
|        wr_to_rd_cross_inst         |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |11     |5       |0       |11      |0       |0       |
|    integ                           |Integration                                      |140    |27      |15      |112     |0       |0       |
|    modu                            |Modulation                                       |69     |34      |14      |65      |0       |1       |
|    rs422                           |Rs422Output                                      |309    |89      |46      |254     |0       |4       |
|    trans                           |SquareWaveGenerator                              |27     |19      |8       |20      |0       |0       |
|  u_uart                            |UART_Control                                     |153    |133     |7       |114     |0       |0       |
|    U0                              |speed_select_Tx                                  |38     |31      |7       |18      |0       |0       |
|    U1                              |uart_tx                                          |14     |14      |0       |14      |0       |0       |
|    U2                              |Ctrl_Data                                        |101    |88      |0       |82      |0       |0       |
|  wendu                             |DS18B20                                          |280    |235     |45      |81      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER                                   |401    |228     |79      |227     |0       |0       |
|    wrapper_cwc_top                 |cwc_top                                          |401    |228     |79      |227     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int                                      |139    |58      |0       |139     |0       |0       |
|        reg_inst                    |register                                         |132    |54      |0       |132     |0       |0       |
|        tap_inst                    |tap                                              |7      |4       |0       |7       |0       |0       |
|      trigger_inst                  |trigger                                          |262    |170     |79      |88      |0       |0       |
|        bus_inst                    |bus_top                                          |52     |34      |18      |16      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det                                          |52     |34      |18      |16      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl                                         |109    |80      |29      |51      |0       |0       |
+-------------------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1659  
    #2          2       431   
    #3          3       191   
    #4          4        27   
    #5        5-10       94   
    #6        11-50      59   
    #7       51-100      1    
    #8       101-500     1    
  Average     2.17            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 8654, tnet num: 2506, tinst num: 1066, tnode num: 11542, tedge num: 15180.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2506 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 7 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 13 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 6. Number of clock nets = 6 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 1066
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2508, pip num: 19870
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 18
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1600 valid insts, and 53544 bits set as '1'.
BIT-1004 : the usercode register value: 00000000001010000010010100101001
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.825261s wall, 22.875000s user + 0.078125s system = 22.953125s CPU (600.0%)

RUN-1004 : used memory is 580 MB, reserved memory is 543 MB, peak memory is 714 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250811_144358.log"
