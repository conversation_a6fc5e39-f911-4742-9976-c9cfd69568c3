============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Thu Jul 31 17:47:25 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1581 instances
RUN-0007 : 370 luts, 964 seqs, 126 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2114 nets
RUN-1001 : 1538 nets have 2 pins
RUN-1001 : 476 nets have [3 - 5] pins
RUN-1001 : 58 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     263     
RUN-1001 :   No   |  No   |  Yes  |     122     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1579 instances, 370 luts, 964 seqs, 196 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7532, tnet num: 2112, tinst num: 1579, tnode num: 10699, tedge num: 12767.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2112 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.267759s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (99.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 521896
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1579.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 446124, overlap = 20.25
PHY-3002 : Step(2): len = 426467, overlap = 18
PHY-3002 : Step(3): len = 411757, overlap = 18
PHY-3002 : Step(4): len = 403003, overlap = 15.75
PHY-3002 : Step(5): len = 389714, overlap = 13.5
PHY-3002 : Step(6): len = 377007, overlap = 13.5
PHY-3002 : Step(7): len = 369666, overlap = 13.5
PHY-3002 : Step(8): len = 359823, overlap = 15.75
PHY-3002 : Step(9): len = 351087, overlap = 15.75
PHY-3002 : Step(10): len = 344163, overlap = 15.75
PHY-3002 : Step(11): len = 336161, overlap = 13.5
PHY-3002 : Step(12): len = 327234, overlap = 13.5
PHY-3002 : Step(13): len = 321388, overlap = 13.5
PHY-3002 : Step(14): len = 313407, overlap = 13.5
PHY-3002 : Step(15): len = 303573, overlap = 13.5
PHY-3002 : Step(16): len = 298539, overlap = 13.5
PHY-3002 : Step(17): len = 292467, overlap = 13.5
PHY-3002 : Step(18): len = 282498, overlap = 13.5
PHY-3002 : Step(19): len = 277398, overlap = 13.5
PHY-3002 : Step(20): len = 272818, overlap = 13.5
PHY-3002 : Step(21): len = 266035, overlap = 13.5
PHY-3002 : Step(22): len = 260172, overlap = 13.5
PHY-3002 : Step(23): len = 255569, overlap = 13.5
PHY-3002 : Step(24): len = 249997, overlap = 13.5
PHY-3002 : Step(25): len = 243610, overlap = 18
PHY-3002 : Step(26): len = 238252, overlap = 20.25
PHY-3002 : Step(27): len = 234082, overlap = 20.25
PHY-3002 : Step(28): len = 226346, overlap = 20.25
PHY-3002 : Step(29): len = 221708, overlap = 20.25
PHY-3002 : Step(30): len = 218541, overlap = 20.25
PHY-3002 : Step(31): len = 209564, overlap = 20.25
PHY-3002 : Step(32): len = 200514, overlap = 20.25
PHY-3002 : Step(33): len = 198053, overlap = 20.25
PHY-3002 : Step(34): len = 192397, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000133444
PHY-3002 : Step(35): len = 194303, overlap = 13.5
PHY-3002 : Step(36): len = 192408, overlap = 13.5
PHY-3002 : Step(37): len = 191124, overlap = 18
PHY-3002 : Step(38): len = 189082, overlap = 13.5
PHY-3002 : Step(39): len = 183221, overlap = 9
PHY-3002 : Step(40): len = 175269, overlap = 6.75
PHY-3002 : Step(41): len = 172969, overlap = 9
PHY-3002 : Step(42): len = 170332, overlap = 6.75
PHY-3002 : Step(43): len = 167348, overlap = 4.5
PHY-3002 : Step(44): len = 163899, overlap = 4.5
PHY-3002 : Step(45): len = 161098, overlap = 4.5
PHY-3002 : Step(46): len = 158513, overlap = 6.75
PHY-3002 : Step(47): len = 154358, overlap = 6.75
PHY-3002 : Step(48): len = 150992, overlap = 6.75
PHY-3002 : Step(49): len = 150010, overlap = 4.5
PHY-3002 : Step(50): len = 147624, overlap = 4.5
PHY-3002 : Step(51): len = 135911, overlap = 6.75
PHY-3002 : Step(52): len = 132284, overlap = 0
PHY-3002 : Step(53): len = 131411, overlap = 2.25
PHY-3002 : Step(54): len = 129542, overlap = 2.25
PHY-3002 : Step(55): len = 127619, overlap = 6.75
PHY-3002 : Step(56): len = 123678, overlap = 6.75
PHY-3002 : Step(57): len = 121425, overlap = 6.75
PHY-3002 : Step(58): len = 119764, overlap = 6.75
PHY-3002 : Step(59): len = 117844, overlap = 4.5
PHY-3002 : Step(60): len = 116149, overlap = 4.5
PHY-3002 : Step(61): len = 113309, overlap = 9
PHY-3002 : Step(62): len = 106904, overlap = 9
PHY-3002 : Step(63): len = 105824, overlap = 6.75
PHY-3002 : Step(64): len = 104432, overlap = 6.75
PHY-3002 : Step(65): len = 102205, overlap = 6.75
PHY-3002 : Step(66): len = 98464.2, overlap = 6.75
PHY-3002 : Step(67): len = 97943.9, overlap = 6.75
PHY-3002 : Step(68): len = 95311.8, overlap = 9
PHY-3002 : Step(69): len = 93967.5, overlap = 6.75
PHY-3002 : Step(70): len = 92447.1, overlap = 6.75
PHY-3002 : Step(71): len = 90945.3, overlap = 2.25
PHY-3002 : Step(72): len = 88783.8, overlap = 6.75
PHY-3002 : Step(73): len = 88514.9, overlap = 9
PHY-3002 : Step(74): len = 87015.2, overlap = 6.75
PHY-3002 : Step(75): len = 85532.9, overlap = 9
PHY-3002 : Step(76): len = 83639.6, overlap = 11.25
PHY-3002 : Step(77): len = 82334.2, overlap = 11.25
PHY-3002 : Step(78): len = 80540.1, overlap = 9
PHY-3002 : Step(79): len = 79376, overlap = 9
PHY-3002 : Step(80): len = 79069.4, overlap = 9
PHY-3002 : Step(81): len = 78252.2, overlap = 6.75
PHY-3002 : Step(82): len = 76676.9, overlap = 6.75
PHY-3002 : Step(83): len = 75285.8, overlap = 9
PHY-3002 : Step(84): len = 74439.2, overlap = 9
PHY-3002 : Step(85): len = 73663.8, overlap = 9
PHY-3002 : Step(86): len = 72758.3, overlap = 9
PHY-3002 : Step(87): len = 71862.5, overlap = 9
PHY-3002 : Step(88): len = 70948.1, overlap = 6.75
PHY-3002 : Step(89): len = 70316.7, overlap = 11.25
PHY-3002 : Step(90): len = 69326.2, overlap = 11.25
PHY-3002 : Step(91): len = 68381.5, overlap = 9
PHY-3002 : Step(92): len = 65850.5, overlap = 9
PHY-3002 : Step(93): len = 65777.8, overlap = 9
PHY-3002 : Step(94): len = 64653.9, overlap = 9
PHY-3002 : Step(95): len = 64309.1, overlap = 11.25
PHY-3002 : Step(96): len = 64361.8, overlap = 6.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000266889
PHY-3002 : Step(97): len = 64729.1, overlap = 9
PHY-3002 : Step(98): len = 64720.8, overlap = 9
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000533777
PHY-3002 : Step(99): len = 64972.1, overlap = 9
PHY-3002 : Step(100): len = 65077.4, overlap = 6.75
PHY-3001 : Before Legalized: Len = 65077.4
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007250s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (862.1%)

PHY-3001 : After Legalized: Len = 68080, Over = 0
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2112 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.064061s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (73.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(101): len = 68127.8, overlap = 1.6875
PHY-3002 : Step(102): len = 66309.5, overlap = 1.375
PHY-3002 : Step(103): len = 65384.3, overlap = 1.5
PHY-3002 : Step(104): len = 63466.1, overlap = 1.5625
PHY-3002 : Step(105): len = 62203.3, overlap = 1.5
PHY-3002 : Step(106): len = 61077.1, overlap = 2.25
PHY-3002 : Step(107): len = 59359, overlap = 3.5625
PHY-3002 : Step(108): len = 57398.1, overlap = 5.5625
PHY-3002 : Step(109): len = 56220, overlap = 6.125
PHY-3002 : Step(110): len = 54812.7, overlap = 6.125
PHY-3002 : Step(111): len = 53377, overlap = 8.625
PHY-3002 : Step(112): len = 51977.3, overlap = 9.9375
PHY-3002 : Step(113): len = 50944.3, overlap = 10
PHY-3002 : Step(114): len = 48926.4, overlap = 9.78125
PHY-3002 : Step(115): len = 48086.4, overlap = 10.75
PHY-3002 : Step(116): len = 47244.4, overlap = 10.75
PHY-3002 : Step(117): len = 46840, overlap = 11
PHY-3002 : Step(118): len = 45683.2, overlap = 11.625
PHY-3002 : Step(119): len = 45401.1, overlap = 11.9375
PHY-3002 : Step(120): len = 45321.6, overlap = 12.1875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000203628
PHY-3002 : Step(121): len = 45032.8, overlap = 12.0938
PHY-3002 : Step(122): len = 44753.9, overlap = 11.7188
PHY-3002 : Step(123): len = 44739.2, overlap = 11.5312
PHY-3002 : Step(124): len = 44464.3, overlap = 11.5938
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000407257
PHY-3002 : Step(125): len = 44441.5, overlap = 11.7188
PHY-3002 : Step(126): len = 44529.2, overlap = 10.9688
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2112 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.062453s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.29124e-05
PHY-3002 : Step(127): len = 44676.7, overlap = 65.3125
PHY-3002 : Step(128): len = 45111.5, overlap = 66.0625
PHY-3002 : Step(129): len = 45628.3, overlap = 64.8125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000165825
PHY-3002 : Step(130): len = 45736, overlap = 64.5
PHY-3002 : Step(131): len = 46077.6, overlap = 62.7812
PHY-3002 : Step(132): len = 47066.2, overlap = 59.375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00033165
PHY-3002 : Step(133): len = 47602.1, overlap = 53.2812
PHY-3002 : Step(134): len = 48925.8, overlap = 47.625
PHY-3002 : Step(135): len = 51503.4, overlap = 32.8125
PHY-3002 : Step(136): len = 51087.2, overlap = 31.7812
PHY-3002 : Step(137): len = 50937.9, overlap = 31.5938
PHY-3002 : Step(138): len = 50512.8, overlap = 28.3438
PHY-3002 : Step(139): len = 50512.8, overlap = 28.3438
PHY-3002 : Step(140): len = 50407.4, overlap = 28.2812
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000663299
PHY-3002 : Step(141): len = 50951.3, overlap = 27.125
PHY-3002 : Step(142): len = 51243.1, overlap = 26.5938
PHY-3002 : Step(143): len = 51783.7, overlap = 23.9062
PHY-3002 : Step(144): len = 52062.4, overlap = 24.8438
PHY-3002 : Step(145): len = 51830, overlap = 25.75
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.0013266
PHY-3002 : Step(146): len = 51598.4, overlap = 25.6875
PHY-3002 : Step(147): len = 51369.1, overlap = 25.2188
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7532, tnet num: 2112, tinst num: 1579, tnode num: 10699, tedge num: 12767.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 88.69 peak overflow 2.56
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2114.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54232, over cnt = 230(0%), over = 1045, worst = 26
PHY-1001 : End global iterations;  0.081986s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (133.4%)

PHY-1001 : Congestion index: top1 = 42.89, top5 = 25.09, top10 = 15.87, top15 = 11.29.
PHY-1001 : End incremental global routing;  0.143102s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (120.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2112 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.080832s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (96.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.260637s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (113.9%)

OPT-1001 : Current memory(MB): used = 209, reserve = 176, peak = 209.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1613/2114.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54232, over cnt = 230(0%), over = 1045, worst = 26
PHY-1002 : len = 59664, over cnt = 172(0%), over = 510, worst = 16
PHY-1002 : len = 61200, over cnt = 124(0%), over = 339, worst = 12
PHY-1002 : len = 65776, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 65840, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.122920s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (101.7%)

PHY-1001 : Congestion index: top1 = 37.05, top5 = 24.51, top10 = 17.51, top15 = 13.02.
OPT-1001 : End congestion update;  0.181251s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (103.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2112 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.073945s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (105.7%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.259363s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (102.4%)

OPT-1001 : Current memory(MB): used = 212, reserve = 179, peak = 212.
OPT-1001 : End physical optimization;  0.833106s wall, 0.843750s user + 0.031250s system = 0.875000s CPU (105.0%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 370 LUT to BLE ...
SYN-4008 : Packed 370 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 775 remaining SEQ's ...
SYN-4005 : Packed 75 SEQ with LUT/SLICE
SYN-4006 : 120 single LUT's are left
SYN-4006 : 700 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1070/1381 primitive instances ...
PHY-3001 : End packing;  0.080500s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (97.1%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 807 instances
RUN-1001 : 378 mslices, 378 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1933 nets
RUN-1001 : 1362 nets have 2 pins
RUN-1001 : 467 nets have [3 - 5] pins
RUN-1001 : 62 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 805 instances, 756 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 51479.6, Over = 49.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6320, tnet num: 1931, tinst num: 805, tnode num: 8625, tedge num: 11163.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1931 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.383454s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (97.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.64846e-05
PHY-3002 : Step(148): len = 50947.6, overlap = 51.75
PHY-3002 : Step(149): len = 50352.4, overlap = 51.25
PHY-3002 : Step(150): len = 49773.7, overlap = 53
PHY-3002 : Step(151): len = 49551.4, overlap = 55.5
PHY-3002 : Step(152): len = 49316, overlap = 54.25
PHY-3002 : Step(153): len = 48701.5, overlap = 52.25
PHY-3002 : Step(154): len = 48408.5, overlap = 53.5
PHY-3002 : Step(155): len = 48135.6, overlap = 53.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.29692e-05
PHY-3002 : Step(156): len = 48428.2, overlap = 53
PHY-3002 : Step(157): len = 49017.7, overlap = 52
PHY-3002 : Step(158): len = 49428.8, overlap = 49.75
PHY-3002 : Step(159): len = 49784.8, overlap = 50
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000145938
PHY-3002 : Step(160): len = 50040.5, overlap = 46.5
PHY-3002 : Step(161): len = 50567.9, overlap = 44.25
PHY-3001 : Before Legalized: Len = 50567.9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.200939s wall, 0.171875s user + 0.234375s system = 0.406250s CPU (202.2%)

PHY-3001 : After Legalized: Len = 64836.5, Over = 0
PHY-3001 : Trial Legalized: Len = 64836.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1931 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.085304s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (109.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00129838
PHY-3002 : Step(162): len = 60571.5, overlap = 9
PHY-3002 : Step(163): len = 59671.2, overlap = 9
PHY-3002 : Step(164): len = 57022.8, overlap = 12
PHY-3002 : Step(165): len = 55862.6, overlap = 11.25
PHY-3002 : Step(166): len = 55416.1, overlap = 17
PHY-3002 : Step(167): len = 55016.4, overlap = 18.25
PHY-3002 : Step(168): len = 54583.7, overlap = 19.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00250777
PHY-3002 : Step(169): len = 54805.7, overlap = 19
PHY-3002 : Step(170): len = 54806.6, overlap = 19
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00501555
PHY-3002 : Step(171): len = 54807.2, overlap = 18.5
PHY-3002 : Step(172): len = 54795, overlap = 18
PHY-3001 : Before Legalized: Len = 54795
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.009868s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 59548.7, Over = 0
PHY-3001 : Legalized: Len = 59548.7, Over = 0
PHY-3001 : Spreading special nets. 9 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.009739s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (160.4%)

PHY-3001 : 12 instances has been re-located, deltaX = 4, deltaY = 6, maxDist = 1.
PHY-3001 : Final: Len = 59746.7, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6320, tnet num: 1931, tinst num: 805, tnode num: 8625, tedge num: 11163.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 48/1933.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 65160, over cnt = 136(0%), over = 207, worst = 5
PHY-1002 : len = 66008, over cnt = 72(0%), over = 89, worst = 4
PHY-1002 : len = 67176, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 67256, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.167090s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (130.9%)

PHY-1001 : Congestion index: top1 = 30.15, top5 = 21.78, top10 = 16.79, top15 = 13.10.
PHY-1001 : End incremental global routing;  0.233315s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (127.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1931 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.074883s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (104.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.344578s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (117.9%)

OPT-1001 : Current memory(MB): used = 214, reserve = 182, peak = 214.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1696/1933.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 67256, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008279s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (188.7%)

PHY-1001 : Congestion index: top1 = 30.15, top5 = 21.78, top10 = 16.79, top15 = 13.10.
OPT-1001 : End congestion update;  0.063966s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (97.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1931 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059763s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.6%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.126075s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (99.1%)

OPT-1001 : Current memory(MB): used = 217, reserve = 184, peak = 217.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1931 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056172s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (111.3%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1696/1933.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 67256, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006401s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 30.15, top5 = 21.78, top10 = 16.79, top15 = 13.10.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1931 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056769s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (110.1%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 29.689655
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  1.056687s wall, 1.125000s user + 0.000000s system = 1.125000s CPU (106.5%)

RUN-1003 : finish command "place" in  6.637926s wall, 8.953125s user + 3.687500s system = 12.640625s CPU (190.4%)

RUN-1004 : used memory is 196 MB, reserved memory is 163 MB, peak memory is 218 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 807 instances
RUN-1001 : 378 mslices, 378 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1933 nets
RUN-1001 : 1362 nets have 2 pins
RUN-1001 : 467 nets have [3 - 5] pins
RUN-1001 : 62 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6320, tnet num: 1931, tinst num: 805, tnode num: 8625, tedge num: 11163.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 378 mslices, 378 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1931 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 64392, over cnt = 137(0%), over = 203, worst = 4
PHY-1002 : len = 65240, over cnt = 70(0%), over = 89, worst = 3
PHY-1002 : len = 66160, over cnt = 11(0%), over = 14, worst = 3
PHY-1002 : len = 66448, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.138120s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (158.4%)

PHY-1001 : Congestion index: top1 = 30.30, top5 = 21.64, top10 = 16.66, top15 = 12.97.
PHY-1001 : End global routing;  0.194753s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (144.4%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 234, reserve = 201, peak = 234.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 495, reserve = 466, peak = 495.
PHY-1001 : End build detailed router design. 3.592718s wall, 3.453125s user + 0.031250s system = 3.484375s CPU (97.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 32664, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.152716s wall, 1.078125s user + 0.015625s system = 1.093750s CPU (94.9%)

PHY-1001 : Current memory(MB): used = 527, reserve = 500, peak = 527.
PHY-1001 : End phase 1; 1.159823s wall, 1.078125s user + 0.015625s system = 1.093750s CPU (94.3%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 28% nets.
PHY-1001 : Routed 34% nets.
PHY-1001 : Routed 44% nets.
PHY-1001 : Routed 61% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 178712, over cnt = 34(0%), over = 34, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 528, reserve = 500, peak = 528.
PHY-1001 : End initial routed; 1.493826s wall, 2.250000s user + 0.109375s system = 2.359375s CPU (157.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1718(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.629   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -47.213  |  23   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.367347s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (102.1%)

PHY-1001 : Current memory(MB): used = 530, reserve = 502, peak = 530.
PHY-1001 : End phase 2; 1.861272s wall, 2.625000s user + 0.109375s system = 2.734375s CPU (146.9%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 178712, over cnt = 34(0%), over = 34, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.018906s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (82.6%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 178624, over cnt = 8(0%), over = 8, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.039586s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (157.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 178720, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.028688s wall, 0.031250s user + 0.031250s system = 0.062500s CPU (217.9%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 178760, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.020402s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (153.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1718(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.629   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -47.213  |  23   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.373736s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (100.3%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 6 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.175942s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (88.8%)

PHY-1001 : Current memory(MB): used = 544, reserve = 516, peak = 544.
PHY-1001 : End phase 3; 0.802632s wall, 0.781250s user + 0.046875s system = 0.828125s CPU (103.2%)

PHY-1003 : Routed, final wirelength = 178760
PHY-1001 : Current memory(MB): used = 544, reserve = 516, peak = 544.
PHY-1001 : End export database. 0.011511s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (135.7%)

PHY-1001 : End detail routing;  7.610333s wall, 8.125000s user + 0.218750s system = 8.343750s CPU (109.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6320, tnet num: 1931, tinst num: 805, tnode num: 8625, tedge num: 11163.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[33] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dib[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[8] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[0] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[4] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[5] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[6] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[54] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_66.dia[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[14] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[5] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[15] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[6] slack -86ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/asy_w_rst1 endpin signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_15.sr slack -89ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/asy_w_rst1 endpin signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_18.sr slack -89ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_56.mi[0] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_56.mi[1] slack -2684ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_59.mi[0] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_59.mi[1] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_62.mi[0] slack -2684ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_62.mi[1] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_65.mi[0] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_65.mi[1] slack -2897ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_68.mi[0] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_68.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_71.mi[0] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_71.mi[1] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_74.mi[0] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_74.mi[1] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_77.mi[0] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_77.mi[1] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6442, tnet num: 1992, tinst num: 866, tnode num: 8747, tedge num: 11285.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_65_mi[0] slack -716ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg2_syn_65_mi[1] slack -185ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_62_mi[1] slack -607ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_59_mi[1] slack -668ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_62_mi[0] slack -480ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_68_mi[0] slack -229ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_68_mi[1] slack -489ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_71_mi[0] slack -725ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_74_mi[0] slack -665ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_77_mi[1] slack -395ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_77_mi[0] slack -697ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_74_mi[1] slack -506ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_71_mi[1] slack -551ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_56_mi[0] slack -379ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_56_mi[1] slack -439ps
RUN-1001 : End hold fix;  3.332925s wall, 3.609375s user + 0.218750s system = 3.828125s CPU (114.9%)

RUN-1003 : finish command "route" in  11.511703s wall, 12.375000s user + 0.453125s system = 12.828125s CPU (111.4%)

RUN-1004 : used memory is 533 MB, reserved memory is 508 MB, peak memory is 545 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      889   out of  19600    4.54%
#reg                     1053   out of  19600    5.37%
#le                      1589
  #lut only               536   out of   1589   33.73%
  #reg only               700   out of   1589   44.05%
  #lut&reg                353   out of   1589   22.22%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         480
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         101
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    43
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1589   |693     |196     |1086    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1089   |294     |134     |864     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |34     |28      |6       |24      |0       |0       |
|    demodu                  |Demodulation                                     |455    |102     |45      |350     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |58     |32      |6       |45      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |5       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |12     |6       |0       |12      |0       |0       |
|    integ                   |Integration                                      |144    |21      |15      |116     |0       |0       |
|    modu                    |Modulation                                       |96     |41      |14      |94      |0       |1       |
|    rs422                   |Rs422Output                                      |325    |75      |46      |258     |0       |4       |
|    trans                   |SquareWaveGenerator                              |35     |27      |8       |22      |0       |0       |
|  u_uart                    |UART_Control                                     |175    |129     |7       |118     |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |26     |18      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |112    |83      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |305    |260     |45      |70      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1384  
    #2          2       336   
    #3          3       104   
    #4          4        27   
    #5        5-10       67   
    #6        11-50      31   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6442, tnet num: 1992, tinst num: 866, tnode num: 8747, tedge num: 11285.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1992 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 866
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1994, pip num: 14469
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 9
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1328 valid insts, and 38781 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  4.557350s wall, 24.750000s user + 0.062500s system = 24.812500s CPU (544.5%)

RUN-1004 : used memory is 549 MB, reserved memory is 520 MB, peak memory is 678 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250731_174725.log"
