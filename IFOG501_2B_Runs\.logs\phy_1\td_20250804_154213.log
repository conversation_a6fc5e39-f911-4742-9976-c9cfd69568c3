============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Mon Aug  4 15:42:13 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1576 instances
RUN-0007 : 373 luts, 953 seqs, 129 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2120 nets
RUN-1001 : 1542 nets have 2 pins
RUN-1001 : 476 nets have [3 - 5] pins
RUN-1001 : 57 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     251     
RUN-1001 :   No   |  No   |  Yes  |     123     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1574 instances, 373 luts, 953 seqs, 199 slices, 20 macros(199 instances: 129 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7554, tnet num: 2118, tinst num: 1574, tnode num: 10716, tedge num: 12836.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2118 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.258400s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (102.8%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 525070
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1574.
PHY-3001 : End clustering;  0.000033s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 454779, overlap = 20.25
PHY-3002 : Step(2): len = 431747, overlap = 13.5
PHY-3002 : Step(3): len = 420824, overlap = 20.25
PHY-3002 : Step(4): len = 408693, overlap = 11.25
PHY-3002 : Step(5): len = 397767, overlap = 13.5
PHY-3002 : Step(6): len = 384603, overlap = 13.5
PHY-3002 : Step(7): len = 377986, overlap = 13.5
PHY-3002 : Step(8): len = 367432, overlap = 15.75
PHY-3002 : Step(9): len = 357559, overlap = 15.75
PHY-3002 : Step(10): len = 349894, overlap = 15.75
PHY-3002 : Step(11): len = 343119, overlap = 15.75
PHY-3002 : Step(12): len = 329901, overlap = 13.5
PHY-3002 : Step(13): len = 324020, overlap = 13.5
PHY-3002 : Step(14): len = 316857, overlap = 13.5
PHY-3002 : Step(15): len = 309512, overlap = 11.25
PHY-3002 : Step(16): len = 301414, overlap = 11.25
PHY-3002 : Step(17): len = 296064, overlap = 11.25
PHY-3002 : Step(18): len = 288531, overlap = 11.25
PHY-3002 : Step(19): len = 281806, overlap = 13.5
PHY-3002 : Step(20): len = 274679, overlap = 13.5
PHY-3002 : Step(21): len = 270569, overlap = 13.5
PHY-3002 : Step(22): len = 261094, overlap = 13.5
PHY-3002 : Step(23): len = 255741, overlap = 13.5
PHY-3002 : Step(24): len = 250688, overlap = 13.5
PHY-3002 : Step(25): len = 245829, overlap = 13.5
PHY-3002 : Step(26): len = 236806, overlap = 20.25
PHY-3002 : Step(27): len = 232559, overlap = 20.25
PHY-3002 : Step(28): len = 228615, overlap = 20.25
PHY-3002 : Step(29): len = 222119, overlap = 20.25
PHY-3002 : Step(30): len = 209836, overlap = 20.25
PHY-3002 : Step(31): len = 207092, overlap = 20.25
PHY-3002 : Step(32): len = 201670, overlap = 20.25
PHY-3002 : Step(33): len = 165110, overlap = 20.25
PHY-3002 : Step(34): len = 157151, overlap = 20.25
PHY-3002 : Step(35): len = 156137, overlap = 20.25
PHY-3002 : Step(36): len = 129400, overlap = 15.75
PHY-3002 : Step(37): len = 118660, overlap = 18
PHY-3002 : Step(38): len = 117123, overlap = 18
PHY-3002 : Step(39): len = 112200, overlap = 15.75
PHY-3002 : Step(40): len = 108407, overlap = 13.5
PHY-3002 : Step(41): len = 106699, overlap = 13.5
PHY-3002 : Step(42): len = 104392, overlap = 15.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000119087
PHY-3002 : Step(43): len = 105078, overlap = 13.5
PHY-3002 : Step(44): len = 104156, overlap = 15.75
PHY-3002 : Step(45): len = 103297, overlap = 15.75
PHY-3002 : Step(46): len = 102943, overlap = 15.75
PHY-3002 : Step(47): len = 102121, overlap = 13.5
PHY-3002 : Step(48): len = 99004.4, overlap = 13.5
PHY-3002 : Step(49): len = 98250.4, overlap = 9
PHY-3002 : Step(50): len = 95829.2, overlap = 11.25
PHY-3002 : Step(51): len = 94373.1, overlap = 13.5
PHY-3002 : Step(52): len = 93342.3, overlap = 15.75
PHY-3002 : Step(53): len = 91686.4, overlap = 15.75
PHY-3002 : Step(54): len = 89083.9, overlap = 15.75
PHY-3002 : Step(55): len = 87301.9, overlap = 15.75
PHY-3002 : Step(56): len = 85667.9, overlap = 15.75
PHY-3002 : Step(57): len = 85042.2, overlap = 15.75
PHY-3002 : Step(58): len = 81234.5, overlap = 19.6875
PHY-3002 : Step(59): len = 80247.8, overlap = 19.8125
PHY-3002 : Step(60): len = 79089.1, overlap = 19.875
PHY-3002 : Step(61): len = 78779.1, overlap = 17.625
PHY-3002 : Step(62): len = 78513.4, overlap = 17.625
PHY-3002 : Step(63): len = 77902.6, overlap = 17.625
PHY-3002 : Step(64): len = 76811.9, overlap = 17.75
PHY-3002 : Step(65): len = 76763.4, overlap = 17.75
PHY-3002 : Step(66): len = 75231.2, overlap = 18.125
PHY-3002 : Step(67): len = 73315.7, overlap = 18.1875
PHY-3002 : Step(68): len = 73111.5, overlap = 18.1875
PHY-3002 : Step(69): len = 71957.2, overlap = 18.3125
PHY-3002 : Step(70): len = 70565.5, overlap = 14.5
PHY-3002 : Step(71): len = 68534, overlap = 19.0625
PHY-3002 : Step(72): len = 67279.1, overlap = 19.1875
PHY-3002 : Step(73): len = 66783.7, overlap = 19.125
PHY-3002 : Step(74): len = 65421.5, overlap = 19.25
PHY-3002 : Step(75): len = 64805.9, overlap = 19.25
PHY-3002 : Step(76): len = 64659.3, overlap = 19.375
PHY-3002 : Step(77): len = 64635.9, overlap = 17.0625
PHY-3002 : Step(78): len = 63878.9, overlap = 19.3125
PHY-3002 : Step(79): len = 64455.9, overlap = 17.0625
PHY-3002 : Step(80): len = 64407.1, overlap = 17.3125
PHY-3002 : Step(81): len = 63218.4, overlap = 19.5625
PHY-3002 : Step(82): len = 63204.2, overlap = 19.5625
PHY-3002 : Step(83): len = 63003, overlap = 17.3125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000238174
PHY-3002 : Step(84): len = 63075.9, overlap = 17.3125
PHY-3002 : Step(85): len = 63096.1, overlap = 17.1875
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000476347
PHY-3002 : Step(86): len = 63310.3, overlap = 17.1875
PHY-3002 : Step(87): len = 63411.6, overlap = 17.1875
PHY-3001 : Before Legalized: Len = 63411.6
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006639s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 67074.7, Over = 3.6875
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2118 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.063468s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(88): len = 67179.6, overlap = 13.4062
PHY-3002 : Step(89): len = 66156.8, overlap = 12.6562
PHY-3002 : Step(90): len = 65301.1, overlap = 12.5
PHY-3002 : Step(91): len = 64464.8, overlap = 12.5625
PHY-3002 : Step(92): len = 63753.5, overlap = 12.4375
PHY-3002 : Step(93): len = 62386.2, overlap = 13.25
PHY-3002 : Step(94): len = 60811.8, overlap = 13.8438
PHY-3002 : Step(95): len = 59784.9, overlap = 14.5625
PHY-3002 : Step(96): len = 59124.6, overlap = 15.0625
PHY-3002 : Step(97): len = 58592.1, overlap = 16.2812
PHY-3002 : Step(98): len = 57725.3, overlap = 17.7812
PHY-3002 : Step(99): len = 56451.2, overlap = 20.3438
PHY-3002 : Step(100): len = 55604.6, overlap = 21.5
PHY-3002 : Step(101): len = 54880.3, overlap = 20.7812
PHY-3002 : Step(102): len = 54350.2, overlap = 23.2812
PHY-3002 : Step(103): len = 53628.7, overlap = 22.9062
PHY-3002 : Step(104): len = 53062.8, overlap = 22.5
PHY-3002 : Step(105): len = 52743.4, overlap = 22.75
PHY-3002 : Step(106): len = 52055.4, overlap = 22.7812
PHY-3002 : Step(107): len = 51712.7, overlap = 25
PHY-3002 : Step(108): len = 51146.2, overlap = 23.2812
PHY-3002 : Step(109): len = 50763.2, overlap = 25.125
PHY-3002 : Step(110): len = 50642.9, overlap = 25.9375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000268978
PHY-3002 : Step(111): len = 50485.3, overlap = 25.375
PHY-3002 : Step(112): len = 50421.1, overlap = 24.8438
PHY-3002 : Step(113): len = 50463.2, overlap = 24.9375
PHY-3002 : Step(114): len = 50436.9, overlap = 25.375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000537956
PHY-3002 : Step(115): len = 50367.1, overlap = 25.4688
PHY-3002 : Step(116): len = 50299.6, overlap = 26.0938
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2118 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.066108s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (118.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.86799e-05
PHY-3002 : Step(117): len = 50324.8, overlap = 66
PHY-3002 : Step(118): len = 52155.5, overlap = 55.2812
PHY-3002 : Step(119): len = 53152.2, overlap = 53.7812
PHY-3002 : Step(120): len = 53188.8, overlap = 48.4375
PHY-3002 : Step(121): len = 52846.3, overlap = 47.375
PHY-3002 : Step(122): len = 52474.7, overlap = 47.4062
PHY-3002 : Step(123): len = 52496.5, overlap = 39.3125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00013736
PHY-3002 : Step(124): len = 52794, overlap = 38.6562
PHY-3002 : Step(125): len = 53103, overlap = 36.3125
PHY-3002 : Step(126): len = 53308.8, overlap = 35.6875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00027472
PHY-3002 : Step(127): len = 53256.8, overlap = 35.1875
PHY-3002 : Step(128): len = 53589.1, overlap = 32.8438
PHY-3002 : Step(129): len = 53939.5, overlap = 32.3125
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00054944
PHY-3002 : Step(130): len = 54241, overlap = 31.1875
PHY-3002 : Step(131): len = 54561.9, overlap = 31.25
PHY-3002 : Step(132): len = 54926.6, overlap = 30.5625
PHY-3002 : Step(133): len = 55253, overlap = 29.4062
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7554, tnet num: 2118, tinst num: 1574, tnode num: 10716, tedge num: 12836.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 85.22 peak overflow 2.53
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2120.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 57864, over cnt = 252(0%), over = 1006, worst = 15
PHY-1001 : End global iterations;  0.062831s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (124.3%)

PHY-1001 : Congestion index: top1 = 42.03, top5 = 25.23, top10 = 16.36, top15 = 11.67.
PHY-1001 : End incremental global routing;  0.114010s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (109.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2118 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.070510s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (110.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.213477s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (109.8%)

OPT-1001 : Current memory(MB): used = 208, reserve = 175, peak = 208.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1664/2120.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 57864, over cnt = 252(0%), over = 1006, worst = 15
PHY-1002 : len = 64088, over cnt = 178(0%), over = 455, worst = 15
PHY-1002 : len = 69512, over cnt = 60(0%), over = 102, worst = 9
PHY-1002 : len = 70448, over cnt = 17(0%), over = 17, worst = 1
PHY-1002 : len = 71000, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.091504s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (136.6%)

PHY-1001 : Congestion index: top1 = 37.31, top5 = 25.10, top10 = 18.29, top15 = 13.60.
OPT-1001 : End congestion update;  0.135189s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (115.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2118 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058661s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (106.5%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.197690s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (110.7%)

OPT-1001 : Current memory(MB): used = 212, reserve = 179, peak = 212.
OPT-1001 : End physical optimization;  0.660610s wall, 0.687500s user + 0.031250s system = 0.718750s CPU (108.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 373 LUT to BLE ...
SYN-4008 : Packed 373 LUT and 188 SEQ to BLE.
SYN-4003 : Packing 765 remaining SEQ's ...
SYN-4005 : Packed 90 SEQ with LUT/SLICE
SYN-4006 : 111 single LUT's are left
SYN-4006 : 675 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1048/1362 primitive instances ...
PHY-3001 : End packing;  0.050666s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.5%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 799 instances
RUN-1001 : 374 mslices, 374 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1940 nets
RUN-1001 : 1364 nets have 2 pins
RUN-1001 : 472 nets have [3 - 5] pins
RUN-1001 : 59 nets have [6 - 10] pins
RUN-1001 : 24 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 797 instances, 748 slices, 20 macros(199 instances: 129 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 55135.4, Over = 50
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6347, tnet num: 1938, tinst num: 797, tnode num: 8642, tedge num: 11240.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1938 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.281515s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.33568e-05
PHY-3002 : Step(134): len = 54383.5, overlap = 53
PHY-3002 : Step(135): len = 53690.2, overlap = 55
PHY-3002 : Step(136): len = 53644.2, overlap = 56.5
PHY-3002 : Step(137): len = 53756.6, overlap = 57
PHY-3002 : Step(138): len = 53675.3, overlap = 56.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.67136e-05
PHY-3002 : Step(139): len = 54038.5, overlap = 57.25
PHY-3002 : Step(140): len = 54212.2, overlap = 57.5
PHY-3002 : Step(141): len = 54813.6, overlap = 55
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000173427
PHY-3002 : Step(142): len = 55443.1, overlap = 52.25
PHY-3002 : Step(143): len = 56321.4, overlap = 47.25
PHY-3001 : Before Legalized: Len = 56321.4
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.083318s wall, 0.078125s user + 0.140625s system = 0.218750s CPU (262.5%)

PHY-3001 : After Legalized: Len = 70118.5, Over = 0
PHY-3001 : Trial Legalized: Len = 70118.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1938 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.052229s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (89.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.0017796
PHY-3002 : Step(144): len = 67100.4, overlap = 4
PHY-3002 : Step(145): len = 64977.5, overlap = 9.25
PHY-3002 : Step(146): len = 62655.8, overlap = 12
PHY-3002 : Step(147): len = 61487.8, overlap = 13.75
PHY-3002 : Step(148): len = 60802.7, overlap = 15
PHY-3002 : Step(149): len = 59984.4, overlap = 18.25
PHY-3002 : Step(150): len = 59620.5, overlap = 20
PHY-3002 : Step(151): len = 59322.7, overlap = 20.25
PHY-3002 : Step(152): len = 59155.6, overlap = 20.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00342068
PHY-3002 : Step(153): len = 59312.2, overlap = 19.75
PHY-3002 : Step(154): len = 59364.9, overlap = 20.5
PHY-3002 : Step(155): len = 59364.9, overlap = 20.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00684135
PHY-3002 : Step(156): len = 59436.5, overlap = 21
PHY-3002 : Step(157): len = 59437.9, overlap = 20.75
PHY-3001 : Before Legalized: Len = 59437.9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005240s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (298.2%)

PHY-3001 : After Legalized: Len = 63656.3, Over = 0
PHY-3001 : Legalized: Len = 63656.3, Over = 0
PHY-3001 : Spreading special nets. 6 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006433s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 10 instances has been re-located, deltaX = 0, deltaY = 12, maxDist = 2.
PHY-3001 : Final: Len = 63882.3, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6347, tnet num: 1938, tinst num: 797, tnode num: 8642, tedge num: 11240.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 53/1940.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70048, over cnt = 164(0%), over = 241, worst = 7
PHY-1002 : len = 71120, over cnt = 96(0%), over = 110, worst = 3
PHY-1002 : len = 72480, over cnt = 9(0%), over = 10, worst = 2
PHY-1002 : len = 72592, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 72608, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.135300s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (138.6%)

PHY-1001 : Congestion index: top1 = 30.93, top5 = 22.61, top10 = 17.50, top15 = 13.68.
PHY-1001 : End incremental global routing;  0.188046s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (124.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1938 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061166s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.278504s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (117.8%)

OPT-1001 : Current memory(MB): used = 215, reserve = 182, peak = 216.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1686/1940.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72608, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006913s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (226.0%)

PHY-1001 : Congestion index: top1 = 30.93, top5 = 22.61, top10 = 17.50, top15 = 13.68.
OPT-1001 : End congestion update;  0.058321s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (80.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1938 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060061s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.1%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 757 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 797 instances, 748 slices, 20 macros(199 instances: 129 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 63945.4, Over = 0
PHY-3001 : End spreading;  0.005029s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 63945.4, Over = 0
PHY-3001 : End incremental legalization;  0.034620s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (180.5%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.168369s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (111.4%)

OPT-1001 : Current memory(MB): used = 220, reserve = 188, peak = 220.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1938 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052616s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (118.8%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1682/1940.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72664, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007452s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 30.91, top5 = 22.64, top10 = 17.50, top15 = 13.69.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1938 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059543s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.482759
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.883968s wall, 0.953125s user + 0.015625s system = 0.968750s CPU (109.6%)

RUN-1003 : finish command "place" in  5.233010s wall, 6.796875s user + 3.296875s system = 10.093750s CPU (192.9%)

RUN-1004 : used memory is 196 MB, reserved memory is 163 MB, peak memory is 220 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 799 instances
RUN-1001 : 374 mslices, 374 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1940 nets
RUN-1001 : 1364 nets have 2 pins
RUN-1001 : 472 nets have [3 - 5] pins
RUN-1001 : 59 nets have [6 - 10] pins
RUN-1001 : 24 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6347, tnet num: 1938, tinst num: 797, tnode num: 8642, tedge num: 11240.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 374 mslices, 374 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1938 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69520, over cnt = 157(0%), over = 232, worst = 7
PHY-1002 : len = 70512, over cnt = 104(0%), over = 115, worst = 4
PHY-1002 : len = 71656, over cnt = 29(0%), over = 31, worst = 2
PHY-1002 : len = 72064, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.112040s wall, 0.140625s user + 0.031250s system = 0.171875s CPU (153.4%)

PHY-1001 : Congestion index: top1 = 30.95, top5 = 22.65, top10 = 17.47, top15 = 13.61.
PHY-1001 : End global routing;  0.161814s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (135.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 237, reserve = 204, peak = 237.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 496, reserve = 468, peak = 496.
PHY-1001 : End build detailed router design. 3.157637s wall, 3.093750s user + 0.046875s system = 3.140625s CPU (99.5%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 29584, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.082266s wall, 1.062500s user + 0.015625s system = 1.078125s CPU (99.6%)

PHY-1001 : Current memory(MB): used = 528, reserve = 502, peak = 528.
PHY-1001 : End phase 1; 1.088568s wall, 1.062500s user + 0.015625s system = 1.078125s CPU (99.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 30% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 179416, over cnt = 43(0%), over = 44, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 531, reserve = 503, peak = 532.
PHY-1001 : End initial routed; 1.584145s wall, 2.453125s user + 0.062500s system = 2.515625s CPU (158.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1722(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.776   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.003   |  -45.941  |  22   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.349844s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (98.3%)

PHY-1001 : Current memory(MB): used = 534, reserve = 506, peak = 534.
PHY-1001 : End phase 2; 1.934079s wall, 2.796875s user + 0.062500s system = 2.859375s CPU (147.8%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 179416, over cnt = 43(0%), over = 44, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.014683s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (106.4%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 179032, over cnt = 12(0%), over = 12, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.030798s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (101.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 179152, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.024102s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (64.8%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 179184, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.020602s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (151.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1722(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.776   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.003   |  -45.941  |  22   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.345894s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (99.4%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 8 feed throughs used by 7 nets
PHY-1001 : End commit to database; 0.172835s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (99.4%)

PHY-1001 : Current memory(MB): used = 548, reserve = 519, peak = 548.
PHY-1001 : End phase 3; 0.746554s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (100.5%)

PHY-1003 : Routed, final wirelength = 179184
PHY-1001 : Current memory(MB): used = 548, reserve = 520, peak = 548.
PHY-1001 : End export database. 0.010355s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (150.9%)

PHY-1001 : End detail routing;  7.120718s wall, 7.890625s user + 0.140625s system = 8.031250s CPU (112.8%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6347, tnet num: 1938, tinst num: 797, tnode num: 8642, tedge num: 11240.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[20] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dia[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[36] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[0] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[3] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[40] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[4] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[41] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[5] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[8] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[0] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[47] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[2] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[52] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[7] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[54] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_66.dia[2] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[7] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[7] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg1_syn_189.mi[0] slack -2859ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg1_syn_191.mi[0] slack -2763ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_68.mi[1] slack -2991ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_71.mi[0] slack -2627ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_71.mi[1] slack -2845ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_74.mi[0] slack -2845ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_74.mi[1] slack -2632ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_79.mi[0] slack -2871ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_79.mi[1] slack -2763ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_82.mi[0] slack -3003ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_82.mi[1] slack -2991ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_85.mi[0] slack -3003ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_85.mi[1] slack -2763ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_88.mi[0] slack -2871ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_88.mi[1] slack -2859ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/tx_data_dy_b[6]_syn_34.mi[0] slack -2991ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6471, tnet num: 2000, tinst num: 859, tnode num: 8766, tedge num: 11364.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/tx_data_dy_b[6]_syn_34_mi[0] slack -310ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_82_mi[1] slack -577ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_82_mi[0] slack -504ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_79_mi[1] slack -404ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_79_mi[0] slack -584ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_85_mi[1] slack -592ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_85_mi[0] slack -224ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_68_mi[1] slack -291ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_71_mi[1] slack -182ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_71_mi[0] slack -363ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_74_mi[0] slack -834ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_74_mi[1] slack -638ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg1_syn_189_mi[0] slack -629ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg1_syn_191_mi[0] slack -587ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_88_mi[0] slack -738ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_88_mi[1] slack -611ps
RUN-1001 : End hold fix;  3.062701s wall, 3.000000s user + 0.234375s system = 3.234375s CPU (105.6%)

RUN-1003 : finish command "route" in  10.678756s wall, 11.390625s user + 0.421875s system = 11.812500s CPU (110.6%)

RUN-1004 : used memory is 503 MB, reserved memory is 474 MB, peak memory is 548 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      902   out of  19600    4.60%
#reg                     1053   out of  19600    5.37%
#le                      1577
  #lut only               524   out of   1577   33.23%
  #reg only               675   out of   1577   42.80%
  #lut&reg                378   out of   1577   23.97%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         477
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         100
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    42
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1577   |703     |199     |1086    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1071   |293     |137     |864     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |29     |23      |6       |22      |0       |0       |
|    demodu                  |Demodulation                                     |451    |104     |41      |353     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |55     |31      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |6       |0       |16      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |13     |7       |0       |13      |0       |0       |
|    integ                   |Integration                                      |139    |22      |15      |111     |0       |0       |
|    modu                    |Modulation                                       |100    |48      |21      |96      |0       |1       |
|    rs422                   |Rs422Output                                      |316    |68      |46      |261     |0       |4       |
|    trans                   |SquareWaveGenerator                              |36     |28      |8       |21      |0       |0       |
|  u_uart                    |UART_Control                                     |173    |132     |7       |118     |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |25     |18      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |111    |86      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |313    |268     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1387  
    #2          2       331   
    #3          3       115   
    #4          4        26   
    #5        5-10       64   
    #6        11-50      34   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6471, tnet num: 2000, tinst num: 859, tnode num: 8766, tedge num: 11364.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2000 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 859
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2002, pip num: 14767
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 14
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1312 valid insts, and 39538 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.146220s wall, 18.265625s user + 0.078125s system = 18.343750s CPU (583.0%)

RUN-1004 : used memory is 545 MB, reserved memory is 515 MB, peak memory is 668 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250804_154213.log"
