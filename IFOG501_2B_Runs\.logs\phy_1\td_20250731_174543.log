============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Thu Jul 31 17:45:43 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1580 instances
RUN-0007 : 369 luts, 964 seqs, 126 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2112 nets
RUN-1001 : 1538 nets have 2 pins
RUN-1001 : 470 nets have [3 - 5] pins
RUN-1001 : 62 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     263     
RUN-1001 :   No   |  No   |  Yes  |     122     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1578 instances, 369 luts, 964 seqs, 196 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7530, tnet num: 2110, tinst num: 1578, tnode num: 10696, tedge num: 12764.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2110 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.263080s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (101.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 518503
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1578.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 441000, overlap = 20.25
PHY-3002 : Step(2): len = 419936, overlap = 18
PHY-3002 : Step(3): len = 405229, overlap = 18
PHY-3002 : Step(4): len = 396161, overlap = 13.5
PHY-3002 : Step(5): len = 382229, overlap = 15.75
PHY-3002 : Step(6): len = 369909, overlap = 13.5
PHY-3002 : Step(7): len = 362616, overlap = 13.5
PHY-3002 : Step(8): len = 352903, overlap = 13.5
PHY-3002 : Step(9): len = 343258, overlap = 13.5
PHY-3002 : Step(10): len = 336326, overlap = 11.25
PHY-3002 : Step(11): len = 328189, overlap = 11.25
PHY-3002 : Step(12): len = 318558, overlap = 11.25
PHY-3002 : Step(13): len = 311837, overlap = 13.5
PHY-3002 : Step(14): len = 304799, overlap = 13.5
PHY-3002 : Step(15): len = 297138, overlap = 13.5
PHY-3002 : Step(16): len = 291008, overlap = 13.5
PHY-3002 : Step(17): len = 284818, overlap = 13.5
PHY-3002 : Step(18): len = 277272, overlap = 13.5
PHY-3002 : Step(19): len = 272366, overlap = 13.5
PHY-3002 : Step(20): len = 267272, overlap = 13.5
PHY-3002 : Step(21): len = 260534, overlap = 13.5
PHY-3002 : Step(22): len = 255194, overlap = 13.5
PHY-3002 : Step(23): len = 250787, overlap = 13.5
PHY-3002 : Step(24): len = 244299, overlap = 13.5
PHY-3002 : Step(25): len = 238276, overlap = 13.5
PHY-3002 : Step(26): len = 234316, overlap = 18
PHY-3002 : Step(27): len = 229268, overlap = 20.25
PHY-3002 : Step(28): len = 220838, overlap = 20.25
PHY-3002 : Step(29): len = 216119, overlap = 20.25
PHY-3002 : Step(30): len = 213685, overlap = 20.25
PHY-3002 : Step(31): len = 202326, overlap = 20.25
PHY-3002 : Step(32): len = 188701, overlap = 20.25
PHY-3002 : Step(33): len = 186493, overlap = 20.25
PHY-3002 : Step(34): len = 181078, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000111131
PHY-3002 : Step(35): len = 182802, overlap = 15.75
PHY-3002 : Step(36): len = 180780, overlap = 15.75
PHY-3002 : Step(37): len = 179274, overlap = 20.25
PHY-3002 : Step(38): len = 176193, overlap = 11.25
PHY-3002 : Step(39): len = 168682, overlap = 9
PHY-3002 : Step(40): len = 165066, overlap = 9
PHY-3002 : Step(41): len = 162562, overlap = 11.25
PHY-3002 : Step(42): len = 158337, overlap = 11.25
PHY-3002 : Step(43): len = 154155, overlap = 9
PHY-3002 : Step(44): len = 151219, overlap = 11.25
PHY-3002 : Step(45): len = 149157, overlap = 11.25
PHY-3002 : Step(46): len = 146704, overlap = 11.25
PHY-3002 : Step(47): len = 142430, overlap = 6.75
PHY-3002 : Step(48): len = 139668, overlap = 4.5
PHY-3002 : Step(49): len = 139137, overlap = 4.5
PHY-3002 : Step(50): len = 135738, overlap = 2.25
PHY-3002 : Step(51): len = 132885, overlap = 4.5
PHY-3002 : Step(52): len = 129744, overlap = 4.5
PHY-3002 : Step(53): len = 128817, overlap = 2.25
PHY-3002 : Step(54): len = 127155, overlap = 2.25
PHY-3002 : Step(55): len = 117890, overlap = 11.25
PHY-3002 : Step(56): len = 114644, overlap = 6.75
PHY-3002 : Step(57): len = 113239, overlap = 9
PHY-3002 : Step(58): len = 109643, overlap = 9
PHY-3002 : Step(59): len = 108814, overlap = 4.5
PHY-3002 : Step(60): len = 108284, overlap = 4.5
PHY-3002 : Step(61): len = 105208, overlap = 9
PHY-3002 : Step(62): len = 102049, overlap = 6.75
PHY-3002 : Step(63): len = 99245.2, overlap = 9
PHY-3002 : Step(64): len = 98695.9, overlap = 6.75
PHY-3002 : Step(65): len = 96460.3, overlap = 4.5
PHY-3002 : Step(66): len = 94217.1, overlap = 6.75
PHY-3002 : Step(67): len = 93769.1, overlap = 6.75
PHY-3002 : Step(68): len = 91416.6, overlap = 9
PHY-3002 : Step(69): len = 90410.6, overlap = 11.25
PHY-3002 : Step(70): len = 89259.6, overlap = 9
PHY-3002 : Step(71): len = 85423.1, overlap = 4.75
PHY-3002 : Step(72): len = 82686.3, overlap = 7
PHY-3002 : Step(73): len = 81560, overlap = 6.9375
PHY-3002 : Step(74): len = 81385.9, overlap = 6.9375
PHY-3002 : Step(75): len = 81026.6, overlap = 9.5625
PHY-3002 : Step(76): len = 80161.2, overlap = 11.875
PHY-3002 : Step(77): len = 78595.1, overlap = 5.1875
PHY-3002 : Step(78): len = 77825.3, overlap = 5.1875
PHY-3002 : Step(79): len = 77064.1, overlap = 7.125
PHY-3002 : Step(80): len = 76888.5, overlap = 7.3125
PHY-3002 : Step(81): len = 75879.7, overlap = 9.5625
PHY-3002 : Step(82): len = 74674.9, overlap = 7.25
PHY-3002 : Step(83): len = 72730.3, overlap = 7
PHY-3002 : Step(84): len = 72025.9, overlap = 9.0625
PHY-3002 : Step(85): len = 70483.9, overlap = 6.8125
PHY-3002 : Step(86): len = 70044.4, overlap = 6.75
PHY-3002 : Step(87): len = 67837.9, overlap = 11.25
PHY-3002 : Step(88): len = 66495.5, overlap = 6.75
PHY-3002 : Step(89): len = 65728.9, overlap = 6.75
PHY-3002 : Step(90): len = 65134.5, overlap = 6.75
PHY-3002 : Step(91): len = 64726.1, overlap = 11.25
PHY-3002 : Step(92): len = 64287.7, overlap = 9
PHY-3002 : Step(93): len = 63814.9, overlap = 6.75
PHY-3002 : Step(94): len = 63688.4, overlap = 9
PHY-3002 : Step(95): len = 63087.5, overlap = 6.75
PHY-3002 : Step(96): len = 62273.9, overlap = 6.75
PHY-3002 : Step(97): len = 61257.6, overlap = 11.25
PHY-3002 : Step(98): len = 60085.6, overlap = 9
PHY-3002 : Step(99): len = 59543.6, overlap = 9
PHY-3002 : Step(100): len = 59371.2, overlap = 9
PHY-3002 : Step(101): len = 58968.8, overlap = 9
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000222262
PHY-3002 : Step(102): len = 59074, overlap = 9
PHY-3002 : Step(103): len = 59076.1, overlap = 9
PHY-3002 : Step(104): len = 58687.3, overlap = 9
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000444524
PHY-3002 : Step(105): len = 58974, overlap = 9
PHY-3002 : Step(106): len = 59046.5, overlap = 9
PHY-3001 : Before Legalized: Len = 59046.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007113s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (219.7%)

PHY-3001 : After Legalized: Len = 61307.3, Over = 0
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2110 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.064015s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (97.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00059917
PHY-3002 : Step(107): len = 61768.2, overlap = 7.3125
PHY-3002 : Step(108): len = 60592.6, overlap = 7.65625
PHY-3002 : Step(109): len = 60500.6, overlap = 8.15625
PHY-3002 : Step(110): len = 58619.3, overlap = 10.2812
PHY-3002 : Step(111): len = 57925, overlap = 10.9062
PHY-3002 : Step(112): len = 57232.5, overlap = 11.2812
PHY-3002 : Step(113): len = 56220.8, overlap = 12.3125
PHY-3002 : Step(114): len = 52718.5, overlap = 20.2188
PHY-3002 : Step(115): len = 51560.3, overlap = 19.7812
PHY-3002 : Step(116): len = 51010.3, overlap = 19.5938
PHY-3002 : Step(117): len = 50265.2, overlap = 20.7188
PHY-3002 : Step(118): len = 48846.4, overlap = 19.75
PHY-3002 : Step(119): len = 48792.6, overlap = 20
PHY-3002 : Step(120): len = 48416.4, overlap = 20.1562
PHY-3002 : Step(121): len = 47910.5, overlap = 19.9688
PHY-3002 : Step(122): len = 47188.6, overlap = 20.0312
PHY-3002 : Step(123): len = 46871, overlap = 19.7188
PHY-3002 : Step(124): len = 46342.7, overlap = 20.5
PHY-3002 : Step(125): len = 45983.5, overlap = 20.0312
PHY-3002 : Step(126): len = 45633.6, overlap = 20.0625
PHY-3002 : Step(127): len = 45354, overlap = 20.0625
PHY-3002 : Step(128): len = 45056.9, overlap = 20.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00119834
PHY-3002 : Step(129): len = 44875.4, overlap = 20.5625
PHY-3002 : Step(130): len = 44493.3, overlap = 20.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00239668
PHY-3002 : Step(131): len = 44483.6, overlap = 20.5625
PHY-3002 : Step(132): len = 44449.1, overlap = 20.625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2110 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.067826s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (92.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.03383e-05
PHY-3002 : Step(133): len = 44876.3, overlap = 66.25
PHY-3002 : Step(134): len = 45333.1, overlap = 65.625
PHY-3002 : Step(135): len = 45444.4, overlap = 64.9688
PHY-3002 : Step(136): len = 45312.8, overlap = 64.4688
PHY-3002 : Step(137): len = 45138.8, overlap = 65.2812
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000160677
PHY-3002 : Step(138): len = 45713.8, overlap = 55.5312
PHY-3002 : Step(139): len = 47122.9, overlap = 53.4062
PHY-3002 : Step(140): len = 47791.2, overlap = 40.1562
PHY-3002 : Step(141): len = 48219.5, overlap = 36.7812
PHY-3002 : Step(142): len = 48583.8, overlap = 36
PHY-3002 : Step(143): len = 48915.4, overlap = 34.9375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000321353
PHY-3002 : Step(144): len = 48859.3, overlap = 33.5312
PHY-3002 : Step(145): len = 48838.1, overlap = 32.625
PHY-3002 : Step(146): len = 48900.2, overlap = 32.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000642706
PHY-3002 : Step(147): len = 49446.7, overlap = 30.8438
PHY-3002 : Step(148): len = 49672, overlap = 29.3125
PHY-3002 : Step(149): len = 49879.1, overlap = 27.875
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7530, tnet num: 2110, tinst num: 1578, tnode num: 10696, tedge num: 12764.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 91.47 peak overflow 2.56
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2112.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53344, over cnt = 231(0%), over = 1101, worst = 25
PHY-1001 : End global iterations;  0.075649s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (103.3%)

PHY-1001 : Congestion index: top1 = 45.71, top5 = 25.38, top10 = 15.94, top15 = 11.43.
PHY-1001 : End incremental global routing;  0.128976s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (96.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2110 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.069911s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (111.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.229078s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (102.3%)

OPT-1001 : Current memory(MB): used = 208, reserve = 175, peak = 208.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1662/2112.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53344, over cnt = 231(0%), over = 1101, worst = 25
PHY-1002 : len = 60240, over cnt = 182(0%), over = 560, worst = 23
PHY-1002 : len = 66032, over cnt = 46(0%), over = 82, worst = 9
PHY-1002 : len = 66728, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 67192, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.098897s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (110.6%)

PHY-1001 : Congestion index: top1 = 40.28, top5 = 25.34, top10 = 18.02, top15 = 13.39.
OPT-1001 : End congestion update;  0.145663s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (107.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2110 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059648s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.8%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.208498s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (104.9%)

OPT-1001 : Current memory(MB): used = 211, reserve = 178, peak = 211.
OPT-1001 : End physical optimization;  0.691341s wall, 0.687500s user + 0.015625s system = 0.703125s CPU (101.7%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 369 LUT to BLE ...
SYN-4008 : Packed 369 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 775 remaining SEQ's ...
SYN-4005 : Packed 80 SEQ with LUT/SLICE
SYN-4006 : 113 single LUT's are left
SYN-4006 : 695 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1064/1375 primitive instances ...
PHY-3001 : End packing;  0.051459s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (121.5%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 808 instances
RUN-1001 : 378 mslices, 379 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1931 nets
RUN-1001 : 1357 nets have 2 pins
RUN-1001 : 468 nets have [3 - 5] pins
RUN-1001 : 64 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 806 instances, 757 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 50093.2, Over = 54
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6331, tnet num: 1929, tinst num: 806, tnode num: 8632, tedge num: 11186.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1929 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.288947s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (97.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.71337e-05
PHY-3002 : Step(150): len = 49457.9, overlap = 55.5
PHY-3002 : Step(151): len = 48930.3, overlap = 54
PHY-3002 : Step(152): len = 48510.7, overlap = 55
PHY-3002 : Step(153): len = 48133.3, overlap = 54.75
PHY-3002 : Step(154): len = 47937.2, overlap = 55.75
PHY-3002 : Step(155): len = 47551.7, overlap = 56.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.42674e-05
PHY-3002 : Step(156): len = 47787.5, overlap = 55
PHY-3002 : Step(157): len = 48211.7, overlap = 52.75
PHY-3002 : Step(158): len = 48760.4, overlap = 51.75
PHY-3002 : Step(159): len = 49042.8, overlap = 51.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000148535
PHY-3002 : Step(160): len = 49287.3, overlap = 52.5
PHY-3002 : Step(161): len = 49598.7, overlap = 51.5
PHY-3001 : Before Legalized: Len = 49598.7
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.088153s wall, 0.078125s user + 0.171875s system = 0.250000s CPU (283.6%)

PHY-3001 : After Legalized: Len = 62685.5, Over = 0
PHY-3001 : Trial Legalized: Len = 62685.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1929 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.049421s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000803951
PHY-3002 : Step(162): len = 59522.8, overlap = 7.5
PHY-3002 : Step(163): len = 57095.6, overlap = 13.25
PHY-3002 : Step(164): len = 55517.8, overlap = 16.25
PHY-3002 : Step(165): len = 54332.1, overlap = 18.5
PHY-3002 : Step(166): len = 53582.8, overlap = 21.25
PHY-3002 : Step(167): len = 53095.2, overlap = 22.25
PHY-3002 : Step(168): len = 52857.5, overlap = 23.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0016079
PHY-3002 : Step(169): len = 53142.2, overlap = 23
PHY-3002 : Step(170): len = 53164.7, overlap = 23.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0032158
PHY-3002 : Step(171): len = 53284.3, overlap = 22.5
PHY-3002 : Step(172): len = 53284.3, overlap = 22.5
PHY-3001 : Before Legalized: Len = 53284.3
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005661s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 58210, Over = 0
PHY-3001 : Legalized: Len = 58210, Over = 0
PHY-3001 : Spreading special nets. 11 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005767s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (270.9%)

PHY-3001 : 19 instances has been re-located, deltaX = 10, deltaY = 10, maxDist = 2.
PHY-3001 : Final: Len = 58438, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6331, tnet num: 1929, tinst num: 806, tnode num: 8632, tedge num: 11186.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 53/1931.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 64208, over cnt = 145(0%), over = 230, worst = 7
PHY-1002 : len = 65520, over cnt = 69(0%), over = 79, worst = 3
PHY-1002 : len = 66584, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 66656, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 66688, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.131865s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (118.5%)

PHY-1001 : Congestion index: top1 = 30.95, top5 = 22.41, top10 = 17.32, top15 = 13.43.
PHY-1001 : End incremental global routing;  0.183753s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (110.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1929 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.064862s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (96.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.278454s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (106.6%)

OPT-1001 : Current memory(MB): used = 213, reserve = 180, peak = 213.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1712/1931.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 66688, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006029s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (259.2%)

PHY-1001 : Congestion index: top1 = 30.95, top5 = 22.41, top10 = 17.32, top15 = 13.43.
OPT-1001 : End congestion update;  0.053307s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (117.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1929 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050901s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.1%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 766 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 806 instances, 757 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 58482.2, Over = 0
PHY-3001 : End spreading;  0.006748s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (231.5%)

PHY-3001 : Final: Len = 58482.2, Over = 0
PHY-3001 : End incremental legalization;  0.044238s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (106.0%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.167308s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (121.4%)

OPT-1001 : Current memory(MB): used = 216, reserve = 184, peak = 217.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1929 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053527s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (116.8%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1708/1931.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 66712, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 66728, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.016966s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (92.1%)

PHY-1001 : Congestion index: top1 = 30.91, top5 = 22.38, top10 = 17.29, top15 = 13.41.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1929 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050725s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.4%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.413793
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.895526s wall, 1.031250s user + 0.031250s system = 1.062500s CPU (118.6%)

RUN-1003 : finish command "place" in  5.549110s wall, 7.968750s user + 3.000000s system = 10.968750s CPU (197.7%)

RUN-1004 : used memory is 192 MB, reserved memory is 160 MB, peak memory is 217 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 808 instances
RUN-1001 : 378 mslices, 379 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1931 nets
RUN-1001 : 1357 nets have 2 pins
RUN-1001 : 468 nets have [3 - 5] pins
RUN-1001 : 64 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6331, tnet num: 1929, tinst num: 806, tnode num: 8632, tedge num: 11186.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 378 mslices, 379 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1929 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 63552, over cnt = 145(0%), over = 233, worst = 7
PHY-1002 : len = 64768, over cnt = 80(0%), over = 101, worst = 6
PHY-1002 : len = 66088, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 66200, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.127869s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (158.9%)

PHY-1001 : Congestion index: top1 = 31.27, top5 = 22.33, top10 = 17.19, top15 = 13.31.
PHY-1001 : End global routing;  0.178854s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (139.8%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 232, reserve = 200, peak = 232.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 491, reserve = 463, peak = 491.
PHY-1001 : End build detailed router design. 3.177748s wall, 3.156250s user + 0.015625s system = 3.171875s CPU (99.8%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 32648, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.101433s wall, 1.078125s user + 0.031250s system = 1.109375s CPU (100.7%)

PHY-1001 : Current memory(MB): used = 524, reserve = 496, peak = 524.
PHY-1001 : End phase 1; 1.108594s wall, 1.078125s user + 0.031250s system = 1.109375s CPU (100.1%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 29% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 176888, over cnt = 31(0%), over = 31, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 526, reserve = 497, peak = 526.
PHY-1001 : End initial routed; 1.303662s wall, 2.281250s user + 0.171875s system = 2.453125s CPU (188.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1716(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.629   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.959   |  -44.774  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.362049s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (103.6%)

PHY-1001 : Current memory(MB): used = 530, reserve = 502, peak = 530.
PHY-1001 : End phase 2; 1.665804s wall, 2.656250s user + 0.171875s system = 2.828125s CPU (169.8%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 176888, over cnt = 31(0%), over = 31, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.018798s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (83.1%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 176696, over cnt = 4(0%), over = 4, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.043702s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (107.3%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 176728, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.021339s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (73.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1716(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.629   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.959   |  -44.774  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.386175s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (101.2%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 7 feed throughs used by 6 nets
PHY-1001 : End commit to database; 0.186209s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (100.7%)

PHY-1001 : Current memory(MB): used = 545, reserve = 517, peak = 545.
PHY-1001 : End phase 3; 0.797569s wall, 0.781250s user + 0.015625s system = 0.796875s CPU (99.9%)

PHY-1003 : Routed, final wirelength = 176728
PHY-1001 : Current memory(MB): used = 546, reserve = 518, peak = 546.
PHY-1001 : End export database. 0.010298s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  6.947219s wall, 7.875000s user + 0.234375s system = 8.109375s CPU (116.7%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6331, tnet num: 1929, tinst num: 806, tnode num: 8632, tedge num: 11186.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[7] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[5] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[53] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[8] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[54] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_66.dia[2] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[55] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_66.dia[5] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[2] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/crc_data_b1[5]_syn_8.mi[0] slack -2719ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_58.mi[0] slack -2588ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_58.mi[1] slack -2959ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_61.mi[0] slack -2947ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_61.mi[1] slack -2583ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_64.mi[0] slack -2583ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_64.mi[1] slack -2827ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_67.mi[0] slack -2959ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_67.mi[1] slack -2719ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_70.mi[0] slack -2719ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_70.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_73.mi[0] slack -2827ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_73.mi[1] slack -2801ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_76.mi[0] slack -2801ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_76.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/tx_data_dy_b[0]_syn_18.mi[0] slack -2959ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6445, tnet num: 1986, tinst num: 863, tnode num: 8746, tedge num: 11300.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/tx_data_dy_b[0]_syn_18_mi[0] slack -656ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/crc_data_b1[5]_syn_8_mi[0] slack -237ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_58_mi[1] slack -244ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_61_mi[1] slack -901ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_61_mi[0] slack -829ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_58_mi[0] slack -311ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_64_mi[0] slack -106ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_67_mi[1] slack -55ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_64_mi[1] slack -409ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg2_syn_67_mi[0] slack -468ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_73_mi[1] slack -695ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_70_mi[0] slack -904ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_70_mi[1] slack -650ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_73_mi[0] slack -167ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_76_mi[0] slack -371ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_76_mi[1] slack -881ps
RUN-1001 : End hold fix;  5.130454s wall, 4.421875s user + 0.343750s system = 4.765625s CPU (92.9%)

RUN-1003 : finish command "route" in  12.590629s wall, 12.875000s user + 0.593750s system = 13.468750s CPU (107.0%)

RUN-1004 : used memory is 501 MB, reserved memory is 472 MB, peak memory is 546 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      884   out of  19600    4.51%
#reg                     1052   out of  19600    5.37%
#le                      1579
  #lut only               527   out of   1579   33.38%
  #reg only               695   out of   1579   44.02%
  #lut&reg                357   out of   1579   22.61%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                      Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0       479
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3       101
#3        wendu/clk_us                    GCLK               mslice             wendu/cnt_b[2]_syn_10.q0    43
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4       1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di             1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1579   |688     |196     |1085    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1070   |283     |133     |861     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |28     |23      |5       |21      |0       |0       |
|    demodu                  |Demodulation                                     |450    |99      |45      |352     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |55     |30      |6       |45      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |7       |0       |16      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |12     |6       |0       |12      |0       |0       |
|    integ                   |Integration                                      |141    |23      |15      |113     |0       |0       |
|    modu                    |Modulation                                       |100    |45      |14      |98      |0       |1       |
|    rs422                   |Rs422Output                                      |325    |75      |46      |257     |0       |4       |
|    trans                   |SquareWaveGenerator                              |26     |18      |8       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |181    |133     |7       |118     |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |26      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |25     |18      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |121    |89      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |306    |261     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1375  
    #2          2       333   
    #3          3       111   
    #4          4        24   
    #5        5-10       68   
    #6        11-50      32   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6445, tnet num: 1986, tinst num: 863, tnode num: 8746, tedge num: 11300.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1986 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 863
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1988, pip num: 14470
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 10
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1305 valid insts, and 38857 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  4.539460s wall, 19.125000s user + 0.125000s system = 19.250000s CPU (424.1%)

RUN-1004 : used memory is 520 MB, reserved memory is 491 MB, peak memory is 666 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250731_174543.log"
