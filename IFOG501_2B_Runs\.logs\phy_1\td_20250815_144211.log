============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Fri Aug 15 14:42:11 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1584 instances
RUN-0007 : 370 luts, 957 seqs, 136 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2132 nets
RUN-1001 : 1552 nets have 2 pins
RUN-1001 : 475 nets have [3 - 5] pins
RUN-1001 : 62 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     253     
RUN-1001 :   No   |  No   |  Yes  |     125     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1582 instances, 370 luts, 957 seqs, 206 slices, 21 macros(206 instances: 136 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7593, tnet num: 2130, tinst num: 1582, tnode num: 10766, tedge num: 12916.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2130 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.251441s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (99.4%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 548651
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1582.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 479779, overlap = 18
PHY-3002 : Step(2): len = 448670, overlap = 20.25
PHY-3002 : Step(3): len = 428601, overlap = 20.25
PHY-3002 : Step(4): len = 416419, overlap = 15.75
PHY-3002 : Step(5): len = 396258, overlap = 11.25
PHY-3002 : Step(6): len = 385647, overlap = 13.5
PHY-3002 : Step(7): len = 375443, overlap = 6.75
PHY-3002 : Step(8): len = 364364, overlap = 11.25
PHY-3002 : Step(9): len = 353709, overlap = 15.75
PHY-3002 : Step(10): len = 347753, overlap = 13.5
PHY-3002 : Step(11): len = 340001, overlap = 13.5
PHY-3002 : Step(12): len = 332338, overlap = 13.5
PHY-3002 : Step(13): len = 326379, overlap = 13.5
PHY-3002 : Step(14): len = 319098, overlap = 13.5
PHY-3002 : Step(15): len = 311585, overlap = 13.5
PHY-3002 : Step(16): len = 305510, overlap = 13.5
PHY-3002 : Step(17): len = 299036, overlap = 15.75
PHY-3002 : Step(18): len = 291328, overlap = 15.75
PHY-3002 : Step(19): len = 285363, overlap = 15.75
PHY-3002 : Step(20): len = 280477, overlap = 15.75
PHY-3002 : Step(21): len = 272309, overlap = 13.5
PHY-3002 : Step(22): len = 265741, overlap = 13.5
PHY-3002 : Step(23): len = 262049, overlap = 13.5
PHY-3002 : Step(24): len = 255147, overlap = 18
PHY-3002 : Step(25): len = 244964, overlap = 20.25
PHY-3002 : Step(26): len = 240138, overlap = 20.25
PHY-3002 : Step(27): len = 236719, overlap = 20.25
PHY-3002 : Step(28): len = 226622, overlap = 18
PHY-3002 : Step(29): len = 216442, overlap = 20.25
PHY-3002 : Step(30): len = 213309, overlap = 20.25
PHY-3002 : Step(31): len = 209146, overlap = 20.25
PHY-3002 : Step(32): len = 189873, overlap = 15.75
PHY-3002 : Step(33): len = 182337, overlap = 18
PHY-3002 : Step(34): len = 180854, overlap = 18
PHY-3002 : Step(35): len = 163661, overlap = 18
PHY-3002 : Step(36): len = 116137, overlap = 11.25
PHY-3002 : Step(37): len = 114842, overlap = 11.25
PHY-3002 : Step(38): len = 111143, overlap = 13.5
PHY-3002 : Step(39): len = 107708, overlap = 15.75
PHY-3002 : Step(40): len = 105353, overlap = 15.75
PHY-3002 : Step(41): len = 103071, overlap = 15.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00010095
PHY-3002 : Step(42): len = 103580, overlap = 15.75
PHY-3002 : Step(43): len = 102771, overlap = 15.75
PHY-3002 : Step(44): len = 101565, overlap = 18
PHY-3002 : Step(45): len = 101505, overlap = 15.75
PHY-3002 : Step(46): len = 100341, overlap = 15.75
PHY-3002 : Step(47): len = 98706, overlap = 13.5
PHY-3002 : Step(48): len = 97977.5, overlap = 13.5
PHY-3002 : Step(49): len = 96150.9, overlap = 13.5
PHY-3002 : Step(50): len = 94919.1, overlap = 11.25
PHY-3002 : Step(51): len = 92732.3, overlap = 13.5
PHY-3002 : Step(52): len = 92009.7, overlap = 13.5
PHY-3002 : Step(53): len = 90032.2, overlap = 11.25
PHY-3002 : Step(54): len = 86550.3, overlap = 13.5
PHY-3002 : Step(55): len = 83633.5, overlap = 13.5
PHY-3002 : Step(56): len = 83474.1, overlap = 13.5
PHY-3002 : Step(57): len = 82755.2, overlap = 11.25
PHY-3002 : Step(58): len = 81889.3, overlap = 13.5
PHY-3002 : Step(59): len = 81252.1, overlap = 13.5
PHY-3002 : Step(60): len = 80057.6, overlap = 13.5
PHY-3002 : Step(61): len = 78402.9, overlap = 13.9375
PHY-3002 : Step(62): len = 78300, overlap = 14.375
PHY-3002 : Step(63): len = 77099.5, overlap = 12.375
PHY-3002 : Step(64): len = 75867.2, overlap = 12.6875
PHY-3002 : Step(65): len = 75077.3, overlap = 12.5625
PHY-3002 : Step(66): len = 73414.6, overlap = 12.8125
PHY-3002 : Step(67): len = 72299.6, overlap = 15
PHY-3002 : Step(68): len = 70705.3, overlap = 17.625
PHY-3002 : Step(69): len = 68229.7, overlap = 15.1875
PHY-3002 : Step(70): len = 66444.7, overlap = 15.3125
PHY-3002 : Step(71): len = 66302.1, overlap = 15.3125
PHY-3002 : Step(72): len = 66077, overlap = 15.3125
PHY-3002 : Step(73): len = 65616.6, overlap = 15.125
PHY-3002 : Step(74): len = 65546.8, overlap = 15.1875
PHY-3002 : Step(75): len = 65236.6, overlap = 12.9375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000201899
PHY-3002 : Step(76): len = 65235.9, overlap = 12.9375
PHY-3002 : Step(77): len = 65172.2, overlap = 12.9375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000403798
PHY-3002 : Step(78): len = 65321.3, overlap = 12.8125
PHY-3002 : Step(79): len = 65395.7, overlap = 12.8125
PHY-3001 : Before Legalized: Len = 65395.7
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006545s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 67951.8, Over = 1.5625
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2130 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.059110s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(80): len = 68010.5, overlap = 5
PHY-3002 : Step(81): len = 67071.9, overlap = 3.6875
PHY-3002 : Step(82): len = 66301.8, overlap = 3.625
PHY-3002 : Step(83): len = 65556.4, overlap = 3.8125
PHY-3002 : Step(84): len = 65107.4, overlap = 3.5
PHY-3002 : Step(85): len = 63885.6, overlap = 4.0625
PHY-3002 : Step(86): len = 62628.8, overlap = 5.625
PHY-3002 : Step(87): len = 61770.4, overlap = 6.625
PHY-3002 : Step(88): len = 61221.5, overlap = 6.875
PHY-3002 : Step(89): len = 60187.5, overlap = 8.5
PHY-3002 : Step(90): len = 59145.3, overlap = 9.125
PHY-3002 : Step(91): len = 57606.6, overlap = 13.875
PHY-3002 : Step(92): len = 56809, overlap = 13.3438
PHY-3002 : Step(93): len = 56058.4, overlap = 13.3125
PHY-3002 : Step(94): len = 55789.3, overlap = 12.4688
PHY-3002 : Step(95): len = 55092.5, overlap = 12.5
PHY-3002 : Step(96): len = 54401.7, overlap = 12.875
PHY-3002 : Step(97): len = 54147, overlap = 16.5625
PHY-3002 : Step(98): len = 53426.9, overlap = 16.75
PHY-3002 : Step(99): len = 52685.3, overlap = 15.375
PHY-3002 : Step(100): len = 52195.2, overlap = 15.5625
PHY-3002 : Step(101): len = 51283.8, overlap = 16.0938
PHY-3002 : Step(102): len = 51041.8, overlap = 17.1875
PHY-3002 : Step(103): len = 50508.5, overlap = 17.6875
PHY-3002 : Step(104): len = 50113.1, overlap = 17.5625
PHY-3002 : Step(105): len = 49762.4, overlap = 18.5625
PHY-3002 : Step(106): len = 49398.7, overlap = 22.2188
PHY-3002 : Step(107): len = 49278.8, overlap = 23.7188
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000297529
PHY-3002 : Step(108): len = 49216.6, overlap = 24.3125
PHY-3002 : Step(109): len = 49062.9, overlap = 20.8125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000595057
PHY-3002 : Step(110): len = 48866.6, overlap = 20.5625
PHY-3002 : Step(111): len = 48897.6, overlap = 21
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2130 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.064665s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (96.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.18782e-05
PHY-3002 : Step(112): len = 49315.3, overlap = 62
PHY-3002 : Step(113): len = 50045.2, overlap = 55.875
PHY-3002 : Step(114): len = 50735.1, overlap = 51.1875
PHY-3002 : Step(115): len = 50597.3, overlap = 49.7812
PHY-3002 : Step(116): len = 50407.9, overlap = 49.7188
PHY-3002 : Step(117): len = 50337.5, overlap = 51.0938
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000123756
PHY-3002 : Step(118): len = 50426.2, overlap = 50.625
PHY-3002 : Step(119): len = 50893.8, overlap = 49.2188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000247513
PHY-3002 : Step(120): len = 50906.4, overlap = 47.0625
PHY-3002 : Step(121): len = 52548.9, overlap = 39.1562
PHY-3002 : Step(122): len = 52768.9, overlap = 38.9688
PHY-3002 : Step(123): len = 52837.5, overlap = 37.75
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7593, tnet num: 2130, tinst num: 1582, tnode num: 10766, tedge num: 12916.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 98.94 peak overflow 4.66
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2132.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56496, over cnt = 226(0%), over = 1105, worst = 23
PHY-1001 : End global iterations;  0.066866s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (116.8%)

PHY-1001 : Congestion index: top1 = 48.94, top5 = 26.21, top10 = 16.37, top15 = 11.66.
PHY-1001 : End incremental global routing;  0.117775s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (119.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2130 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.066970s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (93.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.213565s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (109.7%)

OPT-1001 : Current memory(MB): used = 208, reserve = 175, peak = 208.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1596/2132.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56496, over cnt = 226(0%), over = 1105, worst = 23
PHY-1002 : len = 64328, over cnt = 155(0%), over = 464, worst = 23
PHY-1002 : len = 68000, over cnt = 51(0%), over = 188, worst = 15
PHY-1002 : len = 70576, over cnt = 9(0%), over = 10, worst = 2
PHY-1002 : len = 71168, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.088721s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (140.9%)

PHY-1001 : Congestion index: top1 = 40.97, top5 = 26.23, top10 = 18.63, top15 = 13.71.
OPT-1001 : End congestion update;  0.133497s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (117.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2130 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060408s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.5%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.197269s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (110.9%)

OPT-1001 : Current memory(MB): used = 212, reserve = 179, peak = 212.
OPT-1001 : End physical optimization;  0.647535s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (108.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 370 LUT to BLE ...
SYN-4008 : Packed 370 LUT and 190 SEQ to BLE.
SYN-4003 : Packing 767 remaining SEQ's ...
SYN-4005 : Packed 95 SEQ with LUT/SLICE
SYN-4006 : 101 single LUT's are left
SYN-4006 : 672 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1042/1363 primitive instances ...
PHY-3001 : End packing;  0.045955s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (68.0%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 802 instances
RUN-1001 : 376 mslices, 375 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1950 nets
RUN-1001 : 1374 nets have 2 pins
RUN-1001 : 469 nets have [3 - 5] pins
RUN-1001 : 64 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 800 instances, 751 slices, 21 macros(206 instances: 136 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 52833.2, Over = 70.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6361, tnet num: 1948, tinst num: 800, tnode num: 8654, tedge num: 11281.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1948 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.268126s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (99.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.02912e-05
PHY-3002 : Step(124): len = 52535.9, overlap = 71
PHY-3002 : Step(125): len = 51964.6, overlap = 70.25
PHY-3002 : Step(126): len = 51956.8, overlap = 71.25
PHY-3002 : Step(127): len = 51717, overlap = 70
PHY-3002 : Step(128): len = 51005.7, overlap = 70.75
PHY-3002 : Step(129): len = 50793.9, overlap = 70.25
PHY-3002 : Step(130): len = 50758.8, overlap = 72
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.05823e-05
PHY-3002 : Step(131): len = 51126.3, overlap = 72.25
PHY-3002 : Step(132): len = 51438.4, overlap = 71
PHY-3002 : Step(133): len = 52184.6, overlap = 69.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000114813
PHY-3002 : Step(134): len = 53627, overlap = 66
PHY-3002 : Step(135): len = 54129.4, overlap = 63.25
PHY-3002 : Step(136): len = 55093.1, overlap = 56
PHY-3002 : Step(137): len = 55324.8, overlap = 55.5
PHY-3002 : Step(138): len = 55224.7, overlap = 57.25
PHY-3001 : Before Legalized: Len = 55224.7
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.069709s wall, 0.046875s user + 0.171875s system = 0.218750s CPU (313.8%)

PHY-3001 : After Legalized: Len = 70008.4, Over = 0
PHY-3001 : Trial Legalized: Len = 70008.4
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1948 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.048531s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (96.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00146581
PHY-3002 : Step(139): len = 66676.1, overlap = 5.75
PHY-3002 : Step(140): len = 64841.3, overlap = 10.5
PHY-3002 : Step(141): len = 62564.7, overlap = 13.5
PHY-3002 : Step(142): len = 61429.4, overlap = 16
PHY-3002 : Step(143): len = 61307.2, overlap = 18.25
PHY-3002 : Step(144): len = 60421.4, overlap = 21.25
PHY-3002 : Step(145): len = 59802.7, overlap = 23
PHY-3002 : Step(146): len = 59559.5, overlap = 24
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00293162
PHY-3002 : Step(147): len = 59815, overlap = 24.75
PHY-3002 : Step(148): len = 59901.1, overlap = 24.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00586323
PHY-3002 : Step(149): len = 59953.9, overlap = 24.75
PHY-3002 : Step(150): len = 59957.8, overlap = 24
PHY-3001 : Before Legalized: Len = 59957.8
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005200s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (300.5%)

PHY-3001 : After Legalized: Len = 64722.1, Over = 0
PHY-3001 : Legalized: Len = 64722.1, Over = 0
PHY-3001 : Spreading special nets. 3 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005272s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 6 instances has been re-located, deltaX = 2, deltaY = 6, maxDist = 2.
PHY-3001 : Final: Len = 64872.1, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6361, tnet num: 1948, tinst num: 800, tnode num: 8654, tedge num: 11281.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 120/1950.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72824, over cnt = 150(0%), over = 233, worst = 7
PHY-1002 : len = 73640, over cnt = 61(0%), over = 80, worst = 7
PHY-1002 : len = 74352, over cnt = 23(0%), over = 26, worst = 2
PHY-1002 : len = 74680, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.125324s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (124.7%)

PHY-1001 : Congestion index: top1 = 32.76, top5 = 23.35, top10 = 18.16, top15 = 14.14.
PHY-1001 : End incremental global routing;  0.176472s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (115.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1948 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061681s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.268147s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (110.7%)

OPT-1001 : Current memory(MB): used = 214, reserve = 182, peak = 214.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1735/1950.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 74680, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005293s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (295.2%)

PHY-1001 : Congestion index: top1 = 32.76, top5 = 23.35, top10 = 18.16, top15 = 14.14.
OPT-1001 : End congestion update;  0.051530s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (121.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1948 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046874s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.0%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 760 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 800 instances, 751 slices, 21 macros(206 instances: 136 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 64912.2, Over = 0
PHY-3001 : End spreading;  0.004718s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (331.2%)

PHY-3001 : Final: Len = 64912.2, Over = 0
PHY-3001 : End incremental legalization;  0.033089s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (94.4%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.144911s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (107.8%)

OPT-1001 : Current memory(MB): used = 218, reserve = 186, peak = 218.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1948 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047667s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.3%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1727/1950.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 74744, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.009061s wall, 0.000000s user + 0.015625s system = 0.015625s CPU (172.4%)

PHY-1001 : Congestion index: top1 = 32.67, top5 = 23.30, top10 = 18.14, top15 = 14.14.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1948 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046714s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 32.172414
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.819199s wall, 0.828125s user + 0.015625s system = 0.843750s CPU (103.0%)

RUN-1003 : finish command "place" in  4.844224s wall, 7.343750s user + 2.843750s system = 10.187500s CPU (210.3%)

RUN-1004 : used memory is 196 MB, reserved memory is 163 MB, peak memory is 218 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 802 instances
RUN-1001 : 376 mslices, 375 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1950 nets
RUN-1001 : 1374 nets have 2 pins
RUN-1001 : 469 nets have [3 - 5] pins
RUN-1001 : 64 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6361, tnet num: 1948, tinst num: 800, tnode num: 8654, tedge num: 11281.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 376 mslices, 375 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1948 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70808, over cnt = 143(0%), over = 229, worst = 7
PHY-1002 : len = 71928, over cnt = 58(0%), over = 84, worst = 7
PHY-1002 : len = 72768, over cnt = 16(0%), over = 19, worst = 2
PHY-1002 : len = 73056, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.123019s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (114.3%)

PHY-1001 : Congestion index: top1 = 32.09, top5 = 22.90, top10 = 17.83, top15 = 13.85.
PHY-1001 : End global routing;  0.174449s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (107.5%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 232, reserve = 201, peak = 232.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 492, reserve = 465, peak = 492.
PHY-1001 : End build detailed router design. 3.128700s wall, 3.031250s user + 0.109375s system = 3.140625s CPU (100.4%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 31024, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.057178s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (97.5%)

PHY-1001 : Current memory(MB): used = 524, reserve = 498, peak = 524.
PHY-1001 : End phase 1; 1.063311s wall, 1.046875s user + 0.000000s system = 1.046875s CPU (98.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 37% nets.
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 178632, over cnt = 47(0%), over = 47, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 527, reserve = 499, peak = 528.
PHY-1001 : End initial routed; 1.470166s wall, 2.265625s user + 0.203125s system = 2.468750s CPU (167.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1726(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.003   |  -46.632  |  21   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.329515s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (99.6%)

PHY-1001 : Current memory(MB): used = 528, reserve = 500, peak = 528.
PHY-1001 : End phase 2; 1.799768s wall, 2.593750s user + 0.203125s system = 2.796875s CPU (155.4%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 178632, over cnt = 47(0%), over = 47, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014708s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (106.2%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 178512, over cnt = 9(0%), over = 9, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.032036s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (97.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 178600, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.030145s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (103.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1726(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.003   |  -46.632  |  21   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.345210s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (99.6%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 5 feed throughs used by 5 nets
PHY-1001 : End commit to database; 0.167120s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (102.8%)

PHY-1001 : Current memory(MB): used = 542, reserve = 514, peak = 542.
PHY-1001 : End phase 3; 0.721810s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (99.6%)

PHY-1003 : Routed, final wirelength = 178600
PHY-1001 : Current memory(MB): used = 543, reserve = 514, peak = 543.
PHY-1001 : End export database. 0.011428s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (136.7%)

PHY-1001 : End detail routing;  6.896019s wall, 7.562500s user + 0.312500s system = 7.875000s CPU (114.2%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6361, tnet num: 1948, tinst num: 800, tnode num: 8654, tedge num: 11281.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[31] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dib[4] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[35] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dib[8] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[8] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[54] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_66.dia[2] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/asy_w_rst1 endpin signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_18.sr slack -37ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_68.mi[0] slack -2991ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_68.mi[1] slack -2991ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_71.mi[0] slack -3003ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_71.mi[1] slack -2991ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_74.mi[0] slack -2871ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_74.mi[1] slack -2991ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_77.mi[0] slack -2991ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_77.mi[1] slack -2763ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_80.mi[0] slack -3003ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_80.mi[1] slack -2763ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_83.mi[0] slack -2871ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_83.mi[1] slack -2627ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_86.mi[0] slack -2845ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_86.mi[1] slack -3003ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_89.mi[0] slack -2871ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_89.mi[1] slack -2871ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6473, tnet num: 2004, tinst num: 856, tnode num: 8766, tedge num: 11393.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_86_mi[0] slack -741ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_86_mi[1] slack -698ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_83_mi[1] slack -799ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_83_mi[0] slack -925ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_89_mi[0] slack -457ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_89_mi[1] slack -427ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_80_mi[0] slack -283ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_80_mi[1] slack -545ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg2_syn_71_mi[0] slack -635ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_71_mi[1] slack -1059ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_68_mi[0] slack -572ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_77_mi[1] slack -720ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_74_mi[1] slack -811ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_77_mi[0] slack -373ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_74_mi[0] slack -415ps
RUN-1001 : End hold fix;  3.024662s wall, 3.031250s user + 0.218750s system = 3.250000s CPU (107.5%)

RUN-1003 : finish command "route" in  10.406209s wall, 11.078125s user + 0.546875s system = 11.625000s CPU (111.7%)

RUN-1004 : used memory is 513 MB, reserved memory is 486 MB, peak memory is 543 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   15
  #output                  21
  #inout                    1

Utilization Statistics
#lut                      899   out of  19600    4.59%
#reg                     1059   out of  19600    5.40%
#le                      1571
  #lut only               512   out of   1571   32.59%
  #reg only               672   out of   1571   42.78%
  #lut&reg                387   out of   1571   24.63%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         477
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         101
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    39
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
  RxTransmit       INPUT        M14        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1571   |693     |206     |1092    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1072   |291     |142     |870     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |21     |17      |4       |18      |0       |0       |
|    demodu                  |Demodulation                                     |448    |93      |46      |352     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |57     |30      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |5       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |14     |7       |0       |14      |0       |0       |
|    integ                   |Integration                                      |141    |24      |15      |113     |0       |0       |
|    modu                    |Modulation                                       |104    |52      |23      |100     |0       |1       |
|    rs422                   |Rs422Output                                      |322    |77      |46      |267     |0       |4       |
|    trans                   |SquareWaveGenerator                              |36     |28      |8       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |170    |130     |7       |117     |0       |0       |
|    U0                      |speed_select_Tx                                  |32     |25      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |28     |21      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |110    |84      |0       |85      |0       |0       |
|  wendu                     |DS18B20                                          |305    |260     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1391  
    #2          2       337   
    #3          3       106   
    #4          4        26   
    #5        5-10       68   
    #6        11-50      33   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6473, tnet num: 2004, tinst num: 856, tnode num: 8766, tedge num: 11393.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2004 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 856
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2006, pip num: 14617
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 9
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1351 valid insts, and 39221 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.038085s wall, 17.750000s user + 0.109375s system = 17.859375s CPU (587.8%)

RUN-1004 : used memory is 545 MB, reserved memory is 515 MB, peak memory is 667 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250815_144211.log"
