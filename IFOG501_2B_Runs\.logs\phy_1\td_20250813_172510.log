============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Wed Aug 13 17:25:10 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1572 instances
RUN-0007 : 369 luts, 951 seqs, 131 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2120 nets
RUN-1001 : 1546 nets have 2 pins
RUN-1001 : 471 nets have [3 - 5] pins
RUN-1001 : 60 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     125     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1570 instances, 369 luts, 951 seqs, 201 slices, 20 macros(201 instances: 131 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7550, tnet num: 2118, tinst num: 1570, tnode num: 10712, tedge num: 12854.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2118 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.268374s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (99.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 531259
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1570.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 457376, overlap = 20.25
PHY-3002 : Step(2): len = 433558, overlap = 18
PHY-3002 : Step(3): len = 419091, overlap = 20.25
PHY-3002 : Step(4): len = 410042, overlap = 13.5
PHY-3002 : Step(5): len = 394323, overlap = 15.75
PHY-3002 : Step(6): len = 382491, overlap = 13.5
PHY-3002 : Step(7): len = 374351, overlap = 13.5
PHY-3002 : Step(8): len = 366521, overlap = 15.75
PHY-3002 : Step(9): len = 352181, overlap = 15.75
PHY-3002 : Step(10): len = 346617, overlap = 13.5
PHY-3002 : Step(11): len = 338709, overlap = 13.5
PHY-3002 : Step(12): len = 331590, overlap = 13.5
PHY-3002 : Step(13): len = 323803, overlap = 13.5
PHY-3002 : Step(14): len = 318696, overlap = 13.5
PHY-3002 : Step(15): len = 311433, overlap = 15.75
PHY-3002 : Step(16): len = 305966, overlap = 15.75
PHY-3002 : Step(17): len = 298722, overlap = 15.75
PHY-3002 : Step(18): len = 293840, overlap = 15.75
PHY-3002 : Step(19): len = 286963, overlap = 15.75
PHY-3002 : Step(20): len = 281645, overlap = 15.75
PHY-3002 : Step(21): len = 276046, overlap = 13.5
PHY-3002 : Step(22): len = 271309, overlap = 13.5
PHY-3002 : Step(23): len = 260882, overlap = 13.5
PHY-3002 : Step(24): len = 257023, overlap = 15.75
PHY-3002 : Step(25): len = 251060, overlap = 20.25
PHY-3002 : Step(26): len = 246636, overlap = 20.25
PHY-3002 : Step(27): len = 240273, overlap = 20.25
PHY-3002 : Step(28): len = 235821, overlap = 20.25
PHY-3002 : Step(29): len = 230706, overlap = 20.25
PHY-3002 : Step(30): len = 226169, overlap = 20.25
PHY-3002 : Step(31): len = 216384, overlap = 20.25
PHY-3002 : Step(32): len = 213004, overlap = 20.25
PHY-3002 : Step(33): len = 208205, overlap = 20.25
PHY-3002 : Step(34): len = 201014, overlap = 20.25
PHY-3002 : Step(35): len = 193267, overlap = 20.25
PHY-3002 : Step(36): len = 191388, overlap = 20.25
PHY-3002 : Step(37): len = 182940, overlap = 20.25
PHY-3002 : Step(38): len = 159262, overlap = 20.25
PHY-3002 : Step(39): len = 153870, overlap = 20.25
PHY-3002 : Step(40): len = 152376, overlap = 20.25
PHY-3002 : Step(41): len = 130290, overlap = 18
PHY-3002 : Step(42): len = 121406, overlap = 15.75
PHY-3002 : Step(43): len = 120940, overlap = 20.25
PHY-3002 : Step(44): len = 118336, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.87775e-05
PHY-3002 : Step(45): len = 118316, overlap = 13.5
PHY-3002 : Step(46): len = 117596, overlap = 13.5
PHY-3002 : Step(47): len = 116946, overlap = 13.5
PHY-3002 : Step(48): len = 116255, overlap = 11.25
PHY-3002 : Step(49): len = 114277, overlap = 13.5
PHY-3002 : Step(50): len = 109795, overlap = 13.5
PHY-3002 : Step(51): len = 107593, overlap = 11.25
PHY-3002 : Step(52): len = 105851, overlap = 11.25
PHY-3002 : Step(53): len = 104217, overlap = 13.5
PHY-3002 : Step(54): len = 101508, overlap = 15.75
PHY-3002 : Step(55): len = 99779.4, overlap = 15.75
PHY-3002 : Step(56): len = 98908.3, overlap = 15.75
PHY-3002 : Step(57): len = 97453.3, overlap = 15.75
PHY-3002 : Step(58): len = 94420.4, overlap = 13.5
PHY-3002 : Step(59): len = 92859.2, overlap = 13.5
PHY-3002 : Step(60): len = 92133.9, overlap = 15.75
PHY-3002 : Step(61): len = 88520.6, overlap = 15.75
PHY-3002 : Step(62): len = 87654.9, overlap = 18
PHY-3002 : Step(63): len = 86848, overlap = 18
PHY-3002 : Step(64): len = 85195.7, overlap = 15.75
PHY-3002 : Step(65): len = 84629.3, overlap = 15.75
PHY-3002 : Step(66): len = 83150.9, overlap = 15.75
PHY-3002 : Step(67): len = 79510.3, overlap = 14.8125
PHY-3002 : Step(68): len = 77537.5, overlap = 17.3125
PHY-3002 : Step(69): len = 77136.5, overlap = 17.0625
PHY-3002 : Step(70): len = 75989.6, overlap = 19.5625
PHY-3002 : Step(71): len = 75643.6, overlap = 19.625
PHY-3002 : Step(72): len = 75072.8, overlap = 19.5625
PHY-3002 : Step(73): len = 73014.4, overlap = 17.375
PHY-3002 : Step(74): len = 71932.3, overlap = 17.4375
PHY-3002 : Step(75): len = 70874.2, overlap = 17.75
PHY-3002 : Step(76): len = 70113.3, overlap = 17.6875
PHY-3002 : Step(77): len = 69161.7, overlap = 18.9375
PHY-3002 : Step(78): len = 68190.4, overlap = 19.25
PHY-3002 : Step(79): len = 66771.7, overlap = 19.0625
PHY-3002 : Step(80): len = 66053.9, overlap = 18.875
PHY-3002 : Step(81): len = 65346, overlap = 18.875
PHY-3002 : Step(82): len = 64173.6, overlap = 16.6875
PHY-3002 : Step(83): len = 63735.8, overlap = 16.5625
PHY-3002 : Step(84): len = 63227.4, overlap = 16.375
PHY-3002 : Step(85): len = 62725.8, overlap = 16.3125
PHY-3002 : Step(86): len = 62626.9, overlap = 18.4375
PHY-3002 : Step(87): len = 62084.7, overlap = 20.5625
PHY-3002 : Step(88): len = 61542.4, overlap = 15.875
PHY-3002 : Step(89): len = 61407.7, overlap = 13.625
PHY-3002 : Step(90): len = 59958.5, overlap = 12.8125
PHY-3002 : Step(91): len = 59731.4, overlap = 14.8125
PHY-3002 : Step(92): len = 59234.7, overlap = 12.5625
PHY-3002 : Step(93): len = 58961.8, overlap = 14.8125
PHY-3002 : Step(94): len = 58980.2, overlap = 14.875
PHY-3002 : Step(95): len = 58557.1, overlap = 14.875
PHY-3002 : Step(96): len = 58279.6, overlap = 12.625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000197555
PHY-3002 : Step(97): len = 58457.8, overlap = 12.625
PHY-3002 : Step(98): len = 58334.5, overlap = 12.625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00039511
PHY-3002 : Step(99): len = 58579.7, overlap = 12.625
PHY-3002 : Step(100): len = 58648.7, overlap = 12.625
PHY-3001 : Before Legalized: Len = 58648.7
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006885s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 62039.3, Over = 1.375
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2118 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.076746s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (101.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00258129
PHY-3002 : Step(101): len = 62051.3, overlap = 12.125
PHY-3002 : Step(102): len = 62186.7, overlap = 11.9375
PHY-3002 : Step(103): len = 61468.7, overlap = 11.625
PHY-3002 : Step(104): len = 61519.2, overlap = 11.625
PHY-3002 : Step(105): len = 61362.4, overlap = 13.875
PHY-3002 : Step(106): len = 60299.8, overlap = 14.25
PHY-3002 : Step(107): len = 59717.2, overlap = 12.9375
PHY-3002 : Step(108): len = 59109.2, overlap = 9.875
PHY-3002 : Step(109): len = 59173.2, overlap = 11
PHY-3002 : Step(110): len = 58595.6, overlap = 11.0625
PHY-3002 : Step(111): len = 57756.6, overlap = 10.5625
PHY-3002 : Step(112): len = 56879.3, overlap = 10.8125
PHY-3002 : Step(113): len = 56560.4, overlap = 11.6562
PHY-3002 : Step(114): len = 56225.6, overlap = 12.375
PHY-3002 : Step(115): len = 55476, overlap = 13.9062
PHY-3002 : Step(116): len = 55202.9, overlap = 13.8438
PHY-3002 : Step(117): len = 54914.9, overlap = 14.875
PHY-3002 : Step(118): len = 54054.5, overlap = 15.875
PHY-3002 : Step(119): len = 53967.8, overlap = 15.875
PHY-3002 : Step(120): len = 53775.5, overlap = 16.2188
PHY-3002 : Step(121): len = 53572.1, overlap = 18.2812
PHY-3002 : Step(122): len = 53501.3, overlap = 18.7812
PHY-3002 : Step(123): len = 53188.8, overlap = 15.7188
PHY-3002 : Step(124): len = 52903.3, overlap = 13.625
PHY-3002 : Step(125): len = 52850.5, overlap = 14
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00516258
PHY-3002 : Step(126): len = 52616.7, overlap = 13.7188
PHY-3002 : Step(127): len = 52272.2, overlap = 13.5938
PHY-3002 : Step(128): len = 52325.1, overlap = 13.5312
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2118 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.076475s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (102.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000114004
PHY-3002 : Step(129): len = 53327.3, overlap = 49.3125
PHY-3002 : Step(130): len = 54541.1, overlap = 52.75
PHY-3002 : Step(131): len = 54499.3, overlap = 52.375
PHY-3002 : Step(132): len = 53788.6, overlap = 52.0625
PHY-3002 : Step(133): len = 53055.1, overlap = 52.0625
PHY-3002 : Step(134): len = 52987.8, overlap = 51.9062
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000228008
PHY-3002 : Step(135): len = 53482.7, overlap = 50.25
PHY-3002 : Step(136): len = 54084.2, overlap = 44.9688
PHY-3002 : Step(137): len = 54715.7, overlap = 43.5312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000456015
PHY-3002 : Step(138): len = 54533.2, overlap = 38.4375
PHY-3002 : Step(139): len = 54695.7, overlap = 40.4688
PHY-3002 : Step(140): len = 54996.4, overlap = 35.7812
PHY-3002 : Step(141): len = 54968.8, overlap = 35.0625
PHY-3002 : Step(142): len = 55288.8, overlap = 33.5312
PHY-3002 : Step(143): len = 55382.5, overlap = 33.5625
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7550, tnet num: 2118, tinst num: 1570, tnode num: 10712, tedge num: 12854.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 91.28 peak overflow 4.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2120.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58288, over cnt = 251(0%), over = 1066, worst = 20
PHY-1001 : End global iterations;  0.093505s wall, 0.093750s user + 0.046875s system = 0.140625s CPU (150.4%)

PHY-1001 : Congestion index: top1 = 45.75, top5 = 25.86, top10 = 16.67, top15 = 11.92.
PHY-1001 : End incremental global routing;  0.169631s wall, 0.171875s user + 0.046875s system = 0.218750s CPU (129.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2118 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.077592s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (100.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.280514s wall, 0.281250s user + 0.046875s system = 0.328125s CPU (117.0%)

OPT-1001 : Current memory(MB): used = 208, reserve = 175, peak = 208.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1652/2120.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58288, over cnt = 251(0%), over = 1066, worst = 20
PHY-1002 : len = 65496, over cnt = 204(0%), over = 530, worst = 20
PHY-1002 : len = 70936, over cnt = 60(0%), over = 106, worst = 7
PHY-1002 : len = 72240, over cnt = 12(0%), over = 12, worst = 1
PHY-1002 : len = 73024, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.117998s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (119.2%)

PHY-1001 : Congestion index: top1 = 39.16, top5 = 25.28, top10 = 18.63, top15 = 13.84.
OPT-1001 : End congestion update;  0.169009s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (120.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2118 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058977s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (79.5%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.231278s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (114.9%)

OPT-1001 : Current memory(MB): used = 211, reserve = 178, peak = 211.
OPT-1001 : End physical optimization;  0.813942s wall, 0.828125s user + 0.062500s system = 0.890625s CPU (109.4%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 369 LUT to BLE ...
SYN-4008 : Packed 369 LUT and 190 SEQ to BLE.
SYN-4003 : Packing 761 remaining SEQ's ...
SYN-4005 : Packed 94 SEQ with LUT/SLICE
SYN-4006 : 101 single LUT's are left
SYN-4006 : 667 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1036/1352 primitive instances ...
PHY-3001 : End packing;  0.054282s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (115.1%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 794 instances
RUN-1001 : 371 mslices, 372 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1938 nets
RUN-1001 : 1369 nets have 2 pins
RUN-1001 : 466 nets have [3 - 5] pins
RUN-1001 : 59 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 792 instances, 743 slices, 20 macros(201 instances: 131 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 55789.6, Over = 57
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6329, tnet num: 1936, tinst num: 792, tnode num: 8614, tedge num: 11236.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1936 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.297402s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.27419e-05
PHY-3002 : Step(144): len = 54631.2, overlap = 59
PHY-3002 : Step(145): len = 54078.6, overlap = 61.5
PHY-3002 : Step(146): len = 53811.9, overlap = 64.5
PHY-3002 : Step(147): len = 54093.6, overlap = 66.25
PHY-3002 : Step(148): len = 54298.7, overlap = 65.5
PHY-3002 : Step(149): len = 53691.7, overlap = 65.75
PHY-3002 : Step(150): len = 53335.7, overlap = 66.25
PHY-3002 : Step(151): len = 53197.2, overlap = 66
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.54837e-05
PHY-3002 : Step(152): len = 53709.9, overlap = 66
PHY-3002 : Step(153): len = 54255, overlap = 65.5
PHY-3002 : Step(154): len = 54583.1, overlap = 63
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000170967
PHY-3002 : Step(155): len = 55190.6, overlap = 59.25
PHY-3002 : Step(156): len = 55927.9, overlap = 55
PHY-3002 : Step(157): len = 56676.8, overlap = 52.5
PHY-3002 : Step(158): len = 56889.9, overlap = 53.5
PHY-3001 : Before Legalized: Len = 56889.9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.071612s wall, 0.015625s user + 0.093750s system = 0.109375s CPU (152.7%)

PHY-3001 : After Legalized: Len = 69561.2, Over = 0
PHY-3001 : Trial Legalized: Len = 69561.2
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1936 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.048038s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00109486
PHY-3002 : Step(159): len = 65674.6, overlap = 9.25
PHY-3002 : Step(160): len = 63756.8, overlap = 14.25
PHY-3002 : Step(161): len = 61361.5, overlap = 19.75
PHY-3002 : Step(162): len = 60423.7, overlap = 23.75
PHY-3002 : Step(163): len = 60058, overlap = 25.5
PHY-3002 : Step(164): len = 59617, overlap = 28
PHY-3002 : Step(165): len = 59242.2, overlap = 28.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00218972
PHY-3002 : Step(166): len = 59487.4, overlap = 27.75
PHY-3002 : Step(167): len = 59566, overlap = 26.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00437944
PHY-3002 : Step(168): len = 59712.7, overlap = 27.25
PHY-3002 : Step(169): len = 59725.6, overlap = 27.5
PHY-3001 : Before Legalized: Len = 59725.6
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005539s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 64007.7, Over = 0
PHY-3001 : Legalized: Len = 64007.7, Over = 0
PHY-3001 : Spreading special nets. 4 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006575s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 4 instances has been re-located, deltaX = 1, deltaY = 3, maxDist = 1.
PHY-3001 : Final: Len = 64055.7, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6329, tnet num: 1936, tinst num: 792, tnode num: 8614, tedge num: 11236.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 112/1938.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70984, over cnt = 156(0%), over = 234, worst = 6
PHY-1002 : len = 71520, over cnt = 105(0%), over = 151, worst = 6
PHY-1002 : len = 73456, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 73488, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 73504, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.113066s wall, 0.187500s user + 0.062500s system = 0.250000s CPU (221.1%)

PHY-1001 : Congestion index: top1 = 31.77, top5 = 22.74, top10 = 17.78, top15 = 13.93.
PHY-1001 : End incremental global routing;  0.170224s wall, 0.234375s user + 0.062500s system = 0.296875s CPU (174.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1936 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058037s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (107.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.257097s wall, 0.312500s user + 0.062500s system = 0.375000s CPU (145.9%)

OPT-1001 : Current memory(MB): used = 212, reserve = 180, peak = 213.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1697/1938.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73504, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005206s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (300.1%)

PHY-1001 : Congestion index: top1 = 31.77, top5 = 22.74, top10 = 17.78, top15 = 13.93.
OPT-1001 : End congestion update;  0.052235s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (119.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1936 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057138s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (82.0%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.111395s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (112.2%)

OPT-1001 : Current memory(MB): used = 215, reserve = 182, peak = 215.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1936 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058894s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (79.6%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1697/1938.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73504, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005190s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (301.1%)

PHY-1001 : Congestion index: top1 = 31.77, top5 = 22.74, top10 = 17.78, top15 = 13.93.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1936 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057743s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (81.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.275862
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.856002s wall, 0.906250s user + 0.078125s system = 0.984375s CPU (115.0%)

RUN-1003 : finish command "place" in  5.662428s wall, 7.531250s user + 3.250000s system = 10.781250s CPU (190.4%)

RUN-1004 : used memory is 192 MB, reserved memory is 159 MB, peak memory is 216 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 794 instances
RUN-1001 : 371 mslices, 372 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1938 nets
RUN-1001 : 1369 nets have 2 pins
RUN-1001 : 466 nets have [3 - 5] pins
RUN-1001 : 59 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6329, tnet num: 1936, tinst num: 792, tnode num: 8614, tedge num: 11236.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 371 mslices, 372 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1936 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69200, over cnt = 153(0%), over = 229, worst = 6
PHY-1002 : len = 69840, over cnt = 101(0%), over = 142, worst = 6
PHY-1002 : len = 71744, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 71840, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.114703s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (149.8%)

PHY-1001 : Congestion index: top1 = 32.18, top5 = 22.49, top10 = 17.59, top15 = 13.71.
PHY-1001 : End global routing;  0.165659s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (132.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 232, reserve = 200, peak = 232.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 492, reserve = 464, peak = 492.
PHY-1001 : End build detailed router design. 3.399397s wall, 3.328125s user + 0.046875s system = 3.375000s CPU (99.3%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 30896, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.056095s wall, 1.031250s user + 0.015625s system = 1.046875s CPU (99.1%)

PHY-1001 : Current memory(MB): used = 524, reserve = 497, peak = 524.
PHY-1001 : End phase 1; 1.062502s wall, 1.046875s user + 0.015625s system = 1.062500s CPU (100.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 30% nets.
PHY-1001 : Routed 37% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 178112, over cnt = 29(0%), over = 29, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 526, reserve = 497, peak = 527.
PHY-1001 : End initial routed; 1.523628s wall, 2.390625s user + 0.140625s system = 2.531250s CPU (166.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1718(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.654   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.870   |  -43.653  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.340614s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (100.9%)

PHY-1001 : Current memory(MB): used = 529, reserve = 500, peak = 529.
PHY-1001 : End phase 2; 1.864336s wall, 2.734375s user + 0.140625s system = 2.875000s CPU (154.2%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 178112, over cnt = 29(0%), over = 29, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.013828s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (113.0%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 177904, over cnt = 4(0%), over = 4, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.032896s wall, 0.015625s user + 0.015625s system = 0.031250s CPU (95.0%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 177928, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.021213s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (73.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1718(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.654   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.870   |  -43.653  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.333106s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (103.2%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 6 feed throughs used by 6 nets
PHY-1001 : End commit to database; 0.170625s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (100.7%)

PHY-1001 : Current memory(MB): used = 544, reserve = 515, peak = 544.
PHY-1001 : End phase 3; 0.696561s wall, 0.687500s user + 0.015625s system = 0.703125s CPU (100.9%)

PHY-1003 : Routed, final wirelength = 177928
PHY-1001 : Current memory(MB): used = 544, reserve = 516, peak = 544.
PHY-1001 : End export database. 0.010182s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  7.207227s wall, 7.968750s user + 0.234375s system = 8.203125s CPU (113.8%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6329, tnet num: 1936, tinst num: 792, tnode num: 8614, tedge num: 11236.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[8] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[1] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[4] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[4] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_56.mi[0] slack -2738ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_56.mi[1] slack -2858ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_59.mi[0] slack -2858ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_59.mi[1] slack -2870ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_62.mi[0] slack -2712ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_62.mi[1] slack -2738ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_65.mi[0] slack -2494ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_65.mi[1] slack -2724ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_68.mi[0] slack -2858ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_68.mi[1] slack -2499ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_71.mi[0] slack -2712ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_71.mi[1] slack -2494ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_74.mi[0] slack -2738ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_74.mi[1] slack -2630ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_77.mi[0] slack -2726ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_77.mi[1] slack -2870ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6437, tnet num: 1990, tinst num: 846, tnode num: 8722, tedge num: 11344.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_59_mi[1] slack -461ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_59_mi[0] slack -604ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_65_mi[1] slack -138ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_65_mi[0] slack -675ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_62_mi[0] slack -638ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_62_mi[1] slack -417ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_68_mi[1] slack -458ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_71_mi[0] slack -775ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_71_mi[1] slack -659ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_74_mi[0] slack -559ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_77_mi[1] slack -603ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_77_mi[0] slack -531ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_56_mi[0] slack -675ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_56_mi[1] slack -269ps
RUN-1001 : End hold fix;  3.032566s wall, 2.984375s user + 0.250000s system = 3.234375s CPU (106.7%)

RUN-1003 : finish command "route" in  10.736140s wall, 11.484375s user + 0.500000s system = 11.984375s CPU (111.6%)

RUN-1004 : used memory is 498 MB, reserved memory is 468 MB, peak memory is 544 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      886   out of  19600    4.52%
#reg                     1053   out of  19600    5.37%
#le                      1553
  #lut only               500   out of   1553   32.20%
  #reg only               667   out of   1553   42.95%
  #lut&reg                386   out of   1553   24.86%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                      Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0       478
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3       99
#3        wendu/clk_us                    GCLK               lslice             wendu/cnt_b[2]_syn_13.q0    39
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4       1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di             1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1553   |685     |201     |1086    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1046   |278     |137     |862     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |22     |18      |4       |19      |0       |0       |
|    demodu                  |Demodulation                                     |436    |92      |41      |353     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |56     |36      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |12      |0       |16      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |13     |7       |0       |13      |0       |0       |
|    integ                   |Integration                                      |139    |18      |15      |111     |0       |0       |
|    modu                    |Modulation                                       |99     |50      |23      |95      |0       |1       |
|    rs422                   |Rs422Output                                      |316    |74      |46      |261     |0       |4       |
|    trans                   |SquareWaveGenerator                              |34     |26      |8       |23      |0       |0       |
|  u_uart                    |UART_Control                                     |180    |137     |7       |115     |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |27      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |24     |24      |0       |13      |0       |0       |
|    U2                      |Ctrl_Data                                        |120    |86      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |303    |258     |45      |75      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1384  
    #2          2       328   
    #3          3       110   
    #4          4        28   
    #5        5-10       64   
    #6        11-50      33   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6437, tnet num: 1990, tinst num: 846, tnode num: 8722, tedge num: 11344.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1990 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 846
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1992, pip num: 14657
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 11
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1276 valid insts, and 39267 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.023725s wall, 17.437500s user + 0.093750s system = 17.531250s CPU (579.8%)

RUN-1004 : used memory is 517 MB, reserved memory is 490 MB, peak memory is 663 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250813_172510.log"
