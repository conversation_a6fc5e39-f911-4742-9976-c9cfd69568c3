============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Tue Aug  5 10:06:06 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1583 instances
RUN-0007 : 375 luts, 965 seqs, 122 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2115 nets
RUN-1001 : 1536 nets have 2 pins
RUN-1001 : 479 nets have [3 - 5] pins
RUN-1001 : 58 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     263     
RUN-1001 :   No   |  No   |  Yes  |     123     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1581 instances, 375 luts, 965 seqs, 192 slices, 19 macros(192 instances: 122 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7535, tnet num: 2113, tinst num: 1581, tnode num: 10701, tedge num: 12768.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2113 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.254498s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (104.4%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 525559
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1581.
PHY-3001 : End clustering;  0.000023s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 454138, overlap = 20.25
PHY-3002 : Step(2): len = 427455, overlap = 15.75
PHY-3002 : Step(3): len = 412638, overlap = 20.25
PHY-3002 : Step(4): len = 401412, overlap = 13.5
PHY-3002 : Step(5): len = 387204, overlap = 18
PHY-3002 : Step(6): len = 373353, overlap = 13.5
PHY-3002 : Step(7): len = 363744, overlap = 15.75
PHY-3002 : Step(8): len = 357722, overlap = 15.75
PHY-3002 : Step(9): len = 342390, overlap = 13.5
PHY-3002 : Step(10): len = 335508, overlap = 13.5
PHY-3002 : Step(11): len = 328747, overlap = 11.25
PHY-3002 : Step(12): len = 322086, overlap = 13.5
PHY-3002 : Step(13): len = 310844, overlap = 13.5
PHY-3002 : Step(14): len = 306269, overlap = 13.5
PHY-3002 : Step(15): len = 298616, overlap = 15.75
PHY-3002 : Step(16): len = 293043, overlap = 15.75
PHY-3002 : Step(17): len = 285754, overlap = 15.75
PHY-3002 : Step(18): len = 281028, overlap = 15.75
PHY-3002 : Step(19): len = 274288, overlap = 15.75
PHY-3002 : Step(20): len = 268399, overlap = 15.75
PHY-3002 : Step(21): len = 261852, overlap = 15.75
PHY-3002 : Step(22): len = 258610, overlap = 15.75
PHY-3002 : Step(23): len = 249018, overlap = 15.75
PHY-3002 : Step(24): len = 243009, overlap = 13.5
PHY-3002 : Step(25): len = 239321, overlap = 13.5
PHY-3002 : Step(26): len = 235047, overlap = 13.5
PHY-3002 : Step(27): len = 209391, overlap = 20.25
PHY-3002 : Step(28): len = 204550, overlap = 20.25
PHY-3002 : Step(29): len = 202182, overlap = 20.25
PHY-3002 : Step(30): len = 166130, overlap = 18
PHY-3002 : Step(31): len = 157259, overlap = 20.25
PHY-3002 : Step(32): len = 155262, overlap = 20.25
PHY-3002 : Step(33): len = 153670, overlap = 20.25
PHY-3002 : Step(34): len = 138949, overlap = 20.25
PHY-3002 : Step(35): len = 134704, overlap = 20.25
PHY-3002 : Step(36): len = 132615, overlap = 20.25
PHY-3002 : Step(37): len = 128114, overlap = 20.25
PHY-3002 : Step(38): len = 124672, overlap = 20.25
PHY-3002 : Step(39): len = 122524, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.35226e-05
PHY-3002 : Step(40): len = 122827, overlap = 15.75
PHY-3002 : Step(41): len = 121579, overlap = 15.75
PHY-3002 : Step(42): len = 120991, overlap = 13.5
PHY-3002 : Step(43): len = 119343, overlap = 13.5
PHY-3002 : Step(44): len = 114504, overlap = 11.25
PHY-3002 : Step(45): len = 112347, overlap = 9
PHY-3002 : Step(46): len = 109677, overlap = 11.25
PHY-3002 : Step(47): len = 106738, overlap = 9
PHY-3002 : Step(48): len = 102714, overlap = 11.25
PHY-3002 : Step(49): len = 101268, overlap = 13.5
PHY-3002 : Step(50): len = 100062, overlap = 13.5
PHY-3002 : Step(51): len = 97779.8, overlap = 15.75
PHY-3002 : Step(52): len = 95716.7, overlap = 15.75
PHY-3002 : Step(53): len = 93699.3, overlap = 11.25
PHY-3002 : Step(54): len = 91155.6, overlap = 13.5
PHY-3002 : Step(55): len = 90404.1, overlap = 15.75
PHY-3002 : Step(56): len = 88958.6, overlap = 15.75
PHY-3002 : Step(57): len = 86364.8, overlap = 18
PHY-3002 : Step(58): len = 84708.6, overlap = 18
PHY-3002 : Step(59): len = 84295.3, overlap = 18
PHY-3002 : Step(60): len = 81916.4, overlap = 15.75
PHY-3002 : Step(61): len = 80503.8, overlap = 15.75
PHY-3002 : Step(62): len = 80160.5, overlap = 15.75
PHY-3002 : Step(63): len = 78602.5, overlap = 18
PHY-3002 : Step(64): len = 77694.8, overlap = 18
PHY-3002 : Step(65): len = 77267.3, overlap = 18
PHY-3002 : Step(66): len = 74901.9, overlap = 15.75
PHY-3002 : Step(67): len = 73728.5, overlap = 15.75
PHY-3002 : Step(68): len = 73426.6, overlap = 15.875
PHY-3002 : Step(69): len = 72348.8, overlap = 13.5
PHY-3002 : Step(70): len = 71689.4, overlap = 13.5625
PHY-3002 : Step(71): len = 71423.7, overlap = 15.8125
PHY-3002 : Step(72): len = 70761, overlap = 16.3125
PHY-3002 : Step(73): len = 69976.5, overlap = 16.5625
PHY-3002 : Step(74): len = 69289.8, overlap = 14.8125
PHY-3002 : Step(75): len = 68549.4, overlap = 15.125
PHY-3002 : Step(76): len = 67119.7, overlap = 18.125
PHY-3002 : Step(77): len = 66255.8, overlap = 18.25
PHY-3002 : Step(78): len = 65234, overlap = 18.5
PHY-3002 : Step(79): len = 64962.3, overlap = 16.375
PHY-3002 : Step(80): len = 64305.3, overlap = 16.5
PHY-3002 : Step(81): len = 62113.7, overlap = 16
PHY-3002 : Step(82): len = 61039.5, overlap = 15.6875
PHY-3002 : Step(83): len = 60308.7, overlap = 15.625
PHY-3002 : Step(84): len = 60192.3, overlap = 15.625
PHY-3002 : Step(85): len = 60196.9, overlap = 15.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000187045
PHY-3002 : Step(86): len = 60522.8, overlap = 15.4375
PHY-3002 : Step(87): len = 60568.5, overlap = 15.4375
PHY-3002 : Step(88): len = 60481.1, overlap = 15.4375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00037409
PHY-3002 : Step(89): len = 60765, overlap = 15.1875
PHY-3002 : Step(90): len = 60786.6, overlap = 15.1875
PHY-3001 : Before Legalized: Len = 60786.6
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007820s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 65160.7, Over = 1.6875
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2113 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.061331s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(91): len = 65154.7, overlap = 6.5625
PHY-3002 : Step(92): len = 65192.8, overlap = 6.125
PHY-3002 : Step(93): len = 63950.3, overlap = 6.5625
PHY-3002 : Step(94): len = 63295.5, overlap = 6.5625
PHY-3002 : Step(95): len = 62528.2, overlap = 6.8125
PHY-3002 : Step(96): len = 60850.6, overlap = 7.5
PHY-3002 : Step(97): len = 60299.5, overlap = 7.96875
PHY-3002 : Step(98): len = 59629.8, overlap = 8.90625
PHY-3002 : Step(99): len = 59014.7, overlap = 8.90625
PHY-3002 : Step(100): len = 57490.7, overlap = 9.53125
PHY-3002 : Step(101): len = 56134, overlap = 9.59375
PHY-3002 : Step(102): len = 55662.1, overlap = 9.53125
PHY-3002 : Step(103): len = 55172.8, overlap = 9.46875
PHY-3002 : Step(104): len = 54764.6, overlap = 9.65625
PHY-3002 : Step(105): len = 54365.5, overlap = 9.75
PHY-3002 : Step(106): len = 54255.1, overlap = 9.78125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00152141
PHY-3002 : Step(107): len = 54070.6, overlap = 9.28125
PHY-3002 : Step(108): len = 53836.1, overlap = 9.28125
PHY-3002 : Step(109): len = 53691.7, overlap = 9.8125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2113 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.063309s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000123633
PHY-3002 : Step(110): len = 53817.2, overlap = 54.1875
PHY-3002 : Step(111): len = 55064.3, overlap = 46.375
PHY-3002 : Step(112): len = 55587.1, overlap = 45.7812
PHY-3002 : Step(113): len = 55249.5, overlap = 46.0938
PHY-3002 : Step(114): len = 55021.6, overlap = 44.2812
PHY-3002 : Step(115): len = 54967.8, overlap = 39.4375
PHY-3002 : Step(116): len = 55038.2, overlap = 38.6875
PHY-3002 : Step(117): len = 54851.4, overlap = 38.6875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000247266
PHY-3002 : Step(118): len = 54811.8, overlap = 39.125
PHY-3002 : Step(119): len = 55277.8, overlap = 32.8125
PHY-3002 : Step(120): len = 55432.2, overlap = 32.8125
PHY-3002 : Step(121): len = 55415.2, overlap = 32.3438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000494532
PHY-3002 : Step(122): len = 55440.5, overlap = 31.7812
PHY-3002 : Step(123): len = 55598.7, overlap = 32.75
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7535, tnet num: 2113, tinst num: 1581, tnode num: 10701, tedge num: 12768.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 84.88 peak overflow 2.50
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2115.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58904, over cnt = 225(0%), over = 935, worst = 22
PHY-1001 : End global iterations;  0.062984s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (124.0%)

PHY-1001 : Congestion index: top1 = 43.38, top5 = 25.15, top10 = 16.28, top15 = 11.62.
PHY-1001 : End incremental global routing;  0.114938s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (122.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2113 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.071091s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (87.9%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.214966s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (109.0%)

OPT-1001 : Current memory(MB): used = 209, reserve = 176, peak = 209.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1610/2115.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58904, over cnt = 225(0%), over = 935, worst = 22
PHY-1002 : len = 65808, over cnt = 162(0%), over = 374, worst = 15
PHY-1002 : len = 68888, over cnt = 49(0%), over = 91, worst = 15
PHY-1002 : len = 70528, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 70760, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.106704s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (131.8%)

PHY-1001 : Congestion index: top1 = 36.98, top5 = 24.31, top10 = 17.62, top15 = 13.11.
OPT-1001 : End congestion update;  0.150414s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (124.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2113 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058747s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (79.8%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.212694s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (117.5%)

OPT-1001 : Current memory(MB): used = 213, reserve = 180, peak = 213.
OPT-1001 : End physical optimization;  0.660767s wall, 0.687500s user + 0.031250s system = 0.718750s CPU (108.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 375 LUT to BLE ...
SYN-4008 : Packed 375 LUT and 190 SEQ to BLE.
SYN-4003 : Packing 775 remaining SEQ's ...
SYN-4005 : Packed 76 SEQ with LUT/SLICE
SYN-4006 : 125 single LUT's are left
SYN-4006 : 699 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1074/1381 primitive instances ...
PHY-3001 : End packing;  0.050129s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.5%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 806 instances
RUN-1001 : 378 mslices, 377 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1933 nets
RUN-1001 : 1357 nets have 2 pins
RUN-1001 : 475 nets have [3 - 5] pins
RUN-1001 : 58 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 804 instances, 755 slices, 19 macros(192 instances: 122 mslices 70 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 55555.2, Over = 54
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6313, tnet num: 1931, tinst num: 804, tnode num: 8601, tedge num: 11153.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1931 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.278465s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (84.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.0472e-05
PHY-3002 : Step(124): len = 55090.3, overlap = 53.25
PHY-3002 : Step(125): len = 54633.8, overlap = 54
PHY-3002 : Step(126): len = 54314.8, overlap = 56.25
PHY-3002 : Step(127): len = 54249.6, overlap = 54
PHY-3002 : Step(128): len = 54329.2, overlap = 55.75
PHY-3002 : Step(129): len = 54153.4, overlap = 57
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.09439e-05
PHY-3002 : Step(130): len = 54229.6, overlap = 57.5
PHY-3002 : Step(131): len = 54512.9, overlap = 58.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000121888
PHY-3002 : Step(132): len = 55367, overlap = 55.5
PHY-3002 : Step(133): len = 55934.8, overlap = 52.5
PHY-3002 : Step(134): len = 56100.2, overlap = 50.75
PHY-3001 : Before Legalized: Len = 56100.2
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.080988s wall, 0.031250s user + 0.171875s system = 0.203125s CPU (250.8%)

PHY-3001 : After Legalized: Len = 68496.2, Over = 0
PHY-3001 : Trial Legalized: Len = 68496.2
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1931 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.048106s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000760742
PHY-3002 : Step(135): len = 64630.7, overlap = 4.75
PHY-3002 : Step(136): len = 62750.5, overlap = 11.25
PHY-3002 : Step(137): len = 61066.4, overlap = 14.75
PHY-3002 : Step(138): len = 60021.3, overlap = 19.25
PHY-3002 : Step(139): len = 59479, overlap = 21.5
PHY-3002 : Step(140): len = 59049.5, overlap = 23.5
PHY-3002 : Step(141): len = 58727.8, overlap = 23.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00152148
PHY-3002 : Step(142): len = 59034.2, overlap = 23
PHY-3002 : Step(143): len = 59175.4, overlap = 22
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00304297
PHY-3002 : Step(144): len = 59222.9, overlap = 21.75
PHY-3002 : Step(145): len = 59244.5, overlap = 22
PHY-3001 : Before Legalized: Len = 59244.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005165s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 63671.8, Over = 0
PHY-3001 : Legalized: Len = 63671.8, Over = 0
PHY-3001 : Spreading special nets. 7 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005379s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (290.5%)

PHY-3001 : 11 instances has been re-located, deltaX = 3, deltaY = 8, maxDist = 1.
PHY-3001 : Final: Len = 63817.8, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6313, tnet num: 1931, tinst num: 804, tnode num: 8601, tedge num: 11153.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 76/1933.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70504, over cnt = 152(0%), over = 231, worst = 6
PHY-1002 : len = 71496, over cnt = 65(0%), over = 84, worst = 4
PHY-1002 : len = 72248, over cnt = 12(0%), over = 19, worst = 3
PHY-1002 : len = 72456, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72472, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.121143s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (129.0%)

PHY-1001 : Congestion index: top1 = 31.27, top5 = 22.47, top10 = 17.54, top15 = 13.69.
PHY-1001 : End incremental global routing;  0.173829s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (116.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1931 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060362s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.263870s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (112.5%)

OPT-1001 : Current memory(MB): used = 216, reserve = 183, peak = 216.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1724/1933.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72472, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006186s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.27, top5 = 22.47, top10 = 17.54, top15 = 13.69.
OPT-1001 : End congestion update;  0.051344s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1931 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047839s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.0%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 764 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 804 instances, 755 slices, 19 macros(192 instances: 122 mslices 70 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 63821.2, Over = 0
PHY-3001 : End spreading;  0.006084s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 63821.2, Over = 0
PHY-3001 : End incremental legalization;  0.035353s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (176.8%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.149877s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (114.7%)

OPT-1001 : Current memory(MB): used = 220, reserve = 187, peak = 220.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1931 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049630s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.4%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1720/1933.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72472, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007218s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.29, top5 = 22.47, top10 = 17.56, top15 = 13.70.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1931 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056397s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (110.8%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.931034
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.840138s wall, 0.875000s user + 0.015625s system = 0.890625s CPU (106.0%)

RUN-1003 : finish command "place" in  4.879938s wall, 6.546875s user + 2.765625s system = 9.312500s CPU (190.8%)

RUN-1004 : used memory is 198 MB, reserved memory is 165 MB, peak memory is 220 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 806 instances
RUN-1001 : 378 mslices, 377 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1933 nets
RUN-1001 : 1357 nets have 2 pins
RUN-1001 : 475 nets have [3 - 5] pins
RUN-1001 : 58 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6313, tnet num: 1931, tinst num: 804, tnode num: 8601, tedge num: 11153.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 378 mslices, 377 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1931 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69352, over cnt = 155(0%), over = 236, worst = 6
PHY-1002 : len = 70368, over cnt = 90(0%), over = 116, worst = 3
PHY-1002 : len = 71552, over cnt = 11(0%), over = 12, worst = 2
PHY-1002 : len = 71736, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.111031s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (140.7%)

PHY-1001 : Congestion index: top1 = 30.93, top5 = 22.22, top10 = 17.42, top15 = 13.61.
PHY-1001 : End global routing;  0.162204s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (134.9%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 235, reserve = 203, peak = 235.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 495, reserve = 466, peak = 495.
PHY-1001 : End build detailed router design. 3.090344s wall, 2.984375s user + 0.078125s system = 3.062500s CPU (99.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 29088, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.047705s wall, 1.031250s user + 0.015625s system = 1.046875s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 527, reserve = 499, peak = 527.
PHY-1001 : End phase 1; 1.054151s wall, 1.031250s user + 0.015625s system = 1.046875s CPU (99.3%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 37% nets.
PHY-1001 : Routed 45% nets.
PHY-1001 : Routed 62% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 179032, over cnt = 30(0%), over = 30, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 529, reserve = 501, peak = 530.
PHY-1001 : End initial routed; 1.718287s wall, 2.281250s user + 0.078125s system = 2.359375s CPU (137.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1721(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.003   |  -46.700  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.332720s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (103.3%)

PHY-1001 : Current memory(MB): used = 532, reserve = 504, peak = 532.
PHY-1001 : End phase 2; 2.051108s wall, 2.625000s user + 0.078125s system = 2.703125s CPU (131.8%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 179032, over cnt = 30(0%), over = 30, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.016035s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (97.4%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 178936, over cnt = 14(0%), over = 14, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.024592s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (63.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 179016, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.027617s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (113.2%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 178960, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.023285s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (134.2%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 178984, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 4; 0.026880s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (116.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1721(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.003   |  -46.700  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.328871s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (99.8%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.162296s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (105.9%)

PHY-1001 : Current memory(MB): used = 546, reserve = 518, peak = 546.
PHY-1001 : End phase 3; 0.746278s wall, 0.734375s user + 0.015625s system = 0.750000s CPU (100.5%)

PHY-1003 : Routed, final wirelength = 178984
PHY-1001 : Current memory(MB): used = 547, reserve = 518, peak = 547.
PHY-1001 : End export database. 0.009333s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  7.124837s wall, 7.546875s user + 0.187500s system = 7.734375s CPU (108.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6313, tnet num: 1931, tinst num: 804, tnode num: 8601, tedge num: 11153.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[34] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dib[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[7] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[3] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[1] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[3] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[14] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_56.mi[0] slack -2859ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_56.mi[1] slack -2871ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_59.mi[0] slack -2991ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_59.mi[1] slack -3003ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_62.mi[0] slack -2991ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_62.mi[1] slack -2763ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_65.mi[0] slack -2845ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_65.mi[1] slack -2845ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_68.mi[0] slack -2991ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_68.mi[1] slack -2871ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_71.mi[0] slack -3003ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_71.mi[1] slack -2763ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_74.mi[0] slack -2857ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_74.mi[1] slack -2845ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_77.mi[0] slack -3003ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_77.mi[1] slack -3003ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6425, tnet num: 1987, tinst num: 860, tnode num: 8713, tedge num: 11265.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg2_syn_56_mi[0] slack -264ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_56_mi[1] slack -472ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_62_mi[1] slack -767ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_62_mi[0] slack -228ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_59_mi[1] slack -808ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_59_mi[0] slack -368ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_65_mi[0] slack -89ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_68_mi[1] slack -497ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_68_mi[0] slack -756ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_65_mi[1] slack -808ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_74_mi[1] slack -333ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_74_mi[0] slack -468ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_71_mi[1] slack -769ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_77_mi[1] slack -316ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_77_mi[0] slack -268ps
RUN-1001 : End hold fix;  3.027001s wall, 3.062500s user + 0.187500s system = 3.250000s CPU (107.4%)

RUN-1003 : finish command "route" in  10.629383s wall, 11.156250s user + 0.375000s system = 11.531250s CPU (108.5%)

RUN-1004 : used memory is 525 MB, reserved memory is 499 MB, peak memory is 547 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      878   out of  19600    4.48%
#reg                     1052   out of  19600    5.37%
#le                      1577
  #lut only               525   out of   1577   33.29%
  #reg only               699   out of   1577   44.32%
  #lut&reg                353   out of   1577   22.38%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                      Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0       475
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3       101
#3        wendu/clk_us                    GCLK               lslice             wendu/cnt_b[2]_syn_11.q0    41
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4       1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di             1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1577   |686     |192     |1085    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1057   |278     |129     |861     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |31     |26      |5       |24      |0       |0       |
|    demodu                  |Demodulation                                     |437    |97      |41      |349     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |56     |37      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |6       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |14     |14      |0       |14      |0       |0       |
|    integ                   |Integration                                      |137    |27      |15      |109     |0       |0       |
|    modu                    |Modulation                                       |99     |31      |14      |97      |0       |1       |
|    rs422                   |Rs422Output                                      |317    |69      |46      |262     |0       |4       |
|    trans                   |SquareWaveGenerator                              |36     |28      |8       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |188    |132     |7       |118     |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |27     |19      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |124    |85      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |310    |265     |45      |72      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1374  
    #2          2       334   
    #3          3       112   
    #4          4        29   
    #5        5-10       64   
    #6        11-50      31   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6425, tnet num: 1987, tinst num: 860, tnode num: 8713, tedge num: 11265.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1987 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 860
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1989, pip num: 14569
BIT-1002 : Init feedthrough completely, num: 5
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1343 valid insts, and 38873 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  28.494827s wall, 121.937500s user + 0.968750s system = 122.906250s CPU (431.3%)

RUN-1004 : used memory is 544 MB, reserved memory is 515 MB, peak memory is 672 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250805_100606.log"
