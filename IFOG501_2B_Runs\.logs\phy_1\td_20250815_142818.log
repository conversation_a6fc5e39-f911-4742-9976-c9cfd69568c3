============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Fri Aug 15 14:28:18 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1567 instances
RUN-0007 : 379 luts, 940 seqs, 127 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2098 nets
RUN-1001 : 1544 nets have 2 pins
RUN-1001 : 449 nets have [3 - 5] pins
RUN-1001 : 62 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     237     
RUN-1001 :   No   |  No   |  Yes  |     124     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1565 instances, 379 luts, 940 seqs, 197 slices, 21 macros(197 instances: 127 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7479, tnet num: 2096, tinst num: 1565, tnode num: 10588, tedge num: 12660.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2096 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.321877s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (97.1%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 550652
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1565.
PHY-3001 : End clustering;  0.000028s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 469254, overlap = 15.75
PHY-3002 : Step(2): len = 442546, overlap = 20.25
PHY-3002 : Step(3): len = 425299, overlap = 18
PHY-3002 : Step(4): len = 414584, overlap = 13.5
PHY-3002 : Step(5): len = 398979, overlap = 13.5
PHY-3002 : Step(6): len = 383101, overlap = 13.5
PHY-3002 : Step(7): len = 374515, overlap = 13.5
PHY-3002 : Step(8): len = 366319, overlap = 15.75
PHY-3002 : Step(9): len = 351492, overlap = 15.75
PHY-3002 : Step(10): len = 345104, overlap = 13.5
PHY-3002 : Step(11): len = 336383, overlap = 13.5
PHY-3002 : Step(12): len = 328814, overlap = 13.5
PHY-3002 : Step(13): len = 318528, overlap = 15.75
PHY-3002 : Step(14): len = 313698, overlap = 15.75
PHY-3002 : Step(15): len = 304732, overlap = 15.75
PHY-3002 : Step(16): len = 297147, overlap = 15.75
PHY-3002 : Step(17): len = 290498, overlap = 15.75
PHY-3002 : Step(18): len = 286182, overlap = 15.75
PHY-3002 : Step(19): len = 275425, overlap = 15.75
PHY-3002 : Step(20): len = 269332, overlap = 20.25
PHY-3002 : Step(21): len = 264260, overlap = 20.25
PHY-3002 : Step(22): len = 258466, overlap = 20.25
PHY-3002 : Step(23): len = 248108, overlap = 20.25
PHY-3002 : Step(24): len = 244450, overlap = 20.25
PHY-3002 : Step(25): len = 239656, overlap = 20.25
PHY-3002 : Step(26): len = 229937, overlap = 20.25
PHY-3002 : Step(27): len = 223600, overlap = 20.25
PHY-3002 : Step(28): len = 221393, overlap = 20.25
PHY-3002 : Step(29): len = 208411, overlap = 20.25
PHY-3002 : Step(30): len = 198471, overlap = 20.25
PHY-3002 : Step(31): len = 195478, overlap = 20.25
PHY-3002 : Step(32): len = 190395, overlap = 20.25
PHY-3002 : Step(33): len = 171324, overlap = 20.25
PHY-3002 : Step(34): len = 167928, overlap = 20.25
PHY-3002 : Step(35): len = 165648, overlap = 20.25
PHY-3002 : Step(36): len = 150702, overlap = 20.25
PHY-3002 : Step(37): len = 130997, overlap = 20.25
PHY-3002 : Step(38): len = 129542, overlap = 20.25
PHY-3002 : Step(39): len = 125850, overlap = 20.25
PHY-3002 : Step(40): len = 114385, overlap = 18
PHY-3002 : Step(41): len = 110168, overlap = 20.25
PHY-3002 : Step(42): len = 109301, overlap = 20.25
PHY-3002 : Step(43): len = 106757, overlap = 20.25
PHY-3002 : Step(44): len = 104570, overlap = 20.25
PHY-3002 : Step(45): len = 102553, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.51939e-05
PHY-3002 : Step(46): len = 103008, overlap = 13.5
PHY-3002 : Step(47): len = 101486, overlap = 13.5
PHY-3002 : Step(48): len = 100293, overlap = 13.5
PHY-3002 : Step(49): len = 99881.5, overlap = 11.25
PHY-3002 : Step(50): len = 97502, overlap = 15.75
PHY-3002 : Step(51): len = 94076.7, overlap = 15.75
PHY-3002 : Step(52): len = 92219.5, overlap = 13.9375
PHY-3002 : Step(53): len = 92009.6, overlap = 11.6875
PHY-3002 : Step(54): len = 90323.2, overlap = 13.9375
PHY-3002 : Step(55): len = 89149.2, overlap = 18.4375
PHY-3002 : Step(56): len = 86649.9, overlap = 18.4375
PHY-3002 : Step(57): len = 84778.4, overlap = 18
PHY-3002 : Step(58): len = 84444.9, overlap = 15.75
PHY-3002 : Step(59): len = 82454.4, overlap = 11.25
PHY-3002 : Step(60): len = 81331.7, overlap = 11.25
PHY-3002 : Step(61): len = 80305.6, overlap = 11.25
PHY-3002 : Step(62): len = 78502.7, overlap = 15.75
PHY-3002 : Step(63): len = 77222.7, overlap = 15.75
PHY-3002 : Step(64): len = 75981.5, overlap = 15.75
PHY-3002 : Step(65): len = 75036.5, overlap = 13.5
PHY-3002 : Step(66): len = 72942.7, overlap = 13.0625
PHY-3002 : Step(67): len = 72372.9, overlap = 13.0625
PHY-3002 : Step(68): len = 71975.9, overlap = 15.375
PHY-3002 : Step(69): len = 71647.4, overlap = 15.4375
PHY-3002 : Step(70): len = 71246.3, overlap = 15.5
PHY-3002 : Step(71): len = 70482.5, overlap = 13.75
PHY-3002 : Step(72): len = 68809.5, overlap = 16.5625
PHY-3002 : Step(73): len = 67556, overlap = 16.4375
PHY-3002 : Step(74): len = 66678.2, overlap = 14.25
PHY-3002 : Step(75): len = 66165.9, overlap = 14.5625
PHY-3002 : Step(76): len = 65629, overlap = 14.875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000170388
PHY-3002 : Step(77): len = 66042.7, overlap = 14.875
PHY-3002 : Step(78): len = 66201, overlap = 12.5
PHY-3002 : Step(79): len = 66219.6, overlap = 14.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000340776
PHY-3002 : Step(80): len = 66393.7, overlap = 14.75
PHY-3002 : Step(81): len = 66359.8, overlap = 15
PHY-3001 : Before Legalized: Len = 66359.8
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006304s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 68786.5, Over = 3.75
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2096 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.056995s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (82.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(82): len = 68617, overlap = 14.6562
PHY-3002 : Step(83): len = 68323.9, overlap = 14.5625
PHY-3002 : Step(84): len = 66934.4, overlap = 14.4688
PHY-3002 : Step(85): len = 66254.7, overlap = 15.0938
PHY-3002 : Step(86): len = 65475.5, overlap = 15.3438
PHY-3002 : Step(87): len = 65212, overlap = 15.2812
PHY-3002 : Step(88): len = 63395.3, overlap = 14.6875
PHY-3002 : Step(89): len = 61629.9, overlap = 14.125
PHY-3002 : Step(90): len = 60799.5, overlap = 14.5625
PHY-3002 : Step(91): len = 60232.6, overlap = 14.875
PHY-3002 : Step(92): len = 59920.2, overlap = 15.0625
PHY-3002 : Step(93): len = 59215.8, overlap = 15.875
PHY-3002 : Step(94): len = 58380.2, overlap = 17.1875
PHY-3002 : Step(95): len = 57734.4, overlap = 18.0312
PHY-3002 : Step(96): len = 56946.2, overlap = 18.1562
PHY-3002 : Step(97): len = 56481.6, overlap = 17.9688
PHY-3002 : Step(98): len = 55762.6, overlap = 17.9688
PHY-3002 : Step(99): len = 54875.6, overlap = 18.7812
PHY-3002 : Step(100): len = 54570.1, overlap = 19.4062
PHY-3002 : Step(101): len = 53931.4, overlap = 19.5625
PHY-3002 : Step(102): len = 53616.5, overlap = 19.5625
PHY-3002 : Step(103): len = 52689.9, overlap = 17.1562
PHY-3002 : Step(104): len = 52101, overlap = 17.5938
PHY-3002 : Step(105): len = 51814.3, overlap = 18.5625
PHY-3002 : Step(106): len = 51107.7, overlap = 18.75
PHY-3002 : Step(107): len = 50830.9, overlap = 18.75
PHY-3002 : Step(108): len = 50663.7, overlap = 18.8125
PHY-3002 : Step(109): len = 50583.2, overlap = 20.875
PHY-3002 : Step(110): len = 50359.5, overlap = 22.0625
PHY-3002 : Step(111): len = 50020.4, overlap = 22.5
PHY-3002 : Step(112): len = 49942, overlap = 22.625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000509176
PHY-3002 : Step(113): len = 49677.2, overlap = 22.75
PHY-3002 : Step(114): len = 49722.9, overlap = 22.6562
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00101835
PHY-3002 : Step(115): len = 49598.7, overlap = 20.0625
PHY-3002 : Step(116): len = 49556.7, overlap = 19.9375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2096 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.066821s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (93.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.56635e-05
PHY-3002 : Step(117): len = 50214, overlap = 54.9688
PHY-3002 : Step(118): len = 50847.6, overlap = 50.6562
PHY-3002 : Step(119): len = 51485, overlap = 50.8438
PHY-3002 : Step(120): len = 50996.9, overlap = 45.7812
PHY-3002 : Step(121): len = 50672.2, overlap = 45
PHY-3002 : Step(122): len = 50708.2, overlap = 40.4375
PHY-3002 : Step(123): len = 50716.7, overlap = 39.8438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000131327
PHY-3002 : Step(124): len = 50778.6, overlap = 39.2188
PHY-3002 : Step(125): len = 51068.8, overlap = 38.5938
PHY-3002 : Step(126): len = 51492.2, overlap = 38.1562
PHY-3002 : Step(127): len = 51995.7, overlap = 36.6562
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000262654
PHY-3002 : Step(128): len = 52120.8, overlap = 33.8125
PHY-3002 : Step(129): len = 52399.7, overlap = 32.6875
PHY-3002 : Step(130): len = 52857.3, overlap = 31.4375
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7479, tnet num: 2096, tinst num: 1565, tnode num: 10588, tedge num: 12660.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 86.94 peak overflow 2.47
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2098.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54936, over cnt = 211(0%), over = 1043, worst = 20
PHY-1001 : End global iterations;  0.073636s wall, 0.109375s user + 0.031250s system = 0.140625s CPU (191.0%)

PHY-1001 : Congestion index: top1 = 46.27, top5 = 25.25, top10 = 15.71, top15 = 11.11.
PHY-1001 : End incremental global routing;  0.122414s wall, 0.171875s user + 0.031250s system = 0.203125s CPU (165.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2096 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.062499s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.212309s wall, 0.265625s user + 0.031250s system = 0.296875s CPU (139.8%)

OPT-1001 : Current memory(MB): used = 208, reserve = 175, peak = 208.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1585/2098.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54936, over cnt = 211(0%), over = 1043, worst = 20
PHY-1002 : len = 63256, over cnt = 160(0%), over = 294, worst = 11
PHY-1002 : len = 66168, over cnt = 52(0%), over = 75, worst = 5
PHY-1002 : len = 67048, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 67280, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.087272s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (161.1%)

PHY-1001 : Congestion index: top1 = 39.14, top5 = 25.01, top10 = 17.65, top15 = 12.99.
OPT-1001 : End congestion update;  0.132813s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (129.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2096 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.054941s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (113.8%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.190949s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (122.7%)

OPT-1001 : Current memory(MB): used = 212, reserve = 179, peak = 212.
OPT-1001 : End physical optimization;  0.713359s wall, 0.812500s user + 0.031250s system = 0.843750s CPU (118.3%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 379 LUT to BLE ...
SYN-4008 : Packed 379 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 751 remaining SEQ's ...
SYN-4005 : Packed 94 SEQ with LUT/SLICE
SYN-4006 : 112 single LUT's are left
SYN-4006 : 657 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1036/1348 primitive instances ...
PHY-3001 : End packing;  0.049133s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.4%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 789 instances
RUN-1001 : 369 mslices, 369 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1917 nets
RUN-1001 : 1370 nets have 2 pins
RUN-1001 : 442 nets have [3 - 5] pins
RUN-1001 : 61 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 787 instances, 738 slices, 21 macros(197 instances: 127 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 52802.4, Over = 57
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6260, tnet num: 1915, tinst num: 787, tnode num: 8508, tedge num: 11035.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1915 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.275835s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (96.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.46347e-05
PHY-3002 : Step(131): len = 52435, overlap = 54.75
PHY-3002 : Step(132): len = 52098.2, overlap = 56.5
PHY-3002 : Step(133): len = 51962.9, overlap = 56.75
PHY-3002 : Step(134): len = 51887.8, overlap = 57.5
PHY-3002 : Step(135): len = 51659.5, overlap = 57
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.92694e-05
PHY-3002 : Step(136): len = 51799.1, overlap = 56.5
PHY-3002 : Step(137): len = 52556.2, overlap = 51.25
PHY-3002 : Step(138): len = 53071, overlap = 50.25
PHY-3002 : Step(139): len = 53372.5, overlap = 49.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000138539
PHY-3002 : Step(140): len = 53684.7, overlap = 48.5
PHY-3002 : Step(141): len = 54361.6, overlap = 45
PHY-3001 : Before Legalized: Len = 54361.6
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.078251s wall, 0.046875s user + 0.093750s system = 0.140625s CPU (179.7%)

PHY-3001 : After Legalized: Len = 67084.6, Over = 0
PHY-3001 : Trial Legalized: Len = 67084.6
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1915 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.047721s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000769367
PHY-3002 : Step(142): len = 62497, overlap = 7.25
PHY-3002 : Step(143): len = 60806.2, overlap = 12.75
PHY-3002 : Step(144): len = 59549.5, overlap = 14.75
PHY-3002 : Step(145): len = 58452.4, overlap = 17.5
PHY-3002 : Step(146): len = 58005.8, overlap = 20.5
PHY-3002 : Step(147): len = 57874.5, overlap = 20.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00153873
PHY-3002 : Step(148): len = 58168.5, overlap = 19.75
PHY-3002 : Step(149): len = 58326.1, overlap = 18.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00307747
PHY-3002 : Step(150): len = 58418.4, overlap = 18.25
PHY-3002 : Step(151): len = 58448.9, overlap = 18.25
PHY-3001 : Before Legalized: Len = 58448.9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004995s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 62507.3, Over = 0
PHY-3001 : Legalized: Len = 62507.3, Over = 0
PHY-3001 : Spreading special nets. 8 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005437s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 14 instances has been re-located, deltaX = 1, deltaY = 13, maxDist = 1.
PHY-3001 : Final: Len = 62687.3, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6260, tnet num: 1915, tinst num: 787, tnode num: 8508, tedge num: 11035.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 101/1917.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69408, over cnt = 159(0%), over = 257, worst = 8
PHY-1002 : len = 70480, over cnt = 84(0%), over = 112, worst = 5
PHY-1002 : len = 71536, over cnt = 18(0%), over = 26, worst = 5
PHY-1002 : len = 71928, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.127245s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (147.4%)

PHY-1001 : Congestion index: top1 = 33.08, top5 = 23.24, top10 = 17.78, top15 = 13.77.
PHY-1001 : End incremental global routing;  0.178690s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (131.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1915 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057083s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (109.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.264064s wall, 0.312500s user + 0.015625s system = 0.328125s CPU (124.3%)

OPT-1001 : Current memory(MB): used = 213, reserve = 181, peak = 214.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1711/1917.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71928, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006003s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 33.08, top5 = 23.24, top10 = 17.78, top15 = 13.77.
OPT-1001 : End congestion update;  0.052953s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1915 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046484s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (100.8%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 747 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 787 instances, 738 slices, 21 macros(197 instances: 127 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 62654.4, Over = 0
PHY-3001 : Spreading special nets. 1 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.004902s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 1 instances has been re-located, deltaX = 1, deltaY = 0, maxDist = 1.
PHY-3001 : Final: Len = 62686.4, Over = 0
PHY-3001 : End incremental legalization;  0.035989s wall, 0.078125s user + 0.031250s system = 0.109375s CPU (303.9%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.149134s wall, 0.156250s user + 0.046875s system = 0.203125s CPU (136.2%)

OPT-1001 : Current memory(MB): used = 217, reserve = 185, peak = 217.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1915 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047139s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (99.4%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1711/1917.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71928, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006783s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (230.4%)

PHY-1001 : Congestion index: top1 = 33.08, top5 = 23.24, top10 = 17.78, top15 = 13.77.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1915 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050487s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.8%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 32.689655
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.837888s wall, 0.906250s user + 0.062500s system = 0.968750s CPU (115.6%)

RUN-1003 : finish command "place" in  5.000391s wall, 7.046875s user + 2.562500s system = 9.609375s CPU (192.2%)

RUN-1004 : used memory is 197 MB, reserved memory is 163 MB, peak memory is 217 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 789 instances
RUN-1001 : 369 mslices, 369 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1917 nets
RUN-1001 : 1370 nets have 2 pins
RUN-1001 : 442 nets have [3 - 5] pins
RUN-1001 : 61 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6260, tnet num: 1915, tinst num: 787, tnode num: 8508, tedge num: 11035.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 369 mslices, 369 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1915 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68232, over cnt = 156(0%), over = 256, worst = 7
PHY-1002 : len = 69088, over cnt = 91(0%), over = 136, worst = 7
PHY-1002 : len = 70872, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 71000, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.112919s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (124.5%)

PHY-1001 : Congestion index: top1 = 32.67, top5 = 23.07, top10 = 17.55, top15 = 13.56.
PHY-1001 : End global routing;  0.161404s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (125.8%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 230, reserve = 199, peak = 233.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 492, reserve = 464, peak = 492.
PHY-1001 : End build detailed router design. 3.084697s wall, 3.000000s user + 0.078125s system = 3.078125s CPU (99.8%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 30384, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.057307s wall, 1.062500s user + 0.000000s system = 1.062500s CPU (100.5%)

PHY-1001 : Current memory(MB): used = 524, reserve = 498, peak = 524.
PHY-1001 : End phase 1; 1.063253s wall, 1.062500s user + 0.000000s system = 1.062500s CPU (99.9%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 29% nets.
PHY-1001 : Routed 37% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 176408, over cnt = 42(0%), over = 42, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 526, reserve = 498, peak = 526.
PHY-1001 : End initial routed; 1.400446s wall, 1.890625s user + 0.187500s system = 2.078125s CPU (148.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1702(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.784   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.959   |  -44.734  |  22   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.323244s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (101.5%)

PHY-1001 : Current memory(MB): used = 527, reserve = 499, peak = 527.
PHY-1001 : End phase 2; 1.723778s wall, 2.218750s user + 0.187500s system = 2.406250s CPU (139.6%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 176408, over cnt = 42(0%), over = 42, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.013620s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 176272, over cnt = 10(0%), over = 10, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.038663s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (121.2%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 176352, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.021019s wall, 0.000000s user + 0.015625s system = 0.015625s CPU (74.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1702(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.784   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.959   |  -44.734  |  22   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.324270s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (101.2%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 9 feed throughs used by 9 nets
PHY-1001 : End commit to database; 0.164567s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (104.4%)

PHY-1001 : Current memory(MB): used = 542, reserve = 514, peak = 542.
PHY-1001 : End phase 3; 0.690702s wall, 0.656250s user + 0.031250s system = 0.687500s CPU (99.5%)

PHY-1003 : Routed, final wirelength = 176352
PHY-1001 : Current memory(MB): used = 543, reserve = 514, peak = 543.
PHY-1001 : End export database. 0.010270s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  6.748247s wall, 7.109375s user + 0.312500s system = 7.421875s CPU (110.0%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6260, tnet num: 1915, tinst num: 787, tnode num: 8508, tedge num: 11035.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[24] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dia[6] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[26] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dia[8] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[36] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[0] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[37] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[1] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[41] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[5] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[6] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[7] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[47] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[2] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[0] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[0] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[1] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[3] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[16] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/asy_w_rst1 endpin signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_18.sr slack -48ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg1_syn_212.mi[0] slack -2719ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_74.mi[0] slack -2827ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_74.mi[1] slack -2719ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_78.mi[0] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_78.mi[1] slack -2683ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_81.mi[0] slack -2801ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_81.mi[1] slack -2959ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_84.mi[0] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_84.mi[1] slack -2813ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_87.mi[0] slack -2801ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_87.mi[1] slack -2959ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_90.mi[0] slack -2583ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_90.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_93.mi[0] slack -2719ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_93.mi[1] slack -2588ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_95.mi[0] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6384, tnet num: 1977, tinst num: 849, tnode num: 8632, tedge num: 11159.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_93_mi[0] slack -363ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_90_mi[0] slack -786ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_90_mi[1] slack -417ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_93_mi[1] slack -178ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_87_mi[0] slack -484ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_95_mi[0] slack -601ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_78_mi[1] slack -515ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_81_mi[0] slack -656ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_81_mi[1] slack -851ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_78_mi[0] slack -518ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_84_mi[1] slack -308ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg2_syn_84_mi[0] slack -250ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_87_mi[1] slack -311ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_74_mi[0] slack -617ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg1_syn_212_mi[0] slack -819ps
RUN-1001 : End hold fix;  2.906994s wall, 3.109375s user + 0.109375s system = 3.218750s CPU (110.7%)

RUN-1003 : finish command "route" in  10.130094s wall, 10.718750s user + 0.421875s system = 11.140625s CPU (110.0%)

RUN-1004 : used memory is 531 MB, reserved memory is 504 MB, peak memory is 543 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   15
  #output                  21
  #inout                    1

Utilization Statistics
#lut                      900   out of  19600    4.59%
#reg                     1025   out of  19600    5.23%
#le                      1557
  #lut only               532   out of   1557   34.17%
  #reg only               657   out of   1557   42.20%
  #lut&reg                368   out of   1557   23.64%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    13
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                      Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0       462
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3       102
#3        wendu/clk_us                    GCLK               lslice             wendu/cnt_b[2]_syn_11.q0    43
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4       1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di             1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
  RxTransmit       INPUT        M14        LVCMOS33          N/A          PULLUP      IREG    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1557   |703     |197     |1059    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1046   |296     |133     |834     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |23     |19      |4       |21      |0       |0       |
|    demodu                  |Demodulation                                     |458    |106     |46      |350     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |57     |30      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |6       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |14     |6       |0       |14      |0       |0       |
|    integ                   |Integration                                      |144    |30      |15      |116     |0       |0       |
|    modu                    |Modulation                                       |66     |43      |14      |62      |0       |1       |
|    rs422                   |Rs422Output                                      |321    |72      |46      |265     |0       |4       |
|    trans                   |SquareWaveGenerator                              |34     |26      |8       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |175    |128     |7       |118     |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |26      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |26     |19      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |114    |83      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |312    |267     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1393  
    #2          2       306   
    #3          3       104   
    #4          4        32   
    #5        5-10       68   
    #6        11-50      31   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6384, tnet num: 1977, tinst num: 849, tnode num: 8632, tedge num: 11159.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1977 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 849
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1979, pip num: 14502
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 15
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1330 valid insts, and 38880 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  2.965467s wall, 17.640625s user + 0.046875s system = 17.687500s CPU (596.4%)

RUN-1004 : used memory is 547 MB, reserved memory is 518 MB, peak memory is 674 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250815_142818.log"
