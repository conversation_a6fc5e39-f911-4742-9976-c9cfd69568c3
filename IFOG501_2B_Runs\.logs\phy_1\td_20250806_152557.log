============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Wed Aug  6 15:25:57 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1589 instances
RUN-0007 : 375 luts, 967 seqs, 126 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2122 nets
RUN-1001 : 1545 nets have 2 pins
RUN-1001 : 474 nets have [3 - 5] pins
RUN-1001 : 61 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     263     
RUN-1001 :   No   |  No   |  Yes  |     125     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1587 instances, 375 luts, 967 seqs, 196 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7559, tnet num: 2120, tinst num: 1587, tnode num: 10728, tedge num: 12804.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2120 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.276831s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (96.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 524717
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1587.
PHY-3001 : End clustering;  0.000020s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 450746, overlap = 18
PHY-3002 : Step(2): len = 427379, overlap = 20.25
PHY-3002 : Step(3): len = 405469, overlap = 15.75
PHY-3002 : Step(4): len = 395688, overlap = 18
PHY-3002 : Step(5): len = 378971, overlap = 11.25
PHY-3002 : Step(6): len = 368049, overlap = 15.75
PHY-3002 : Step(7): len = 354961, overlap = 11.25
PHY-3002 : Step(8): len = 346248, overlap = 11.25
PHY-3002 : Step(9): len = 336470, overlap = 13.5
PHY-3002 : Step(10): len = 327233, overlap = 13.5
PHY-3002 : Step(11): len = 322838, overlap = 11.25
PHY-3002 : Step(12): len = 314751, overlap = 11.25
PHY-3002 : Step(13): len = 309408, overlap = 11.25
PHY-3002 : Step(14): len = 302530, overlap = 11.25
PHY-3002 : Step(15): len = 297830, overlap = 13.5
PHY-3002 : Step(16): len = 288197, overlap = 15.75
PHY-3002 : Step(17): len = 283189, overlap = 15.75
PHY-3002 : Step(18): len = 278011, overlap = 15.75
PHY-3002 : Step(19): len = 270601, overlap = 15.75
PHY-3002 : Step(20): len = 263340, overlap = 15.75
PHY-3002 : Step(21): len = 261015, overlap = 15.75
PHY-3002 : Step(22): len = 252189, overlap = 13.5
PHY-3002 : Step(23): len = 241646, overlap = 13.5
PHY-3002 : Step(24): len = 237558, overlap = 13.5
PHY-3002 : Step(25): len = 234330, overlap = 18
PHY-3002 : Step(26): len = 201724, overlap = 20.25
PHY-3002 : Step(27): len = 195611, overlap = 20.25
PHY-3002 : Step(28): len = 194087, overlap = 20.25
PHY-3002 : Step(29): len = 185354, overlap = 18
PHY-3002 : Step(30): len = 159066, overlap = 18
PHY-3002 : Step(31): len = 155255, overlap = 18
PHY-3002 : Step(32): len = 151629, overlap = 18
PHY-3002 : Step(33): len = 144307, overlap = 18
PHY-3002 : Step(34): len = 141675, overlap = 18
PHY-3002 : Step(35): len = 134047, overlap = 15.75
PHY-3002 : Step(36): len = 131306, overlap = 18
PHY-3002 : Step(37): len = 129100, overlap = 15.75
PHY-3002 : Step(38): len = 127087, overlap = 15.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000105931
PHY-3002 : Step(39): len = 127076, overlap = 13.5
PHY-3002 : Step(40): len = 126533, overlap = 13.5
PHY-3002 : Step(41): len = 125833, overlap = 13.5
PHY-3002 : Step(42): len = 124409, overlap = 15.75
PHY-3002 : Step(43): len = 121174, overlap = 15.75
PHY-3002 : Step(44): len = 119322, overlap = 15.75
PHY-3002 : Step(45): len = 117302, overlap = 13.5
PHY-3002 : Step(46): len = 114433, overlap = 11.25
PHY-3002 : Step(47): len = 110477, overlap = 11.25
PHY-3002 : Step(48): len = 108895, overlap = 15.75
PHY-3002 : Step(49): len = 107574, overlap = 15.75
PHY-3002 : Step(50): len = 104236, overlap = 18
PHY-3002 : Step(51): len = 100820, overlap = 15.75
PHY-3002 : Step(52): len = 99947.9, overlap = 18
PHY-3002 : Step(53): len = 96207.5, overlap = 15.8125
PHY-3002 : Step(54): len = 94586.3, overlap = 15.8125
PHY-3002 : Step(55): len = 94175.3, overlap = 15.75
PHY-3002 : Step(56): len = 93538.5, overlap = 18
PHY-3002 : Step(57): len = 92008.2, overlap = 18
PHY-3002 : Step(58): len = 91368.6, overlap = 18
PHY-3002 : Step(59): len = 88575, overlap = 13.5
PHY-3002 : Step(60): len = 87115.1, overlap = 13.5
PHY-3002 : Step(61): len = 85091.4, overlap = 15.75
PHY-3002 : Step(62): len = 84196.8, overlap = 13.5
PHY-3002 : Step(63): len = 82550.3, overlap = 15.75
PHY-3002 : Step(64): len = 81812.1, overlap = 15.75
PHY-3002 : Step(65): len = 78832, overlap = 18
PHY-3002 : Step(66): len = 77420.8, overlap = 18
PHY-3002 : Step(67): len = 75900.5, overlap = 15.75
PHY-3002 : Step(68): len = 75666.3, overlap = 15.75
PHY-3002 : Step(69): len = 73287.1, overlap = 13.8125
PHY-3002 : Step(70): len = 71743.8, overlap = 16.625
PHY-3002 : Step(71): len = 71500.4, overlap = 14.75
PHY-3002 : Step(72): len = 70568.1, overlap = 17.125
PHY-3002 : Step(73): len = 70289, overlap = 19.375
PHY-3002 : Step(74): len = 70178.2, overlap = 19.375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000211862
PHY-3002 : Step(75): len = 70500.7, overlap = 19.375
PHY-3002 : Step(76): len = 70635, overlap = 17
PHY-3002 : Step(77): len = 70626.5, overlap = 16.875
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000423723
PHY-3002 : Step(78): len = 70794.6, overlap = 16.875
PHY-3002 : Step(79): len = 70764.6, overlap = 16.625
PHY-3001 : Before Legalized: Len = 70764.6
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008669s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 74578, Over = 0.875
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2120 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.069403s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (90.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(80): len = 74423.4, overlap = 4.1875
PHY-3002 : Step(81): len = 72702.7, overlap = 3.5
PHY-3002 : Step(82): len = 71482.8, overlap = 3.375
PHY-3002 : Step(83): len = 70654.5, overlap = 3.375
PHY-3002 : Step(84): len = 69622.7, overlap = 3.375
PHY-3002 : Step(85): len = 68423.3, overlap = 3.3125
PHY-3002 : Step(86): len = 66857.8, overlap = 3.84375
PHY-3002 : Step(87): len = 65896.9, overlap = 3.875
PHY-3002 : Step(88): len = 65145.3, overlap = 4.03125
PHY-3002 : Step(89): len = 64435.3, overlap = 4.53125
PHY-3002 : Step(90): len = 63271.2, overlap = 4.09375
PHY-3002 : Step(91): len = 62301.5, overlap = 6.96875
PHY-3002 : Step(92): len = 61559.5, overlap = 6.78125
PHY-3002 : Step(93): len = 60805.8, overlap = 7.03125
PHY-3002 : Step(94): len = 59127.4, overlap = 9.09375
PHY-3002 : Step(95): len = 58409.5, overlap = 9.125
PHY-3002 : Step(96): len = 57477.9, overlap = 9.0625
PHY-3002 : Step(97): len = 56610.2, overlap = 9.0625
PHY-3002 : Step(98): len = 56028.7, overlap = 11.6875
PHY-3002 : Step(99): len = 55066.1, overlap = 11.75
PHY-3002 : Step(100): len = 54366.1, overlap = 13.4375
PHY-3002 : Step(101): len = 53667.1, overlap = 13.4375
PHY-3002 : Step(102): len = 53273.6, overlap = 13.5
PHY-3002 : Step(103): len = 52845.7, overlap = 14.1875
PHY-3002 : Step(104): len = 52654.2, overlap = 14.5
PHY-3002 : Step(105): len = 52530.1, overlap = 14.4375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00582204
PHY-3002 : Step(106): len = 52431.2, overlap = 14.25
PHY-3002 : Step(107): len = 52246.3, overlap = 14.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2120 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.063267s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.8738e-05
PHY-3002 : Step(108): len = 52674.5, overlap = 55.375
PHY-3002 : Step(109): len = 53066.5, overlap = 54.2812
PHY-3002 : Step(110): len = 53811.5, overlap = 49.6562
PHY-3002 : Step(111): len = 53821.7, overlap = 45.9062
PHY-3002 : Step(112): len = 53471.3, overlap = 45.1562
PHY-3002 : Step(113): len = 53454, overlap = 45.7812
PHY-3002 : Step(114): len = 53563.1, overlap = 45.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000177476
PHY-3002 : Step(115): len = 53758.4, overlap = 45.4688
PHY-3002 : Step(116): len = 54087, overlap = 41.3125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000311482
PHY-3002 : Step(117): len = 54536.7, overlap = 40.1875
PHY-3002 : Step(118): len = 55228.6, overlap = 36.1875
PHY-3002 : Step(119): len = 56075.5, overlap = 35.5312
PHY-3002 : Step(120): len = 56451.8, overlap = 33.625
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7559, tnet num: 2120, tinst num: 1587, tnode num: 10728, tedge num: 12804.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 93.53 peak overflow 3.34
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2122.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58728, over cnt = 241(0%), over = 1021, worst = 25
PHY-1001 : End global iterations;  0.065237s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (191.6%)

PHY-1001 : Congestion index: top1 = 44.50, top5 = 25.58, top10 = 16.24, top15 = 11.57.
PHY-1001 : End incremental global routing;  0.117445s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (159.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2120 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.072627s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (107.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.221786s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (133.9%)

OPT-1001 : Current memory(MB): used = 209, reserve = 176, peak = 209.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1609/2122.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58728, over cnt = 241(0%), over = 1021, worst = 25
PHY-1002 : len = 67984, over cnt = 163(0%), over = 293, worst = 18
PHY-1002 : len = 70696, over cnt = 41(0%), over = 60, worst = 5
PHY-1002 : len = 71424, over cnt = 11(0%), over = 12, worst = 2
PHY-1002 : len = 72208, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.100532s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (124.3%)

PHY-1001 : Congestion index: top1 = 38.45, top5 = 25.31, top10 = 18.01, top15 = 13.38.
OPT-1001 : End congestion update;  0.146485s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (106.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2120 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059715s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.7%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.209657s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (104.3%)

OPT-1001 : Current memory(MB): used = 213, reserve = 180, peak = 213.
OPT-1001 : End physical optimization;  0.712977s wall, 0.765625s user + 0.015625s system = 0.781250s CPU (109.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 375 LUT to BLE ...
SYN-4008 : Packed 375 LUT and 192 SEQ to BLE.
SYN-4003 : Packing 775 remaining SEQ's ...
SYN-4005 : Packed 101 SEQ with LUT/SLICE
SYN-4006 : 99 single LUT's are left
SYN-4006 : 674 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1049/1360 primitive instances ...
PHY-3001 : End packing;  0.052753s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (118.5%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 800 instances
RUN-1001 : 374 mslices, 375 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1938 nets
RUN-1001 : 1365 nets have 2 pins
RUN-1001 : 470 nets have [3 - 5] pins
RUN-1001 : 60 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 798 instances, 749 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 56526.6, Over = 58
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6327, tnet num: 1936, tinst num: 798, tnode num: 8616, tedge num: 11175.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1936 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.335712s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (102.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.06126e-05
PHY-3002 : Step(121): len = 55749.6, overlap = 57
PHY-3002 : Step(122): len = 55109.5, overlap = 56.75
PHY-3002 : Step(123): len = 54717.2, overlap = 55.5
PHY-3002 : Step(124): len = 54822.9, overlap = 54
PHY-3002 : Step(125): len = 55085.4, overlap = 55.5
PHY-3002 : Step(126): len = 54929.4, overlap = 57.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.12251e-05
PHY-3002 : Step(127): len = 55128.6, overlap = 56.5
PHY-3002 : Step(128): len = 55936.3, overlap = 56.25
PHY-3002 : Step(129): len = 56472.2, overlap = 58
PHY-3002 : Step(130): len = 56542.7, overlap = 57
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00016245
PHY-3002 : Step(131): len = 56849.9, overlap = 56.25
PHY-3002 : Step(132): len = 57393.4, overlap = 54
PHY-3001 : Before Legalized: Len = 57393.4
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.075720s wall, 0.078125s user + 0.125000s system = 0.203125s CPU (268.3%)

PHY-3001 : After Legalized: Len = 71578.8, Over = 0
PHY-3001 : Trial Legalized: Len = 71578.8
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1936 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.052126s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (89.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00102009
PHY-3002 : Step(133): len = 67804, overlap = 6.25
PHY-3002 : Step(134): len = 65227.9, overlap = 11.25
PHY-3002 : Step(135): len = 62837, overlap = 16.25
PHY-3002 : Step(136): len = 61545.3, overlap = 20.5
PHY-3002 : Step(137): len = 61096.6, overlap = 22.5
PHY-3002 : Step(138): len = 60701.8, overlap = 22.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00204018
PHY-3002 : Step(139): len = 60829.3, overlap = 22.75
PHY-3002 : Step(140): len = 60853.8, overlap = 23.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00408036
PHY-3002 : Step(141): len = 60862.2, overlap = 23
PHY-3002 : Step(142): len = 60871.2, overlap = 23
PHY-3001 : Before Legalized: Len = 60871.2
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005464s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 65859, Over = 0
PHY-3001 : Legalized: Len = 65859, Over = 0
PHY-3001 : Spreading special nets. 8 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005603s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (278.9%)

PHY-3001 : 12 instances has been re-located, deltaX = 2, deltaY = 10, maxDist = 1.
PHY-3001 : Final: Len = 65923, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6327, tnet num: 1936, tinst num: 798, tnode num: 8616, tedge num: 11175.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 33/1938.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71128, over cnt = 158(0%), over = 249, worst = 7
PHY-1002 : len = 72304, over cnt = 96(0%), over = 125, worst = 4
PHY-1002 : len = 73896, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 73912, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 74072, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.142419s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (87.8%)

PHY-1001 : Congestion index: top1 = 32.46, top5 = 23.11, top10 = 17.84, top15 = 13.94.
PHY-1001 : End incremental global routing;  0.199524s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (94.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1936 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061588s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.290585s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (96.8%)

OPT-1001 : Current memory(MB): used = 216, reserve = 183, peak = 216.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1708/1938.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 74072, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005719s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.46, top5 = 23.11, top10 = 17.84, top15 = 13.94.
OPT-1001 : End congestion update;  0.051556s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (90.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1936 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059234s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.5%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 758 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 798 instances, 749 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 65935.6, Over = 0
PHY-3001 : End spreading;  0.005256s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 65935.6, Over = 0
PHY-3001 : End incremental legalization;  0.036787s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (169.9%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.161567s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (106.4%)

OPT-1001 : Current memory(MB): used = 221, reserve = 188, peak = 221.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1936 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061528s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.6%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1704/1938.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 74072, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 74072, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.016527s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (94.5%)

PHY-1001 : Congestion index: top1 = 32.46, top5 = 23.06, top10 = 17.81, top15 = 13.92.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1936 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053215s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.1%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 32.000000
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.937960s wall, 0.921875s user + 0.015625s system = 0.937500s CPU (100.0%)

RUN-1003 : finish command "place" in  5.305667s wall, 7.515625s user + 2.718750s system = 10.234375s CPU (192.9%)

RUN-1004 : used memory is 203 MB, reserved memory is 170 MB, peak memory is 221 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 800 instances
RUN-1001 : 374 mslices, 375 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1938 nets
RUN-1001 : 1365 nets have 2 pins
RUN-1001 : 470 nets have [3 - 5] pins
RUN-1001 : 60 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6327, tnet num: 1936, tinst num: 798, tnode num: 8616, tedge num: 11175.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 374 mslices, 375 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1936 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70728, over cnt = 151(0%), over = 242, worst = 7
PHY-1002 : len = 71928, over cnt = 88(0%), over = 116, worst = 4
PHY-1002 : len = 73320, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 73544, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.138740s wall, 0.171875s user + 0.062500s system = 0.234375s CPU (168.9%)

PHY-1001 : Congestion index: top1 = 32.26, top5 = 23.00, top10 = 17.74, top15 = 13.83.
PHY-1001 : End global routing;  0.189613s wall, 0.218750s user + 0.062500s system = 0.281250s CPU (148.3%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 235, reserve = 203, peak = 236.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 496, reserve = 468, peak = 496.
PHY-1001 : End build detailed router design. 3.182173s wall, 3.140625s user + 0.031250s system = 3.171875s CPU (99.7%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 30688, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.117398s wall, 1.109375s user + 0.000000s system = 1.109375s CPU (99.3%)

PHY-1001 : Current memory(MB): used = 528, reserve = 501, peak = 528.
PHY-1001 : End phase 1; 1.125486s wall, 1.109375s user + 0.000000s system = 1.109375s CPU (98.6%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 174976, over cnt = 24(0%), over = 24, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 530, reserve = 502, peak = 530.
PHY-1001 : End initial routed; 1.728967s wall, 2.890625s user + 0.171875s system = 3.062500s CPU (177.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1723(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.931   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.959   |  -46.005  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.375882s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (95.6%)

PHY-1001 : Current memory(MB): used = 531, reserve = 503, peak = 531.
PHY-1001 : End phase 2; 2.104939s wall, 3.250000s user + 0.171875s system = 3.421875s CPU (162.6%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 174976, over cnt = 24(0%), over = 24, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015325s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (102.0%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 175024, over cnt = 6(0%), over = 6, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.022976s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (340.0%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 175048, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.021027s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (74.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1723(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.931   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.959   |  -46.005  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.356354s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (96.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 2 feed throughs used by 2 nets
PHY-1001 : End commit to database; 0.167074s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (102.9%)

PHY-1001 : Current memory(MB): used = 545, reserve = 517, peak = 545.
PHY-1001 : End phase 3; 0.716282s wall, 0.734375s user + 0.031250s system = 0.765625s CPU (106.9%)

PHY-1003 : Routed, final wirelength = 175048
PHY-1001 : Current memory(MB): used = 545, reserve = 517, peak = 545.
PHY-1001 : End export database. 0.009729s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (160.6%)

PHY-1001 : End detail routing;  7.321213s wall, 8.437500s user + 0.234375s system = 8.671875s CPU (118.4%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6327, tnet num: 1936, tinst num: 798, tnode num: 8616, tedge num: 11175.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[36] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[7] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[8] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[47] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[2] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[4] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[0] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[0] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[3] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[0] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[3] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[16] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_58.mi[0] slack -2827ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_58.mi[1] slack -2947ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_62.mi[0] slack -2588ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_62.mi[1] slack -2827ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_65.mi[0] slack -2827ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_65.mi[1] slack -2827ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_68.mi[0] slack -2767ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_68.mi[1] slack -2801ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_71.mi[0] slack -2959ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_71.mi[1] slack -2827ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_74.mi[0] slack -2719ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_74.mi[1] slack -2947ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_77.mi[0] slack -2947ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_77.mi[1] slack -2827ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_79.mi[0] slack -2947ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/tx_data_dy_b[0]_syn_21.mi[0] slack -2959ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6449, tnet num: 1997, tinst num: 859, tnode num: 8738, tedge num: 11297.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_77_mi[1] slack -633ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_77_mi[0] slack -396ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_79_mi[0] slack -494ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_71_mi[0] slack -561ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_68_mi[1] slack -738ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_71_mi[1] slack -432ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_65_mi[1] slack -387ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_62_mi[0] slack -206ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_62_mi[1] slack -42ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_65_mi[0] slack -881ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_74_mi[1] slack -432ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/tx_data_dy_b[0]_syn_21_mi[0] slack -127ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_74_mi[0] slack -604ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_58_mi[0] slack -394ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_58_mi[1] slack -336ps
RUN-1001 : End hold fix;  3.239787s wall, 3.437500s user + 0.187500s system = 3.625000s CPU (111.9%)

RUN-1003 : finish command "route" in  11.093331s wall, 12.437500s user + 0.484375s system = 12.921875s CPU (116.5%)

RUN-1004 : used memory is 525 MB, reserved memory is 498 MB, peak memory is 545 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      895   out of  19600    4.57%
#reg                     1053   out of  19600    5.37%
#le                      1569
  #lut only               516   out of   1569   32.89%
  #reg only               674   out of   1569   42.96%
  #lut&reg                379   out of   1569   24.16%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         475
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         101
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q0    40
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1569   |699     |196     |1086    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1069   |289     |132     |862     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |21     |17      |4       |18      |0       |0       |
|    demodu                  |Demodulation                                     |454    |101     |45      |350     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |53     |28      |6       |44      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |14     |5       |0       |14      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |13     |7       |0       |13      |0       |0       |
|    integ                   |Integration                                      |145    |35      |15      |117     |0       |0       |
|    modu                    |Modulation                                       |101    |39      |14      |99      |0       |1       |
|    rs422                   |Rs422Output                                      |313    |70      |46      |258     |0       |4       |
|    trans                   |SquareWaveGenerator                              |35     |27      |8       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |173    |140     |7       |115     |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |27     |27      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |109    |85      |0       |83      |0       |0       |
|  wendu                     |DS18B20                                          |303    |258     |45      |75      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1387  
    #2          2       331   
    #3          3       116   
    #4          4        23   
    #5        5-10       66   
    #6        11-50      31   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6449, tnet num: 1997, tinst num: 859, tnode num: 8738, tedge num: 11297.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1997 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 859
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1999, pip num: 14562
BIT-1002 : Init feedthrough completely, num: 8
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1301 valid insts, and 39070 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.291607s wall, 18.671875s user + 0.062500s system = 18.734375s CPU (569.2%)

RUN-1004 : used memory is 519 MB, reserved memory is 492 MB, peak memory is 668 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250806_152557.log"
