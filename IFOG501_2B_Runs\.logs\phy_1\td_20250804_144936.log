============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Mon Aug  4 14:49:37 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1576 instances
RUN-0007 : 366 luts, 963 seqs, 126 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2107 nets
RUN-1001 : 1535 nets have 2 pins
RUN-1001 : 466 nets have [3 - 5] pins
RUN-1001 : 63 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     263     
RUN-1001 :   No   |  No   |  Yes  |     121     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1574 instances, 366 luts, 963 seqs, 196 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7521, tnet num: 2105, tinst num: 1574, tnode num: 10684, tedge num: 12753.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2105 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.251482s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (99.4%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 533630
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1574.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 461570, overlap = 20.25
PHY-3002 : Step(2): len = 444006, overlap = 15.75
PHY-3002 : Step(3): len = 431264, overlap = 20.25
PHY-3002 : Step(4): len = 421896, overlap = 15.75
PHY-3002 : Step(5): len = 405193, overlap = 18
PHY-3002 : Step(6): len = 392197, overlap = 13.5
PHY-3002 : Step(7): len = 380911, overlap = 13.5
PHY-3002 : Step(8): len = 374962, overlap = 13.5
PHY-3002 : Step(9): len = 356003, overlap = 13.5
PHY-3002 : Step(10): len = 346243, overlap = 13.5
PHY-3002 : Step(11): len = 338792, overlap = 11.25
PHY-3002 : Step(12): len = 328627, overlap = 11.25
PHY-3002 : Step(13): len = 318216, overlap = 11.25
PHY-3002 : Step(14): len = 313992, overlap = 11.25
PHY-3002 : Step(15): len = 300559, overlap = 11.25
PHY-3002 : Step(16): len = 293081, overlap = 13.5
PHY-3002 : Step(17): len = 288233, overlap = 13.5
PHY-3002 : Step(18): len = 282365, overlap = 13.5
PHY-3002 : Step(19): len = 266463, overlap = 13.5
PHY-3002 : Step(20): len = 262536, overlap = 13.5
PHY-3002 : Step(21): len = 257525, overlap = 13.5
PHY-3002 : Step(22): len = 245595, overlap = 13.5
PHY-3002 : Step(23): len = 237988, overlap = 18
PHY-3002 : Step(24): len = 236023, overlap = 20.25
PHY-3002 : Step(25): len = 222860, overlap = 20.25
PHY-3002 : Step(26): len = 201361, overlap = 20.25
PHY-3002 : Step(27): len = 196764, overlap = 20.25
PHY-3002 : Step(28): len = 194379, overlap = 20.25
PHY-3002 : Step(29): len = 143316, overlap = 18
PHY-3002 : Step(30): len = 140044, overlap = 18
PHY-3002 : Step(31): len = 137539, overlap = 20.25
PHY-3002 : Step(32): len = 133747, overlap = 20.25
PHY-3002 : Step(33): len = 130585, overlap = 20.25
PHY-3002 : Step(34): len = 128142, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000109412
PHY-3002 : Step(35): len = 128779, overlap = 15.75
PHY-3002 : Step(36): len = 127714, overlap = 15.75
PHY-3002 : Step(37): len = 126773, overlap = 13.5
PHY-3002 : Step(38): len = 125329, overlap = 11.25
PHY-3002 : Step(39): len = 121304, overlap = 13.5
PHY-3002 : Step(40): len = 119570, overlap = 9
PHY-3002 : Step(41): len = 117340, overlap = 9
PHY-3002 : Step(42): len = 115666, overlap = 9
PHY-3002 : Step(43): len = 111110, overlap = 11.25
PHY-3002 : Step(44): len = 108963, overlap = 9
PHY-3002 : Step(45): len = 107746, overlap = 9
PHY-3002 : Step(46): len = 105580, overlap = 11.25
PHY-3002 : Step(47): len = 103550, overlap = 13.5
PHY-3002 : Step(48): len = 102080, overlap = 11.25
PHY-3002 : Step(49): len = 101416, overlap = 11.25
PHY-3002 : Step(50): len = 96535.5, overlap = 15.75
PHY-3002 : Step(51): len = 95561.4, overlap = 13.5
PHY-3002 : Step(52): len = 93162.9, overlap = 15.75
PHY-3002 : Step(53): len = 92333.7, overlap = 15.75
PHY-3002 : Step(54): len = 91642.5, overlap = 13.5
PHY-3002 : Step(55): len = 88884.6, overlap = 15.75
PHY-3002 : Step(56): len = 82542.5, overlap = 15.75
PHY-3002 : Step(57): len = 81538.5, overlap = 15.75
PHY-3002 : Step(58): len = 80413.4, overlap = 15.75
PHY-3002 : Step(59): len = 79973.3, overlap = 15.75
PHY-3002 : Step(60): len = 79655.9, overlap = 13.5
PHY-3002 : Step(61): len = 79243.6, overlap = 15.75
PHY-3002 : Step(62): len = 78649.6, overlap = 15.75
PHY-3002 : Step(63): len = 77766.2, overlap = 15.75
PHY-3002 : Step(64): len = 76324.8, overlap = 18
PHY-3002 : Step(65): len = 72878.2, overlap = 18.5625
PHY-3002 : Step(66): len = 72183.4, overlap = 18.625
PHY-3002 : Step(67): len = 71338.9, overlap = 16.375
PHY-3002 : Step(68): len = 70919.4, overlap = 16.5625
PHY-3002 : Step(69): len = 70617.3, overlap = 16.8125
PHY-3002 : Step(70): len = 69253.4, overlap = 19
PHY-3002 : Step(71): len = 68203.8, overlap = 12.1875
PHY-3002 : Step(72): len = 67098.4, overlap = 14.4375
PHY-3002 : Step(73): len = 65844.1, overlap = 14.4375
PHY-3002 : Step(74): len = 65543.4, overlap = 14.1875
PHY-3002 : Step(75): len = 64364.5, overlap = 18.25
PHY-3002 : Step(76): len = 63162.2, overlap = 16.3125
PHY-3002 : Step(77): len = 61793.7, overlap = 16.6875
PHY-3002 : Step(78): len = 61824.4, overlap = 16.5625
PHY-3002 : Step(79): len = 61452.8, overlap = 16.4375
PHY-3002 : Step(80): len = 60456, overlap = 16.4375
PHY-3002 : Step(81): len = 60413, overlap = 14.0625
PHY-3002 : Step(82): len = 60184.5, overlap = 14.1875
PHY-3002 : Step(83): len = 59439.4, overlap = 14
PHY-3002 : Step(84): len = 59448, overlap = 15.9375
PHY-3002 : Step(85): len = 58799.9, overlap = 13.6875
PHY-3002 : Step(86): len = 57948.2, overlap = 13.6875
PHY-3002 : Step(87): len = 57807, overlap = 15.75
PHY-3002 : Step(88): len = 57049.2, overlap = 13.5
PHY-3002 : Step(89): len = 56918.1, overlap = 13.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000218824
PHY-3002 : Step(90): len = 57448.3, overlap = 13.5
PHY-3002 : Step(91): len = 57468.1, overlap = 13.5
PHY-3002 : Step(92): len = 57369, overlap = 13.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000437648
PHY-3002 : Step(93): len = 57601.4, overlap = 13.5
PHY-3002 : Step(94): len = 57718.8, overlap = 13.5
PHY-3001 : Before Legalized: Len = 57718.8
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007182s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 62299.6, Over = 0
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2105 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.066028s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (94.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(95): len = 62430, overlap = 8
PHY-3002 : Step(96): len = 62457.6, overlap = 10.875
PHY-3002 : Step(97): len = 61341.4, overlap = 10.3125
PHY-3002 : Step(98): len = 61189.5, overlap = 10.3438
PHY-3002 : Step(99): len = 60008.8, overlap = 10.8438
PHY-3002 : Step(100): len = 59487.1, overlap = 10.6875
PHY-3002 : Step(101): len = 59124.4, overlap = 10.8438
PHY-3002 : Step(102): len = 58245.2, overlap = 12.0625
PHY-3002 : Step(103): len = 56954.7, overlap = 10.3438
PHY-3002 : Step(104): len = 56095.7, overlap = 11.4375
PHY-3002 : Step(105): len = 55538.5, overlap = 11.375
PHY-3002 : Step(106): len = 54147.1, overlap = 13.1562
PHY-3002 : Step(107): len = 53728.6, overlap = 15.5
PHY-3002 : Step(108): len = 53055.7, overlap = 15.4375
PHY-3002 : Step(109): len = 52898, overlap = 15.5
PHY-3002 : Step(110): len = 52862.7, overlap = 16.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000603427
PHY-3002 : Step(111): len = 52526.9, overlap = 16.3125
PHY-3002 : Step(112): len = 52600.5, overlap = 16.3125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00120685
PHY-3002 : Step(113): len = 52518.7, overlap = 16.375
PHY-3002 : Step(114): len = 52636, overlap = 16.625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2105 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.056982s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (109.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.59345e-05
PHY-3002 : Step(115): len = 52675.5, overlap = 62.125
PHY-3002 : Step(116): len = 53815.4, overlap = 59.4688
PHY-3002 : Step(117): len = 54460.1, overlap = 58.3438
PHY-3002 : Step(118): len = 54650.5, overlap = 58.2188
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000151869
PHY-3002 : Step(119): len = 54562.5, overlap = 57.7188
PHY-3002 : Step(120): len = 54884.5, overlap = 40.5
PHY-3002 : Step(121): len = 55473, overlap = 42.375
PHY-3002 : Step(122): len = 55121.5, overlap = 41.625
PHY-3002 : Step(123): len = 54888.6, overlap = 40.1875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000303738
PHY-3002 : Step(124): len = 54962.2, overlap = 39.625
PHY-3002 : Step(125): len = 55176.9, overlap = 38.8438
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7521, tnet num: 2105, tinst num: 1574, tnode num: 10684, tedge num: 12753.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 87.34 peak overflow 2.84
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2107.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58208, over cnt = 237(0%), over = 942, worst = 24
PHY-1001 : End global iterations;  0.064182s wall, 0.140625s user + 0.031250s system = 0.171875s CPU (267.8%)

PHY-1001 : Congestion index: top1 = 43.04, top5 = 25.28, top10 = 16.11, top15 = 11.43.
PHY-1001 : End incremental global routing;  0.112614s wall, 0.171875s user + 0.031250s system = 0.203125s CPU (180.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2105 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.063854s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (97.9%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.203991s wall, 0.265625s user + 0.031250s system = 0.296875s CPU (145.5%)

OPT-1001 : Current memory(MB): used = 207, reserve = 174, peak = 207.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1576/2107.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58208, over cnt = 237(0%), over = 942, worst = 24
PHY-1002 : len = 65752, over cnt = 158(0%), over = 281, worst = 11
PHY-1002 : len = 68984, over cnt = 32(0%), over = 39, worst = 4
PHY-1002 : len = 69296, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 70048, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.084259s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (148.4%)

PHY-1001 : Congestion index: top1 = 36.27, top5 = 24.46, top10 = 17.58, top15 = 13.02.
OPT-1001 : End congestion update;  0.127204s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (135.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2105 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052623s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (118.8%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.182969s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (128.1%)

OPT-1001 : Current memory(MB): used = 210, reserve = 178, peak = 210.
OPT-1001 : End physical optimization;  0.618094s wall, 0.734375s user + 0.031250s system = 0.765625s CPU (123.9%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 366 LUT to BLE ...
SYN-4008 : Packed 366 LUT and 188 SEQ to BLE.
SYN-4003 : Packing 775 remaining SEQ's ...
SYN-4005 : Packed 77 SEQ with LUT/SLICE
SYN-4006 : 118 single LUT's are left
SYN-4006 : 698 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1064/1375 primitive instances ...
PHY-3001 : End packing;  0.049457s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.8%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 803 instances
RUN-1001 : 376 mslices, 376 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1927 nets
RUN-1001 : 1358 nets have 2 pins
RUN-1001 : 461 nets have [3 - 5] pins
RUN-1001 : 64 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 801 instances, 752 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 55447.4, Over = 57.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6311, tnet num: 1925, tinst num: 801, tnode num: 8608, tedge num: 11151.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1925 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.276471s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (101.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.83193e-05
PHY-3002 : Step(126): len = 54858.2, overlap = 58.25
PHY-3002 : Step(127): len = 54382.4, overlap = 58.5
PHY-3002 : Step(128): len = 54159.4, overlap = 62.5
PHY-3002 : Step(129): len = 54324.1, overlap = 63.75
PHY-3002 : Step(130): len = 54184.1, overlap = 61.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.66387e-05
PHY-3002 : Step(131): len = 54247.7, overlap = 61.25
PHY-3002 : Step(132): len = 55044.5, overlap = 57.25
PHY-3002 : Step(133): len = 55511.9, overlap = 54
PHY-3002 : Step(134): len = 55502.8, overlap = 53.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000113277
PHY-3002 : Step(135): len = 55905.5, overlap = 53.5
PHY-3002 : Step(136): len = 56554.3, overlap = 51.25
PHY-3001 : Before Legalized: Len = 56554.3
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.070753s wall, 0.031250s user + 0.156250s system = 0.187500s CPU (265.0%)

PHY-3001 : After Legalized: Len = 69467.9, Over = 0
PHY-3001 : Trial Legalized: Len = 69467.9
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1925 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.047964s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000574541
PHY-3002 : Step(137): len = 64693.2, overlap = 10.75
PHY-3002 : Step(138): len = 62949.8, overlap = 15
PHY-3002 : Step(139): len = 61147.2, overlap = 17.75
PHY-3002 : Step(140): len = 59828.8, overlap = 21.5
PHY-3002 : Step(141): len = 59293.6, overlap = 26
PHY-3002 : Step(142): len = 59140.4, overlap = 27.5
PHY-3002 : Step(143): len = 58824.3, overlap = 28.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00114908
PHY-3002 : Step(144): len = 59129.5, overlap = 27.25
PHY-3002 : Step(145): len = 59249.4, overlap = 27.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00229816
PHY-3002 : Step(146): len = 59460.7, overlap = 27
PHY-3002 : Step(147): len = 59592.4, overlap = 26.25
PHY-3001 : Before Legalized: Len = 59592.4
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005162s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 63892.1, Over = 0
PHY-3001 : Legalized: Len = 63892.1, Over = 0
PHY-3001 : Spreading special nets. 5 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005464s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (285.9%)

PHY-3001 : 9 instances has been re-located, deltaX = 4, deltaY = 5, maxDist = 1.
PHY-3001 : Final: Len = 63982.1, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6311, tnet num: 1925, tinst num: 801, tnode num: 8608, tedge num: 11151.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 109/1927.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71024, over cnt = 147(0%), over = 227, worst = 6
PHY-1002 : len = 71912, over cnt = 83(0%), over = 96, worst = 3
PHY-1002 : len = 72832, over cnt = 14(0%), over = 14, worst = 1
PHY-1002 : len = 73008, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 73040, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.123478s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (177.2%)

PHY-1001 : Congestion index: top1 = 31.72, top5 = 22.25, top10 = 17.56, top15 = 13.72.
PHY-1001 : End incremental global routing;  0.174582s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (161.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1925 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056576s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (82.9%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.258705s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (138.9%)

OPT-1001 : Current memory(MB): used = 212, reserve = 180, peak = 214.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1700/1927.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73040, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005684s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.72, top5 = 22.25, top10 = 17.56, top15 = 13.72.
OPT-1001 : End congestion update;  0.053052s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1925 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057686s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.3%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 761 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 801 instances, 752 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 63954.4, Over = 0
PHY-3001 : End spreading;  0.005847s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 63954.4, Over = 0
PHY-3001 : End incremental legalization;  0.036289s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (129.2%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.161931s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (96.5%)

OPT-1001 : Current memory(MB): used = 216, reserve = 184, peak = 216.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1925 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046977s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (99.8%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1692/1927.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72992, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 72992, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 73008, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.026653s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (117.2%)

PHY-1001 : Congestion index: top1 = 31.75, top5 = 22.26, top10 = 17.51, top15 = 13.69.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1925 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.045277s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (103.5%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.275862
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.839453s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (111.7%)

RUN-1003 : finish command "place" in  4.867795s wall, 6.437500s user + 3.078125s system = 9.515625s CPU (195.5%)

RUN-1004 : used memory is 201 MB, reserved memory is 168 MB, peak memory is 216 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 803 instances
RUN-1001 : 376 mslices, 376 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1927 nets
RUN-1001 : 1358 nets have 2 pins
RUN-1001 : 461 nets have [3 - 5] pins
RUN-1001 : 64 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6311, tnet num: 1925, tinst num: 801, tnode num: 8608, tedge num: 11151.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 376 mslices, 376 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1925 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69552, over cnt = 140(0%), over = 214, worst = 6
PHY-1002 : len = 70392, over cnt = 100(0%), over = 123, worst = 4
PHY-1002 : len = 71640, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 71736, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.120877s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (142.2%)

PHY-1001 : Congestion index: top1 = 31.34, top5 = 22.04, top10 = 17.27, top15 = 13.47.
PHY-1001 : End global routing;  0.169600s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (129.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 229, reserve = 197, peak = 234.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 492, reserve = 464, peak = 492.
PHY-1001 : End build detailed router design. 3.086622s wall, 2.937500s user + 0.093750s system = 3.031250s CPU (98.2%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 29440, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.022997s wall, 1.015625s user + 0.015625s system = 1.031250s CPU (100.8%)

PHY-1001 : Current memory(MB): used = 524, reserve = 498, peak = 524.
PHY-1001 : End phase 1; 1.028990s wall, 1.015625s user + 0.015625s system = 1.031250s CPU (100.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 30% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 62% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 176672, over cnt = 27(0%), over = 27, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 527, reserve = 499, peak = 527.
PHY-1001 : End initial routed; 1.622305s wall, 2.218750s user + 0.078125s system = 2.296875s CPU (141.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1712(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.870   |  -43.739  |  24   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.325748s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (100.7%)

PHY-1001 : Current memory(MB): used = 528, reserve = 500, peak = 528.
PHY-1001 : End phase 2; 1.948146s wall, 2.546875s user + 0.078125s system = 2.625000s CPU (134.7%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 176672, over cnt = 27(0%), over = 27, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.013671s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (114.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 176592, over cnt = 5(0%), over = 5, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.026943s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (116.0%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 176624, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.021299s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (146.7%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 176624, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.017151s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (91.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1712(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.870   |  -43.476  |  24   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.331110s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (99.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.173981s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (98.8%)

PHY-1001 : Current memory(MB): used = 542, reserve = 514, peak = 542.
PHY-1001 : End phase 3; 0.711976s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (98.8%)

PHY-1003 : Routed, final wirelength = 176624
PHY-1001 : Current memory(MB): used = 543, reserve = 514, peak = 543.
PHY-1001 : End export database. 0.010235s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  6.954651s wall, 7.390625s user + 0.187500s system = 7.578125s CPU (109.0%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6311, tnet num: 1925, tinst num: 801, tnode num: 8608, tedge num: 11151.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : Too much hold violation, please check the src code.
RUN-1003 : finish command "route" in  7.761894s wall, 8.218750s user + 0.203125s system = 8.421875s CPU (108.5%)

RUN-1004 : used memory is 519 MB, reserved memory is 494 MB, peak memory is 543 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      764   out of  19600    3.90%
#reg                     1051   out of  19600    5.36%
#le                      1462
  #lut only               411   out of   1462   28.11%
  #reg only               698   out of   1462   47.74%
  #lut&reg                353   out of   1462   24.15%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         480
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         100
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    43
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1462   |568     |196     |1084    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1043   |259     |134     |858     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |26     |19      |7       |20      |0       |0       |
|    demodu                  |Demodulation                                     |431    |88      |45      |351     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |56     |39      |6       |46      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |14     |7       |0       |14      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |15      |0       |15      |0       |0       |
|    integ                   |Integration                                      |142    |28      |15      |114     |0       |0       |
|    modu                    |Modulation                                       |99     |35      |14      |97      |0       |1       |
|    rs422                   |Rs422Output                                      |313    |64      |46      |258     |0       |4       |
|    trans                   |SquareWaveGenerator                              |32     |25      |7       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |185    |130     |7       |116     |0       |0       |
|    U0                      |speed_select_Tx                                  |32     |25      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |28     |20      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |125    |85      |0       |84      |0       |0       |
|  wendu                     |DS18B20                                          |214    |169     |45      |76      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1319  
    #2          2       330   
    #3          3       110   
    #4          4        21   
    #5        5-10       70   
    #6        11-50      32   
    #7       101-500     1    
  Average     1.98            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6311, tnet num: 1925, tinst num: 801, tnode num: 8608, tedge num: 11151.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1925 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 801
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1927, pip num: 14424
BIT-1002 : Init feedthrough completely, num: 4
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1330 valid insts, and 37733 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.121516s wall, 17.390625s user + 0.078125s system = 17.468750s CPU (559.6%)

RUN-1004 : used memory is 544 MB, reserved memory is 514 MB, peak memory is 664 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250804_144936.log"
