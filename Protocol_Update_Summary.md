# 数据输出协议修改总结

## 修改概述
将Ctrl_Data模块中的数据输出协议从A5A5帧头协议修改为80帧头协议。

## 协议对比

### 旧协议（A5A5帧头）
- **帧头**: A5A5 (2字节)
- **陀螺数据**: 4字节 (32bit)
- **温度数据**: 2字节 (16bit)
- **异或校验**: 1字节
- **总长度**: 7字节

### 新协议（80帧头）
- **帧头**: 80 (1字节) - 第1字节
- **32bit角速度数据**: 5字节 (第2-6字节)
  - **第2字节**: 高1位补0 + 7位角速度数据D6~D0
  - **第3字节**: 高1位补0 + 7位角速度数据D13~D7
  - **第4字节**: 高1位补0 + 7位角速度数据D20~D14
  - **第5字节**: 高1位补0 + 7位角速度数据D27~D21
  - **第6字节**: 高4位补0 + 4位角速度数据D31~D28
- **校验XOR**: 1字节 (第7字节，第2-6字节数据的XOR值，高1位补0 + 7位校验位C6~C0)
- **16bit温度数据**: 2字节 (第8-9字节)
  - **第8字节**: 高1位补0 + 7位温度数据T6~T0
  - **第9字节**: 高1位补0 + 7位温度数据T13~T7
- **校验XOR**: 1字节 (第10字节，第2-9字节数据的XOR值，高1位补0 + 7位校验位C6~C0)
- **总长度**: 10字节

## 主要修改内容

### 1. 状态机修改
- 移除了`tx_Header2`状态，只保留一个`tx_Header`状态
- 增加了`tx_Eighth`、`tx_Ninth`、`tx_Tenth`状态
- 调整了状态参数编码

### 2. 校验算法修改
- 分离了角速度数据校验和温度数据校验
- `crc_data_speed`: 第2-6字节数据的XOR校验
- `crc_data_temp`: 第2-9字节数据的XOR校验

### 3. 数据发送序列修改
- **第1字节**: 帧头80
- **第2字节**: {1'b0, data_Packetdy[6:0]} - 高1位补0 + D6~D0
- **第3字节**: {1'b0, data_Packetdy[13:7]} - 高1位补0 + D13~D7
- **第4字节**: {1'b0, data_Packetdy[20:14]} - 高1位补0 + D20~D14
- **第5字节**: {1'b0, data_Packetdy[27:21]} - 高1位补0 + D27~D21
- **第6字节**: {4'b0000, data_Packetdy[31:28]} - 高4位补0 + D31~D28
- **第7字节**: crc_data_speed - 角速度数据校验，高1位补0 + C6~C0
- **第8字节**: {1'b0, temp_datady[6:0]} - 高1位补0 + T6~T0
- **第9字节**: {1'b0, temp_datady[13:7]} - 高1位补0 + T13~T7
- **第10字节**: crc_data_temp - 温度数据校验，高1位补0 + C6~C0

## 修改的文件
- `Src_al/Ctrl_Data.v`: 主要的协议实现文件

## 技术细节

### 校验算法实现
```verilog
// 角速度数据校验 (第7字节) - 高1位补0 + 7位校验位C6~C0
crc_data_speed <= {1'b0,  // 高位补0
                   data_Packetdy[6]^data_Packetdy[13]^data_Packetdy[20]^data_Packetdy[27]^data_Packetdy[31],  // C6
                   data_Packetdy[5]^data_Packetdy[12]^data_Packetdy[19]^data_Packetdy[26]^data_Packetdy[30],  // C5
                   data_Packetdy[4]^data_Packetdy[11]^data_Packetdy[18]^data_Packetdy[25]^data_Packetdy[29],  // C4
                   data_Packetdy[3]^data_Packetdy[10]^data_Packetdy[17]^data_Packetdy[24]^data_Packetdy[28],  // C3
                   data_Packetdy[2]^data_Packetdy[9]^data_Packetdy[16]^data_Packetdy[23],                    // C2
                   data_Packetdy[1]^data_Packetdy[8]^data_Packetdy[15]^data_Packetdy[22],                    // C1
                   data_Packetdy[0]^data_Packetdy[7]^data_Packetdy[14]^data_Packetdy[21]                     // C0
                   };

// 温度数据校验 (第10字节) - 高1位补0 + 7位校验位C6~C0
// 包含第2-9字节所有数据的XOR运算
crc_data_temp <= {1'b0,  // 高位补0
                  // C6~C0: 对应位的XOR运算，包括角速度数据、温度数据和第7字节校验数据
                  ...
                  };
```

### 状态机流程
1. `tx_idle` → 等待transmit信号
2. `tx_Header` → 发送帧头80
3. `tx_First` → 发送第2字节：{1'b0, D6~D0}
4. `tx_Second` → 发送第3字节：{1'b0, D13~D7}
5. `tx_Third` → 发送第4字节：{1'b0, D20~D14}
6. `tx_Fourth` → 发送第5字节：{1'b0, D27~D21}
7. `tx_Fifth` → 发送第6字节：{4'b0000, D31~D28}
8. `tx_Sixth` → 发送第7字节：角速度校验{1'b0, C6~C0}
9. `tx_Seventh` → 发送第8字节：{1'b0, T6~T0}
10. `tx_Eighth` → 发送第9字节：{1'b0, T13~T7}
11. `tx_Ninth` → 发送第10字节：温度校验{1'b0, C6~C0}
12. `tx_end` → 结束发送，返回idle

## 验证建议
1. 编译检查语法错误
2. 仿真验证数据包格式正确性
3. 验证校验算法的正确性
4. 测试与接收端的兼容性
