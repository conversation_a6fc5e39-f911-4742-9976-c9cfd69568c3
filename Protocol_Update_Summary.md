# 数据输出协议修改总结

## 修改概述
将Ctrl_Data模块中的数据输出协议从A5A5帧头协议修改为80帧头协议。

## 协议对比

### 旧协议（A5A5帧头）
- **帧头**: A5A5 (2字节)
- **陀螺数据**: 4字节 (32bit)
- **温度数据**: 2字节 (16bit)
- **异或校验**: 1字节
- **总长度**: 7字节

### 新协议（80帧头）
- **帧头**: 80 (1字节)
- **32bit角速度数据**: 4字节 (第2-5字节)
- **保留字节**: 1字节 (第6字节，设为0)
- **校验XOR**: 1字节 (第7字节，第2-6字节数据的XOR值)
- **16bit温度数据**: 2字节 (第8-9字节)
- **校验XOR**: 1字节 (第10字节，第2-9字节数据的XOR值)
- **总长度**: 10字节

## 主要修改内容

### 1. 状态机修改
- 移除了`tx_Header2`状态，只保留一个`tx_Header`状态
- 增加了`tx_Eighth`、`tx_Ninth`、`tx_Tenth`状态
- 调整了状态参数编码

### 2. 校验算法修改
- 分离了角速度数据校验和温度数据校验
- `crc_data_speed`: 第2-6字节数据的XOR校验
- `crc_data_temp`: 第2-9字节数据的XOR校验

### 3. 数据发送序列修改
- **第1字节**: 帧头80
- **第2-5字节**: 32bit角速度数据 (data_Packetdy[31:0])
- **第6字节**: 保留字节 (0x00)
- **第7字节**: 角速度数据校验 (crc_data_speed)
- **第8-9字节**: 16bit温度数据 (temp_datady[15:0])
- **第10字节**: 温度数据校验 (crc_data_temp)

## 修改的文件
- `Src_al/Ctrl_Data.v`: 主要的协议实现文件

## 技术细节

### 校验算法实现
```verilog
// 角速度数据校验 (第7字节)
crc_data_speed <= { data_Packetdy[31]^data_Packetdy[23]^data_Packetdy[15]^data_Packetdy[7],
                    data_Packetdy[30]^data_Packetdy[22]^data_Packetdy[14]^data_Packetdy[6],
                    // ... 其他位的XOR运算
                  };

// 温度数据校验 (第10字节)  
crc_data_temp <= { data_Packetdy[31]^...^temp_datady[15]^temp_datady[7]^crc_data_speed[7],
                   // ... 包含所有第2-9字节数据的XOR运算
                 };
```

### 状态机流程
1. `tx_idle` → 等待transmit信号
2. `tx_Header` → 发送帧头80
3. `tx_First` → 发送角速度数据最高字节
4. `tx_Second` → 发送角速度数据第2字节
5. `tx_Third` → 发送角速度数据第3字节
6. `tx_Fourth` → 发送角速度数据最低字节
7. `tx_Fifth` → 发送保留字节(0x00)
8. `tx_Sixth` → 发送角速度校验字节
9. `tx_Seventh` → 发送温度数据高字节
10. `tx_Eighth` → 发送温度数据低字节
11. `tx_Ninth` → 发送温度校验字节
12. `tx_end` → 结束发送，返回idle

## 验证建议
1. 编译检查语法错误
2. 仿真验证数据包格式正确性
3. 验证校验算法的正确性
4. 测试与接收端的兼容性
