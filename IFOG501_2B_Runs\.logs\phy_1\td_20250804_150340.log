============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Mon Aug  4 15:03:40 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1577 instances
RUN-0007 : 367 luts, 963 seqs, 126 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2110 nets
RUN-1001 : 1535 nets have 2 pins
RUN-1001 : 472 nets have [3 - 5] pins
RUN-1001 : 61 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     263     
RUN-1001 :   No   |  No   |  Yes  |     121     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1575 instances, 367 luts, 963 seqs, 196 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7523, tnet num: 2108, tinst num: 1575, tnode num: 10690, tedge num: 12757.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2108 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.258429s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (96.7%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 520445
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1575.
PHY-3001 : End clustering;  0.000021s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 454150, overlap = 20.25
PHY-3002 : Step(2): len = 431448, overlap = 9
PHY-3002 : Step(3): len = 418832, overlap = 20.25
PHY-3002 : Step(4): len = 407954, overlap = 15.75
PHY-3002 : Step(5): len = 394018, overlap = 20.25
PHY-3002 : Step(6): len = 379418, overlap = 13.5
PHY-3002 : Step(7): len = 369941, overlap = 18
PHY-3002 : Step(8): len = 364264, overlap = 18
PHY-3002 : Step(9): len = 342915, overlap = 13.5
PHY-3002 : Step(10): len = 335646, overlap = 13.5
PHY-3002 : Step(11): len = 329469, overlap = 13.5
PHY-3002 : Step(12): len = 318136, overlap = 13.5
PHY-3002 : Step(13): len = 307621, overlap = 13.5
PHY-3002 : Step(14): len = 304168, overlap = 13.5
PHY-3002 : Step(15): len = 291918, overlap = 13.5
PHY-3002 : Step(16): len = 284085, overlap = 13.5
PHY-3002 : Step(17): len = 279496, overlap = 13.5
PHY-3002 : Step(18): len = 274746, overlap = 13.5
PHY-3002 : Step(19): len = 264821, overlap = 13.5
PHY-3002 : Step(20): len = 259728, overlap = 13.5
PHY-3002 : Step(21): len = 255658, overlap = 13.5
PHY-3002 : Step(22): len = 250348, overlap = 13.5
PHY-3002 : Step(23): len = 239366, overlap = 13.5
PHY-3002 : Step(24): len = 236548, overlap = 13.5
PHY-3002 : Step(25): len = 231862, overlap = 13.5
PHY-3002 : Step(26): len = 226455, overlap = 15.75
PHY-3002 : Step(27): len = 220301, overlap = 18
PHY-3002 : Step(28): len = 217745, overlap = 18
PHY-3002 : Step(29): len = 207115, overlap = 20.25
PHY-3002 : Step(30): len = 201099, overlap = 20.25
PHY-3002 : Step(31): len = 198307, overlap = 20.25
PHY-3002 : Step(32): len = 194246, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000135995
PHY-3002 : Step(33): len = 195357, overlap = 11.25
PHY-3002 : Step(34): len = 193854, overlap = 9
PHY-3002 : Step(35): len = 192289, overlap = 9
PHY-3002 : Step(36): len = 189519, overlap = 9
PHY-3002 : Step(37): len = 184706, overlap = 11.25
PHY-3002 : Step(38): len = 182191, overlap = 6.75
PHY-3002 : Step(39): len = 177504, overlap = 4.5
PHY-3002 : Step(40): len = 174141, overlap = 6.75
PHY-3002 : Step(41): len = 171487, overlap = 6.75
PHY-3002 : Step(42): len = 168781, overlap = 6.75
PHY-3002 : Step(43): len = 165053, overlap = 4.5
PHY-3002 : Step(44): len = 162505, overlap = 2.25
PHY-3002 : Step(45): len = 159691, overlap = 4.5
PHY-3002 : Step(46): len = 156885, overlap = 4.5
PHY-3002 : Step(47): len = 153081, overlap = 2.25
PHY-3002 : Step(48): len = 150706, overlap = 4.5
PHY-3002 : Step(49): len = 149576, overlap = 4.5
PHY-3002 : Step(50): len = 146283, overlap = 4.5
PHY-3002 : Step(51): len = 139255, overlap = 4.5
PHY-3002 : Step(52): len = 136907, overlap = 2.25
PHY-3002 : Step(53): len = 135444, overlap = 4.5
PHY-3002 : Step(54): len = 127501, overlap = 6.75
PHY-3002 : Step(55): len = 122126, overlap = 6.75
PHY-3002 : Step(56): len = 121192, overlap = 6.75
PHY-3002 : Step(57): len = 120092, overlap = 4.5
PHY-3002 : Step(58): len = 118717, overlap = 6.75
PHY-3002 : Step(59): len = 118495, overlap = 2.25
PHY-3002 : Step(60): len = 116747, overlap = 4.5
PHY-3002 : Step(61): len = 112632, overlap = 9
PHY-3002 : Step(62): len = 110590, overlap = 9
PHY-3002 : Step(63): len = 108531, overlap = 4.5
PHY-3002 : Step(64): len = 107601, overlap = 4.5
PHY-3002 : Step(65): len = 105260, overlap = 6.75
PHY-3002 : Step(66): len = 103890, overlap = 6.75
PHY-3002 : Step(67): len = 101570, overlap = 9
PHY-3002 : Step(68): len = 100784, overlap = 9
PHY-3002 : Step(69): len = 97495.7, overlap = 9
PHY-3002 : Step(70): len = 95552.2, overlap = 6.75
PHY-3002 : Step(71): len = 93114.8, overlap = 6.75
PHY-3002 : Step(72): len = 92494.3, overlap = 4.5
PHY-3002 : Step(73): len = 90870.4, overlap = 6.75
PHY-3002 : Step(74): len = 88953.9, overlap = 6.75
PHY-3002 : Step(75): len = 85943.1, overlap = 6.75
PHY-3002 : Step(76): len = 84938, overlap = 6.9375
PHY-3002 : Step(77): len = 83147.4, overlap = 4.6875
PHY-3002 : Step(78): len = 82252.1, overlap = 9.375
PHY-3002 : Step(79): len = 79998.3, overlap = 7.125
PHY-3002 : Step(80): len = 78459.6, overlap = 9.4375
PHY-3002 : Step(81): len = 76258.5, overlap = 9.3125
PHY-3002 : Step(82): len = 76196.5, overlap = 9.1875
PHY-3002 : Step(83): len = 75048.2, overlap = 6.75
PHY-3002 : Step(84): len = 72606.2, overlap = 11.25
PHY-3002 : Step(85): len = 70603, overlap = 9
PHY-3002 : Step(86): len = 69424.6, overlap = 9
PHY-3002 : Step(87): len = 68025.9, overlap = 9
PHY-3002 : Step(88): len = 67752.8, overlap = 9
PHY-3002 : Step(89): len = 67641, overlap = 9
PHY-3002 : Step(90): len = 67374.7, overlap = 6.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00027199
PHY-3002 : Step(91): len = 67570.5, overlap = 6.75
PHY-3002 : Step(92): len = 67574.4, overlap = 6.75
PHY-3002 : Step(93): len = 67238.4, overlap = 9
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000543979
PHY-3002 : Step(94): len = 67581.5, overlap = 9
PHY-3002 : Step(95): len = 67691.1, overlap = 9
PHY-3002 : Step(96): len = 67706.5, overlap = 9
PHY-3001 : Before Legalized: Len = 67706.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.009439s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (165.5%)

PHY-3001 : After Legalized: Len = 70089.8, Over = 0
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2108 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.065663s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (95.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(97): len = 69985.7, overlap = 0.5625
PHY-3002 : Step(98): len = 67906.1, overlap = 1.0625
PHY-3002 : Step(99): len = 67386.3, overlap = 1.25
PHY-3002 : Step(100): len = 64732.9, overlap = 1.375
PHY-3002 : Step(101): len = 63778.9, overlap = 2.0625
PHY-3002 : Step(102): len = 62541.9, overlap = 2.75
PHY-3002 : Step(103): len = 61583.7, overlap = 3.375
PHY-3002 : Step(104): len = 58370.4, overlap = 5.875
PHY-3002 : Step(105): len = 55339.9, overlap = 9.0625
PHY-3002 : Step(106): len = 54314.1, overlap = 11.0625
PHY-3002 : Step(107): len = 53726.6, overlap = 11.375
PHY-3002 : Step(108): len = 52337.3, overlap = 14.9375
PHY-3002 : Step(109): len = 51843.7, overlap = 15
PHY-3002 : Step(110): len = 51184.7, overlap = 15.4375
PHY-3002 : Step(111): len = 50736.3, overlap = 15.375
PHY-3002 : Step(112): len = 50231.3, overlap = 15.4375
PHY-3002 : Step(113): len = 49906.1, overlap = 16.75
PHY-3002 : Step(114): len = 49536.9, overlap = 16.9375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000426206
PHY-3002 : Step(115): len = 49146.6, overlap = 16.8438
PHY-3002 : Step(116): len = 48922.7, overlap = 16.9688
PHY-3002 : Step(117): len = 48766.4, overlap = 20.0625
PHY-3002 : Step(118): len = 48729.1, overlap = 20.1562
PHY-3002 : Step(119): len = 48407.3, overlap = 20.875
PHY-3002 : Step(120): len = 48074.1, overlap = 21.0312
PHY-3002 : Step(121): len = 47373.5, overlap = 21.6562
PHY-3002 : Step(122): len = 47312, overlap = 21.8438
PHY-3002 : Step(123): len = 46891.1, overlap = 21.9062
PHY-3002 : Step(124): len = 46083.3, overlap = 21.75
PHY-3002 : Step(125): len = 45638.6, overlap = 22.2812
PHY-3002 : Step(126): len = 45401.3, overlap = 22.4688
PHY-3002 : Step(127): len = 45173, overlap = 22.2812
PHY-3002 : Step(128): len = 45126.3, overlap = 22.4688
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000852413
PHY-3002 : Step(129): len = 45076, overlap = 22.5938
PHY-3002 : Step(130): len = 45155.5, overlap = 22.4688
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00170483
PHY-3002 : Step(131): len = 45029.5, overlap = 22.8438
PHY-3002 : Step(132): len = 45125.7, overlap = 22.5625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2108 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.071278s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (109.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.32269e-05
PHY-3002 : Step(133): len = 45099.3, overlap = 77.9062
PHY-3002 : Step(134): len = 45702.1, overlap = 75.875
PHY-3002 : Step(135): len = 46217.4, overlap = 68.875
PHY-3002 : Step(136): len = 47502, overlap = 61.5625
PHY-3002 : Step(137): len = 48080.3, overlap = 59.4688
PHY-3002 : Step(138): len = 48014.8, overlap = 55.0312
PHY-3002 : Step(139): len = 47750.4, overlap = 54.0625
PHY-3002 : Step(140): len = 47567.4, overlap = 52.9688
PHY-3002 : Step(141): len = 47611.9, overlap = 48.2812
PHY-3002 : Step(142): len = 47541.4, overlap = 48.0625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000166454
PHY-3002 : Step(143): len = 47480.1, overlap = 46.9375
PHY-3002 : Step(144): len = 47712.8, overlap = 47.1875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000332908
PHY-3002 : Step(145): len = 47761.3, overlap = 45.0938
PHY-3002 : Step(146): len = 49737.2, overlap = 32.5625
PHY-3002 : Step(147): len = 50209.4, overlap = 31.125
PHY-3002 : Step(148): len = 50161.3, overlap = 30.875
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7523, tnet num: 2108, tinst num: 1575, tnode num: 10690, tedge num: 12757.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 102.12 peak overflow 3.31
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2110.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53288, over cnt = 233(0%), over = 1048, worst = 25
PHY-1001 : End global iterations;  0.072653s wall, 0.109375s user + 0.031250s system = 0.140625s CPU (193.6%)

PHY-1001 : Congestion index: top1 = 46.08, top5 = 25.17, top10 = 15.96, top15 = 11.44.
PHY-1001 : End incremental global routing;  0.122504s wall, 0.156250s user + 0.031250s system = 0.187500s CPU (153.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2108 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.071102s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (87.9%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.223253s wall, 0.250000s user + 0.031250s system = 0.281250s CPU (126.0%)

OPT-1001 : Current memory(MB): used = 208, reserve = 175, peak = 208.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1642/2110.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53288, over cnt = 233(0%), over = 1048, worst = 25
PHY-1002 : len = 59624, over cnt = 183(0%), over = 546, worst = 25
PHY-1002 : len = 65656, over cnt = 41(0%), over = 69, worst = 8
PHY-1002 : len = 66360, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 67080, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.093232s wall, 0.093750s user + 0.015625s system = 0.109375s CPU (117.3%)

PHY-1001 : Congestion index: top1 = 40.45, top5 = 25.35, top10 = 18.17, top15 = 13.38.
OPT-1001 : End congestion update;  0.136707s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (114.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2108 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059234s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.5%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.200027s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (109.4%)

OPT-1001 : Current memory(MB): used = 211, reserve = 178, peak = 211.
OPT-1001 : End physical optimization;  0.674711s wall, 0.687500s user + 0.078125s system = 0.765625s CPU (113.5%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 367 LUT to BLE ...
SYN-4008 : Packed 367 LUT and 188 SEQ to BLE.
SYN-4003 : Packing 775 remaining SEQ's ...
SYN-4005 : Packed 78 SEQ with LUT/SLICE
SYN-4006 : 118 single LUT's are left
SYN-4006 : 697 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1064/1375 primitive instances ...
PHY-3001 : End packing;  0.048817s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (96.0%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 810 instances
RUN-1001 : 380 mslices, 379 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1930 nets
RUN-1001 : 1361 nets have 2 pins
RUN-1001 : 462 nets have [3 - 5] pins
RUN-1001 : 65 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 808 instances, 759 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 49831, Over = 56.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6317, tnet num: 1928, tinst num: 808, tnode num: 8618, tedge num: 11163.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.284671s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (98.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.35217e-05
PHY-3002 : Step(149): len = 49688.6, overlap = 56.5
PHY-3002 : Step(150): len = 49095.5, overlap = 59
PHY-3002 : Step(151): len = 48769.5, overlap = 60.5
PHY-3002 : Step(152): len = 48599.4, overlap = 61.75
PHY-3002 : Step(153): len = 48321.1, overlap = 62.5
PHY-3002 : Step(154): len = 48212.6, overlap = 61.5
PHY-3002 : Step(155): len = 47776, overlap = 61.75
PHY-3002 : Step(156): len = 47269.7, overlap = 61.75
PHY-3002 : Step(157): len = 47222.9, overlap = 61.5
PHY-3002 : Step(158): len = 47013.9, overlap = 61.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.70434e-05
PHY-3002 : Step(159): len = 47770.1, overlap = 61
PHY-3002 : Step(160): len = 48139.5, overlap = 60
PHY-3002 : Step(161): len = 48631.9, overlap = 59.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000134087
PHY-3002 : Step(162): len = 49049.5, overlap = 55.75
PHY-3002 : Step(163): len = 49504.7, overlap = 53.5
PHY-3001 : Before Legalized: Len = 49504.7
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.075638s wall, 0.078125s user + 0.093750s system = 0.171875s CPU (227.2%)

PHY-3001 : After Legalized: Len = 62660.6, Over = 0
PHY-3001 : Trial Legalized: Len = 62660.6
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.051625s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (121.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000549853
PHY-3002 : Step(164): len = 58362.1, overlap = 14.25
PHY-3002 : Step(165): len = 57372.2, overlap = 16
PHY-3002 : Step(166): len = 55409.5, overlap = 20.5
PHY-3002 : Step(167): len = 54231, overlap = 23
PHY-3002 : Step(168): len = 53969.1, overlap = 23.5
PHY-3002 : Step(169): len = 54009.8, overlap = 22.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00109971
PHY-3002 : Step(170): len = 54389.4, overlap = 21.75
PHY-3002 : Step(171): len = 54451.9, overlap = 21.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00219941
PHY-3002 : Step(172): len = 54623.4, overlap = 21.25
PHY-3002 : Step(173): len = 54702.7, overlap = 21.75
PHY-3001 : Before Legalized: Len = 54702.7
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005041s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 59436.6, Over = 0
PHY-3001 : Legalized: Len = 59436.6, Over = 0
PHY-3001 : Spreading special nets. 13 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005845s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (267.3%)

PHY-3001 : 20 instances has been re-located, deltaX = 9, deltaY = 9, maxDist = 1.
PHY-3001 : Final: Len = 59710.6, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6317, tnet num: 1928, tinst num: 808, tnode num: 8618, tedge num: 11163.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 42/1930.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 66048, over cnt = 160(0%), over = 267, worst = 7
PHY-1002 : len = 67480, over cnt = 74(0%), over = 92, worst = 6
PHY-1002 : len = 68568, over cnt = 6(0%), over = 10, worst = 3
PHY-1002 : len = 68776, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.148249s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (115.9%)

PHY-1001 : Congestion index: top1 = 31.75, top5 = 22.68, top10 = 17.47, top15 = 13.56.
PHY-1001 : End incremental global routing;  0.200265s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (117.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.062660s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.292253s wall, 0.312500s user + 0.015625s system = 0.328125s CPU (112.3%)

OPT-1001 : Current memory(MB): used = 212, reserve = 180, peak = 214.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1698/1930.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68776, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006031s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (259.1%)

PHY-1001 : Congestion index: top1 = 31.75, top5 = 22.68, top10 = 17.47, top15 = 13.56.
OPT-1001 : End congestion update;  0.053764s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (87.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053138s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.2%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 768 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 808 instances, 759 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 59863, Over = 0
PHY-3001 : Spreading special nets. 1 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.004830s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (323.5%)

PHY-3001 : 1 instances has been re-located, deltaX = 0, deltaY = 1, maxDist = 1.
PHY-3001 : Final: Len = 59863, Over = 0
PHY-3001 : End incremental legalization;  0.035143s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (88.9%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 3 cells processed and 200 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.155958s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (100.2%)

OPT-1001 : Current memory(MB): used = 217, reserve = 184, peak = 217.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049873s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1689/1930.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68856, over cnt = 1(0%), over = 2, worst = 2
PHY-1002 : len = 68872, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.017750s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (88.0%)

PHY-1001 : Congestion index: top1 = 31.92, top5 = 22.70, top10 = 17.49, top15 = 13.57.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052288s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (89.6%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.551724
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.880784s wall, 0.875000s user + 0.031250s system = 0.906250s CPU (102.9%)

RUN-1003 : finish command "place" in  5.521342s wall, 7.218750s user + 3.218750s system = 10.437500s CPU (189.0%)

RUN-1004 : used memory is 199 MB, reserved memory is 168 MB, peak memory is 217 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 810 instances
RUN-1001 : 380 mslices, 379 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1930 nets
RUN-1001 : 1361 nets have 2 pins
RUN-1001 : 462 nets have [3 - 5] pins
RUN-1001 : 65 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6317, tnet num: 1928, tinst num: 808, tnode num: 8618, tedge num: 11163.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 380 mslices, 379 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 65504, over cnt = 156(0%), over = 258, worst = 7
PHY-1002 : len = 66944, over cnt = 69(0%), over = 85, worst = 6
PHY-1002 : len = 67944, over cnt = 14(0%), over = 15, worst = 2
PHY-1002 : len = 68216, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.128468s wall, 0.171875s user + 0.031250s system = 0.203125s CPU (158.1%)

PHY-1001 : Congestion index: top1 = 31.70, top5 = 22.60, top10 = 17.34, top15 = 13.48.
PHY-1001 : End global routing;  0.186452s wall, 0.218750s user + 0.031250s system = 0.250000s CPU (134.1%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 233, reserve = 201, peak = 233.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 493, reserve = 465, peak = 493.
PHY-1001 : End build detailed router design. 3.190273s wall, 3.156250s user + 0.046875s system = 3.203125s CPU (100.4%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33600, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.083380s wall, 1.078125s user + 0.000000s system = 1.078125s CPU (99.5%)

PHY-1001 : Current memory(MB): used = 525, reserve = 499, peak = 525.
PHY-1001 : End phase 1; 1.090084s wall, 1.078125s user + 0.000000s system = 1.078125s CPU (98.9%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 28% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 181536, over cnt = 29(0%), over = 29, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 527, reserve = 499, peak = 528.
PHY-1001 : End initial routed; 1.364751s wall, 2.421875s user + 0.171875s system = 2.593750s CPU (190.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1715(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.613   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -47.191  |  24   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.356547s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (100.8%)

PHY-1001 : Current memory(MB): used = 529, reserve = 501, peak = 529.
PHY-1001 : End phase 2; 1.721388s wall, 2.781250s user + 0.171875s system = 2.953125s CPU (171.6%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 181536, over cnt = 29(0%), over = 29, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.016324s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (95.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 181440, over cnt = 6(0%), over = 6, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.028067s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (111.3%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 181488, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.022203s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (140.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1715(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.613   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -47.191  |  24   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.342322s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (100.4%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 5 feed throughs used by 5 nets
PHY-1001 : End commit to database; 0.170776s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (100.6%)

PHY-1001 : Current memory(MB): used = 543, reserve = 515, peak = 543.
PHY-1001 : End phase 3; 0.705739s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (99.6%)

PHY-1003 : Routed, final wirelength = 181488
PHY-1001 : Current memory(MB): used = 544, reserve = 515, peak = 544.
PHY-1001 : End export database. 0.010101s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (154.7%)

PHY-1001 : End detail routing;  6.897611s wall, 7.890625s user + 0.218750s system = 8.109375s CPU (117.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6317, tnet num: 1928, tinst num: 808, tnode num: 8618, tedge num: 11163.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : Too much hold violation, please check the src code.
RUN-1003 : finish command "route" in  7.766410s wall, 8.796875s user + 0.250000s system = 9.046875s CPU (116.5%)

RUN-1004 : used memory is 498 MB, reserved memory is 471 MB, peak memory is 544 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      766   out of  19600    3.91%
#reg                     1053   out of  19600    5.37%
#le                      1463
  #lut only               410   out of   1463   28.02%
  #reg only               697   out of   1463   47.64%
  #lut&reg                356   out of   1463   24.33%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         482
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         101
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    40
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1463   |570     |196     |1086    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1057   |262     |135     |863     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |33     |26      |7       |20      |0       |0       |
|    demodu                  |Demodulation                                     |430    |76      |45      |351     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |56     |28      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |7       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |6       |0       |16      |0       |0       |
|    integ                   |Integration                                      |142    |22      |15      |114     |0       |0       |
|    modu                    |Modulation                                       |100    |44      |14      |98      |0       |1       |
|    rs422                   |Rs422Output                                      |325    |75      |46      |259     |0       |4       |
|    trans                   |SquareWaveGenerator                              |27     |19      |8       |21      |0       |0       |
|  u_uart                    |UART_Control                                     |176    |132     |7       |116     |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |23     |18      |0       |14      |0       |0       |
|    U2                      |Ctrl_Data                                        |116    |86      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |212    |167     |45      |73      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1322  
    #2          2       331   
    #3          3       106   
    #4          4        25   
    #5        5-10       69   
    #6        11-50      32   
    #7       101-500     1    
  Average     1.98            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6317, tnet num: 1928, tinst num: 808, tnode num: 8618, tedge num: 11163.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 808
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1930, pip num: 14384
BIT-1002 : Init feedthrough completely, num: 5
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1303 valid insts, and 37721 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.183518s wall, 17.343750s user + 0.062500s system = 17.406250s CPU (546.8%)

RUN-1004 : used memory is 538 MB, reserved memory is 508 MB, peak memory is 662 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250804_150340.log"
