============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Wed Aug  6 15:23:47 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1559 instances
RUN-0007 : 376 luts, 947 seqs, 115 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2078 nets
RUN-1001 : 1532 nets have 2 pins
RUN-1001 : 443 nets have [3 - 5] pins
RUN-1001 : 62 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     243     
RUN-1001 :   No   |  No   |  Yes  |     125     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1557 instances, 376 luts, 947 seqs, 185 slices, 19 macros(185 instances: 115 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7406, tnet num: 2076, tinst num: 1557, tnode num: 10507, tedge num: 12507.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2076 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.324320s wall, 0.312500s user + 0.015625s system = 0.328125s CPU (101.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 519907
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1557.
PHY-3001 : End clustering;  0.000020s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 449980, overlap = 20.25
PHY-3002 : Step(2): len = 419586, overlap = 13.5
PHY-3002 : Step(3): len = 407465, overlap = 20.25
PHY-3002 : Step(4): len = 395590, overlap = 13.5
PHY-3002 : Step(5): len = 381570, overlap = 18
PHY-3002 : Step(6): len = 370165, overlap = 13.5
PHY-3002 : Step(7): len = 362539, overlap = 13.5
PHY-3002 : Step(8): len = 354739, overlap = 18
PHY-3002 : Step(9): len = 345084, overlap = 15.75
PHY-3002 : Step(10): len = 338909, overlap = 15.75
PHY-3002 : Step(11): len = 329030, overlap = 15.75
PHY-3002 : Step(12): len = 322877, overlap = 13.5
PHY-3002 : Step(13): len = 314800, overlap = 13.5
PHY-3002 : Step(14): len = 308828, overlap = 13.5
PHY-3002 : Step(15): len = 300924, overlap = 13.5
PHY-3002 : Step(16): len = 295809, overlap = 15.75
PHY-3002 : Step(17): len = 288424, overlap = 15.75
PHY-3002 : Step(18): len = 283644, overlap = 15.75
PHY-3002 : Step(19): len = 276626, overlap = 15.75
PHY-3002 : Step(20): len = 272117, overlap = 15.75
PHY-3002 : Step(21): len = 265420, overlap = 13.5
PHY-3002 : Step(22): len = 261202, overlap = 13.5
PHY-3002 : Step(23): len = 253563, overlap = 13.5
PHY-3002 : Step(24): len = 249176, overlap = 15.75
PHY-3002 : Step(25): len = 243978, overlap = 20.25
PHY-3002 : Step(26): len = 238881, overlap = 20.25
PHY-3002 : Step(27): len = 232366, overlap = 20.25
PHY-3002 : Step(28): len = 229541, overlap = 20.25
PHY-3002 : Step(29): len = 222693, overlap = 20.25
PHY-3002 : Step(30): len = 215358, overlap = 20.25
PHY-3002 : Step(31): len = 209909, overlap = 20.25
PHY-3002 : Step(32): len = 207327, overlap = 20.25
PHY-3002 : Step(33): len = 198601, overlap = 20.25
PHY-3002 : Step(34): len = 193063, overlap = 20.25
PHY-3002 : Step(35): len = 189837, overlap = 20.25
PHY-3002 : Step(36): len = 185560, overlap = 20.25
PHY-3002 : Step(37): len = 174999, overlap = 20.25
PHY-3002 : Step(38): len = 171433, overlap = 20.25
PHY-3002 : Step(39): len = 168524, overlap = 20.25
PHY-3002 : Step(40): len = 160054, overlap = 20.25
PHY-3002 : Step(41): len = 149100, overlap = 20.25
PHY-3002 : Step(42): len = 147661, overlap = 20.25
PHY-3002 : Step(43): len = 140303, overlap = 18
PHY-3002 : Step(44): len = 97910.1, overlap = 15.75
PHY-3002 : Step(45): len = 96242.9, overlap = 15.75
PHY-3002 : Step(46): len = 94621.7, overlap = 15.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.60593e-05
PHY-3002 : Step(47): len = 95743, overlap = 13.5
PHY-3002 : Step(48): len = 95506.5, overlap = 15.75
PHY-3002 : Step(49): len = 94998.6, overlap = 18
PHY-3002 : Step(50): len = 94464.7, overlap = 18
PHY-3002 : Step(51): len = 93613.9, overlap = 18
PHY-3002 : Step(52): len = 90322.3, overlap = 15.75
PHY-3002 : Step(53): len = 88003.6, overlap = 18
PHY-3002 : Step(54): len = 86706.8, overlap = 13.5
PHY-3002 : Step(55): len = 85808.4, overlap = 15.75
PHY-3002 : Step(56): len = 83972.7, overlap = 15.75
PHY-3002 : Step(57): len = 82764.6, overlap = 15.75
PHY-3002 : Step(58): len = 81009.4, overlap = 18
PHY-3002 : Step(59): len = 79640.8, overlap = 18
PHY-3002 : Step(60): len = 78189.6, overlap = 15.75
PHY-3002 : Step(61): len = 76652.5, overlap = 15.75
PHY-3002 : Step(62): len = 76179, overlap = 15.75
PHY-3002 : Step(63): len = 71974.1, overlap = 16.5625
PHY-3002 : Step(64): len = 70762.2, overlap = 18.1875
PHY-3002 : Step(65): len = 69293, overlap = 18.25
PHY-3002 : Step(66): len = 68832.8, overlap = 13.9375
PHY-3002 : Step(67): len = 68603.4, overlap = 16.4375
PHY-3002 : Step(68): len = 67931.3, overlap = 16.5625
PHY-3002 : Step(69): len = 67324.4, overlap = 16.5625
PHY-3002 : Step(70): len = 66994.3, overlap = 16.8125
PHY-3002 : Step(71): len = 66278.1, overlap = 14.875
PHY-3002 : Step(72): len = 65029.2, overlap = 14.8125
PHY-3002 : Step(73): len = 64816.8, overlap = 14.625
PHY-3002 : Step(74): len = 64155, overlap = 16.8125
PHY-3002 : Step(75): len = 63789, overlap = 17.0625
PHY-3002 : Step(76): len = 63386.1, overlap = 17.0625
PHY-3002 : Step(77): len = 62141.9, overlap = 14.5
PHY-3002 : Step(78): len = 61445.3, overlap = 14.375
PHY-3002 : Step(79): len = 61189, overlap = 18.8125
PHY-3002 : Step(80): len = 60538, overlap = 18.6875
PHY-3002 : Step(81): len = 59900.4, overlap = 16.4375
PHY-3002 : Step(82): len = 59671, overlap = 16.25
PHY-3002 : Step(83): len = 58723.7, overlap = 16.1875
PHY-3002 : Step(84): len = 58009.6, overlap = 18
PHY-3002 : Step(85): len = 57878.3, overlap = 17.9375
PHY-3002 : Step(86): len = 57215.7, overlap = 15.6875
PHY-3002 : Step(87): len = 56422.7, overlap = 14.9375
PHY-3002 : Step(88): len = 56463.9, overlap = 12.6875
PHY-3002 : Step(89): len = 56110.8, overlap = 12.6875
PHY-3002 : Step(90): len = 55690, overlap = 14.9375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000192119
PHY-3002 : Step(91): len = 56002.7, overlap = 12.6875
PHY-3002 : Step(92): len = 56059.4, overlap = 12.6875
PHY-3002 : Step(93): len = 56101.3, overlap = 12.6875
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000384237
PHY-3002 : Step(94): len = 56156.5, overlap = 12.6875
PHY-3002 : Step(95): len = 56212.6, overlap = 12.6875
PHY-3001 : Before Legalized: Len = 56212.6
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.013016s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 59587.4, Over = 1.4375
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2076 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.060347s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (103.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(96): len = 59617.2, overlap = 9.625
PHY-3002 : Step(97): len = 59832.7, overlap = 8.71875
PHY-3002 : Step(98): len = 58661.5, overlap = 8.3125
PHY-3002 : Step(99): len = 58762.2, overlap = 8.125
PHY-3002 : Step(100): len = 57704.4, overlap = 7.6875
PHY-3002 : Step(101): len = 57341.5, overlap = 7.5
PHY-3002 : Step(102): len = 57063.8, overlap = 7.4375
PHY-3002 : Step(103): len = 56150.7, overlap = 5.25
PHY-3002 : Step(104): len = 54758.7, overlap = 12.5312
PHY-3002 : Step(105): len = 54219.6, overlap = 14.4062
PHY-3002 : Step(106): len = 53710.1, overlap = 14.5625
PHY-3002 : Step(107): len = 53460.3, overlap = 14.6875
PHY-3002 : Step(108): len = 52470.8, overlap = 14.5625
PHY-3002 : Step(109): len = 52239.8, overlap = 14.8125
PHY-3002 : Step(110): len = 52268, overlap = 16.4688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00131301
PHY-3002 : Step(111): len = 51896.1, overlap = 14.5938
PHY-3002 : Step(112): len = 51907.3, overlap = 15.0938
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00262602
PHY-3002 : Step(113): len = 51713.2, overlap = 14.7812
PHY-3002 : Step(114): len = 51794.7, overlap = 15
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2076 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.061783s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (101.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000121215
PHY-3002 : Step(115): len = 52147.8, overlap = 56.3125
PHY-3002 : Step(116): len = 53563.9, overlap = 50.2812
PHY-3002 : Step(117): len = 54559.7, overlap = 48.5312
PHY-3002 : Step(118): len = 54226.4, overlap = 48.3438
PHY-3002 : Step(119): len = 53551.1, overlap = 44.9688
PHY-3002 : Step(120): len = 53301.7, overlap = 45.6562
PHY-3002 : Step(121): len = 53337.6, overlap = 45.9062
PHY-3002 : Step(122): len = 53088.8, overlap = 42.25
PHY-3002 : Step(123): len = 53022.6, overlap = 40.4375
PHY-3002 : Step(124): len = 53059.9, overlap = 38.75
PHY-3002 : Step(125): len = 53273.9, overlap = 36.375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000242429
PHY-3002 : Step(126): len = 53096.9, overlap = 37.9375
PHY-3002 : Step(127): len = 52878.9, overlap = 37.9688
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000484858
PHY-3002 : Step(128): len = 53391.9, overlap = 37.9062
PHY-3002 : Step(129): len = 53596.8, overlap = 37.625
PHY-3002 : Step(130): len = 54488.5, overlap = 36.3438
PHY-3002 : Step(131): len = 54542.1, overlap = 36.2188
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7406, tnet num: 2076, tinst num: 1557, tnode num: 10507, tedge num: 12507.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 89.34 peak overflow 2.91
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2078.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 57776, over cnt = 239(0%), over = 991, worst = 24
PHY-1001 : End global iterations;  0.061600s wall, 0.109375s user + 0.046875s system = 0.156250s CPU (253.7%)

PHY-1001 : Congestion index: top1 = 42.18, top5 = 25.05, top10 = 16.39, top15 = 11.68.
PHY-1001 : End incremental global routing;  0.111602s wall, 0.156250s user + 0.046875s system = 0.203125s CPU (182.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2076 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.068642s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (113.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.209475s wall, 0.265625s user + 0.046875s system = 0.312500s CPU (149.2%)

OPT-1001 : Current memory(MB): used = 209, reserve = 176, peak = 209.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1611/2078.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 57776, over cnt = 239(0%), over = 991, worst = 24
PHY-1002 : len = 64048, over cnt = 165(0%), over = 418, worst = 16
PHY-1002 : len = 68824, over cnt = 22(0%), over = 27, worst = 3
PHY-1002 : len = 69112, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 69328, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.127669s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (134.6%)

PHY-1001 : Congestion index: top1 = 35.50, top5 = 24.27, top10 = 17.87, top15 = 13.27.
OPT-1001 : End congestion update;  0.172950s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (117.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2076 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058168s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (80.6%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.234469s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (113.3%)

OPT-1001 : Current memory(MB): used = 212, reserve = 179, peak = 212.
OPT-1001 : End physical optimization;  0.698460s wall, 0.765625s user + 0.062500s system = 0.828125s CPU (118.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 376 LUT to BLE ...
SYN-4008 : Packed 376 LUT and 192 SEQ to BLE.
SYN-4003 : Packing 755 remaining SEQ's ...
SYN-4005 : Packed 87 SEQ with LUT/SLICE
SYN-4006 : 113 single LUT's are left
SYN-4006 : 668 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1044/1344 primitive instances ...
PHY-3001 : End packing;  0.055034s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (85.2%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 787 instances
RUN-1001 : 368 mslices, 368 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1894 nets
RUN-1001 : 1355 nets have 2 pins
RUN-1001 : 436 nets have [3 - 5] pins
RUN-1001 : 62 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 785 instances, 736 slices, 19 macros(185 instances: 115 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 54396.8, Over = 57.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6186, tnet num: 1892, tinst num: 785, tnode num: 8423, tedge num: 10887.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1892 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.308219s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (101.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.10327e-05
PHY-3002 : Step(132): len = 53653, overlap = 59
PHY-3002 : Step(133): len = 53020.4, overlap = 60.75
PHY-3002 : Step(134): len = 52753.2, overlap = 61.25
PHY-3002 : Step(135): len = 52748.3, overlap = 60.75
PHY-3002 : Step(136): len = 52709.5, overlap = 59.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.20654e-05
PHY-3002 : Step(137): len = 53027.4, overlap = 60.5
PHY-3002 : Step(138): len = 53353.6, overlap = 60.25
PHY-3002 : Step(139): len = 53984.5, overlap = 59.75
PHY-3002 : Step(140): len = 54339.8, overlap = 54.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000164131
PHY-3002 : Step(141): len = 54794.5, overlap = 52.25
PHY-3002 : Step(142): len = 55256.6, overlap = 49.75
PHY-3001 : Before Legalized: Len = 55256.6
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.074258s wall, 0.031250s user + 0.125000s system = 0.156250s CPU (210.4%)

PHY-3001 : After Legalized: Len = 68132.1, Over = 0
PHY-3001 : Trial Legalized: Len = 68132.1
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1892 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.050534s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00110161
PHY-3002 : Step(143): len = 64146.3, overlap = 9.25
PHY-3002 : Step(144): len = 62732.7, overlap = 14
PHY-3002 : Step(145): len = 60729.4, overlap = 14.75
PHY-3002 : Step(146): len = 59682.7, overlap = 18.75
PHY-3002 : Step(147): len = 58998.8, overlap = 21.25
PHY-3002 : Step(148): len = 58593.5, overlap = 23.75
PHY-3002 : Step(149): len = 58352.7, overlap = 25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00220322
PHY-3002 : Step(150): len = 58532.1, overlap = 24.25
PHY-3002 : Step(151): len = 58568.2, overlap = 23
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00440645
PHY-3002 : Step(152): len = 58639.1, overlap = 23
PHY-3002 : Step(153): len = 58689.3, overlap = 22
PHY-3001 : Before Legalized: Len = 58689.3
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008129s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 63099.1, Over = 0
PHY-3001 : Legalized: Len = 63099.1, Over = 0
PHY-3001 : Spreading special nets. 5 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005538s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 8 instances has been re-located, deltaX = 2, deltaY = 6, maxDist = 1.
PHY-3001 : Final: Len = 63229.1, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6186, tnet num: 1892, tinst num: 785, tnode num: 8423, tedge num: 10887.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 71/1894.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69824, over cnt = 166(0%), over = 255, worst = 6
PHY-1002 : len = 70720, over cnt = 94(0%), over = 122, worst = 4
PHY-1002 : len = 71920, over cnt = 17(0%), over = 24, worst = 3
PHY-1002 : len = 72312, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72328, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.138073s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (135.8%)

PHY-1001 : Congestion index: top1 = 32.39, top5 = 22.94, top10 = 18.01, top15 = 14.11.
PHY-1001 : End incremental global routing;  0.191127s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (122.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1892 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059621s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.282940s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (116.0%)

OPT-1001 : Current memory(MB): used = 216, reserve = 183, peak = 216.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1702/1894.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72328, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007751s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (201.6%)

PHY-1001 : Congestion index: top1 = 32.39, top5 = 22.94, top10 = 18.01, top15 = 14.11.
OPT-1001 : End congestion update;  0.055577s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (112.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1892 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057216s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (109.2%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 745 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 785 instances, 736 slices, 19 macros(185 instances: 115 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 63196.8, Over = 0
PHY-3001 : End spreading;  0.004996s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (312.8%)

PHY-3001 : Final: Len = 63196.8, Over = 0
PHY-3001 : End incremental legalization;  0.034379s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (90.9%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.159937s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (107.5%)

OPT-1001 : Current memory(MB): used = 220, reserve = 188, peak = 221.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1892 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046345s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (101.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1698/1894.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72280, over cnt = 1(0%), over = 2, worst = 2
PHY-1002 : len = 72280, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72296, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.024375s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (64.1%)

PHY-1001 : Congestion index: top1 = 32.46, top5 = 22.95, top10 = 18.03, top15 = 14.11.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1892 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050773s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (123.1%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 32.068966
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.923670s wall, 0.984375s user + 0.000000s system = 0.984375s CPU (106.6%)

RUN-1003 : finish command "place" in  5.623772s wall, 7.484375s user + 3.312500s system = 10.796875s CPU (192.0%)

RUN-1004 : used memory is 200 MB, reserved memory is 168 MB, peak memory is 221 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 787 instances
RUN-1001 : 368 mslices, 368 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1894 nets
RUN-1001 : 1355 nets have 2 pins
RUN-1001 : 436 nets have [3 - 5] pins
RUN-1001 : 62 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6186, tnet num: 1892, tinst num: 785, tnode num: 8423, tedge num: 10887.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 368 mslices, 368 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1892 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69056, over cnt = 157(0%), over = 243, worst = 6
PHY-1002 : len = 69880, over cnt = 98(0%), over = 132, worst = 6
PHY-1002 : len = 71528, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 71560, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.115653s wall, 0.140625s user + 0.031250s system = 0.171875s CPU (148.6%)

PHY-1001 : Congestion index: top1 = 31.38, top5 = 22.64, top10 = 17.88, top15 = 13.98.
PHY-1001 : End global routing;  0.164374s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (133.1%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 236, reserve = 204, peak = 236.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 494, reserve = 465, peak = 494.
PHY-1001 : End build detailed router design. 3.236476s wall, 3.109375s user + 0.062500s system = 3.171875s CPU (98.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 30168, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.132944s wall, 1.125000s user + 0.015625s system = 1.140625s CPU (100.7%)

PHY-1001 : Current memory(MB): used = 526, reserve = 498, peak = 526.
PHY-1001 : End phase 1; 1.139572s wall, 1.125000s user + 0.015625s system = 1.140625s CPU (100.1%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 30% nets.
PHY-1001 : Routed 37% nets.
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 89% nets.
PHY-1022 : len = 176440, over cnt = 25(0%), over = 25, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 528, reserve = 500, peak = 528.
PHY-1001 : End initial routed; 1.539891s wall, 2.281250s user + 0.171875s system = 2.453125s CPU (159.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1689(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.776   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -46.727  |  21   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.355945s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (96.6%)

PHY-1001 : Current memory(MB): used = 530, reserve = 501, peak = 530.
PHY-1001 : End phase 2; 1.895967s wall, 2.625000s user + 0.171875s system = 2.796875s CPU (147.5%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 176440, over cnt = 25(0%), over = 25, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.013811s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (113.1%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 176160, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.030623s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (102.0%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 176208, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.021562s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (144.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1689(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.776   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -46.727  |  21   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.364988s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (98.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 1 feed throughs used by 1 nets
PHY-1001 : End commit to database; 0.168672s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (101.9%)

PHY-1001 : Current memory(MB): used = 546, reserve = 517, peak = 546.
PHY-1001 : End phase 3; 0.727945s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (100.9%)

PHY-1003 : Routed, final wirelength = 176208
PHY-1001 : Current memory(MB): used = 546, reserve = 518, peak = 546.
PHY-1001 : End export database. 0.010666s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (146.5%)

PHY-1001 : End detail routing;  7.188822s wall, 7.781250s user + 0.250000s system = 8.031250s CPU (111.7%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6186, tnet num: 1892, tinst num: 785, tnode num: 8423, tedge num: 10887.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[36] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[0] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[37] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[38] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[40] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[4] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[41] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[5] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[6] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[0] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[54] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_66.dia[2] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[55] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_66.dia[5] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[7] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[7] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_68.mi[0] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_68.mi[1] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_71.mi[0] slack -2897ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_71.mi[1] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_74.mi[0] slack -2897ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_74.mi[1] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_77.mi[0] slack -2911ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_77.mi[1] slack -2791ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_80.mi[0] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_80.mi[1] slack -2679ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_83.mi[0] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_83.mi[1] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_86.mi[0] slack -2684ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_86.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_89.mi[0] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_89.mi[1] slack -2897ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6308, tnet num: 1953, tinst num: 846, tnode num: 8545, tedge num: 11009.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_68_mi[1] slack -76ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_68_mi[0] slack -748ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_71_mi[1] slack -151ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg2_syn_74_mi[1] slack -211ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_77_mi[1] slack -620ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_77_mi[0] slack -272ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_71_mi[0] slack -438ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_74_mi[0] slack -654ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_80_mi[1] slack -353ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_80_mi[0] slack -547ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_89_mi[1] slack -246ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_83_mi[1] slack -803ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_89_mi[0] slack -582ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_86_mi[0] slack -500ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_83_mi[0] slack -834ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_86_mi[1] slack -394ps
RUN-1001 : End hold fix;  3.183702s wall, 3.218750s user + 0.234375s system = 3.453125s CPU (108.5%)

RUN-1003 : finish command "route" in  10.886475s wall, 11.546875s user + 0.515625s system = 12.062500s CPU (110.8%)

RUN-1004 : used memory is 522 MB, reserved memory is 494 MB, peak memory is 546 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      873   out of  19600    4.45%
#reg                     1019   out of  19600    5.20%
#le                      1541
  #lut only               522   out of   1541   33.87%
  #reg only               668   out of   1541   43.35%
  #lut&reg                351   out of   1541   22.78%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         464
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         101
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q0    39
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1541   |688     |185     |1052    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1031   |282     |121     |829     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |36     |32      |4       |25      |0       |0       |
|    demodu                  |Demodulation                                     |441    |101     |41      |346     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |51     |28      |6       |43      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |6       |0       |16      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |13     |6       |0       |13      |0       |0       |
|    integ                   |Integration                                      |144    |18      |15      |116     |0       |0       |
|    modu                    |Modulation                                       |65     |42      |7       |63      |0       |1       |
|    rs422                   |Rs422Output                                      |320    |72      |46      |258     |0       |4       |
|    trans                   |SquareWaveGenerator                              |25     |17      |8       |21      |0       |0       |
|  u_uart                    |UART_Control                                     |179    |132     |7       |116     |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |26     |19      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |118    |85      |0       |84      |0       |0       |
|  wendu                     |DS18B20                                          |307    |262     |45      |73      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1377  
    #2          2       296   
    #3          3       115   
    #4          4        25   
    #5        5-10       66   
    #6        11-50      31   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6308, tnet num: 1953, tinst num: 846, tnode num: 8545, tedge num: 11009.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1953 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 846
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1955, pip num: 14452
BIT-1002 : Init feedthrough completely, num: 4
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1309 valid insts, and 38618 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.157404s wall, 18.015625s user + 0.062500s system = 18.078125s CPU (572.6%)

RUN-1004 : used memory is 543 MB, reserved memory is 513 MB, peak memory is 670 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250806_152347.log"
