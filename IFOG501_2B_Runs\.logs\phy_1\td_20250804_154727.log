============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Mon Aug  4 15:47:27 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1576 instances
RUN-0007 : 371 luts, 955 seqs, 129 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2120 nets
RUN-1001 : 1545 nets have 2 pins
RUN-1001 : 472 nets have [3 - 5] pins
RUN-1001 : 61 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     251     
RUN-1001 :   No   |  No   |  Yes  |     125     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1574 instances, 371 luts, 955 seqs, 199 slices, 20 macros(199 instances: 129 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7545, tnet num: 2118, tinst num: 1574, tnode num: 10709, tedge num: 12817.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2118 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.257991s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (103.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 523269
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1574.
PHY-3001 : End clustering;  0.000027s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 447558, overlap = 20.25
PHY-3002 : Step(2): len = 421350, overlap = 18
PHY-3002 : Step(3): len = 406539, overlap = 18
PHY-3002 : Step(4): len = 397182, overlap = 13.5
PHY-3002 : Step(5): len = 381745, overlap = 13.5
PHY-3002 : Step(6): len = 369487, overlap = 13.5
PHY-3002 : Step(7): len = 361668, overlap = 13.5
PHY-3002 : Step(8): len = 353877, overlap = 15.75
PHY-3002 : Step(9): len = 341310, overlap = 15.75
PHY-3002 : Step(10): len = 335828, overlap = 13.5
PHY-3002 : Step(11): len = 326511, overlap = 13.5
PHY-3002 : Step(12): len = 316590, overlap = 13.5
PHY-3002 : Step(13): len = 308946, overlap = 13.5
PHY-3002 : Step(14): len = 304841, overlap = 13.5
PHY-3002 : Step(15): len = 291095, overlap = 15.75
PHY-3002 : Step(16): len = 285418, overlap = 15.75
PHY-3002 : Step(17): len = 280157, overlap = 15.75
PHY-3002 : Step(18): len = 274194, overlap = 15.75
PHY-3002 : Step(19): len = 264470, overlap = 13.5
PHY-3002 : Step(20): len = 260723, overlap = 13.5
PHY-3002 : Step(21): len = 255473, overlap = 13.5
PHY-3002 : Step(22): len = 248848, overlap = 20.25
PHY-3002 : Step(23): len = 239511, overlap = 20.25
PHY-3002 : Step(24): len = 236549, overlap = 20.25
PHY-3002 : Step(25): len = 230607, overlap = 20.25
PHY-3002 : Step(26): len = 222038, overlap = 20.25
PHY-3002 : Step(27): len = 216225, overlap = 20.25
PHY-3002 : Step(28): len = 213966, overlap = 20.25
PHY-3002 : Step(29): len = 203087, overlap = 20.25
PHY-3002 : Step(30): len = 192705, overlap = 20.25
PHY-3002 : Step(31): len = 188917, overlap = 20.25
PHY-3002 : Step(32): len = 186439, overlap = 20.25
PHY-3002 : Step(33): len = 142106, overlap = 18
PHY-3002 : Step(34): len = 135258, overlap = 20.25
PHY-3002 : Step(35): len = 133835, overlap = 20.25
PHY-3002 : Step(36): len = 126283, overlap = 18
PHY-3002 : Step(37): len = 121093, overlap = 20.25
PHY-3002 : Step(38): len = 118585, overlap = 20.25
PHY-3002 : Step(39): len = 116279, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.08177e-05
PHY-3002 : Step(40): len = 116508, overlap = 13.5
PHY-3002 : Step(41): len = 115718, overlap = 15.75
PHY-3002 : Step(42): len = 114644, overlap = 15.75
PHY-3002 : Step(43): len = 114193, overlap = 11.25
PHY-3002 : Step(44): len = 111528, overlap = 13.5
PHY-3002 : Step(45): len = 106887, overlap = 13.5
PHY-3002 : Step(46): len = 105935, overlap = 11.25
PHY-3002 : Step(47): len = 104335, overlap = 11.25
PHY-3002 : Step(48): len = 101649, overlap = 15.75
PHY-3002 : Step(49): len = 99407.9, overlap = 15.75
PHY-3002 : Step(50): len = 97668.6, overlap = 15.75
PHY-3002 : Step(51): len = 96599.5, overlap = 15.75
PHY-3002 : Step(52): len = 94093.2, overlap = 15.75
PHY-3002 : Step(53): len = 92408.3, overlap = 15.75
PHY-3002 : Step(54): len = 90554.7, overlap = 15.75
PHY-3002 : Step(55): len = 89448.4, overlap = 15.75
PHY-3002 : Step(56): len = 86944.6, overlap = 18
PHY-3002 : Step(57): len = 85455, overlap = 18
PHY-3002 : Step(58): len = 84010.5, overlap = 18
PHY-3002 : Step(59): len = 81098.5, overlap = 15.75
PHY-3002 : Step(60): len = 79257.5, overlap = 15.75
PHY-3002 : Step(61): len = 78960.8, overlap = 15.75
PHY-3002 : Step(62): len = 76464.3, overlap = 18
PHY-3002 : Step(63): len = 74801, overlap = 18
PHY-3002 : Step(64): len = 72982.5, overlap = 15.8125
PHY-3002 : Step(65): len = 71111.3, overlap = 17.1875
PHY-3002 : Step(66): len = 70127, overlap = 17.3125
PHY-3002 : Step(67): len = 68713.9, overlap = 15.75
PHY-3002 : Step(68): len = 66683.8, overlap = 17.75
PHY-3002 : Step(69): len = 65952.6, overlap = 18
PHY-3002 : Step(70): len = 64991.2, overlap = 18.1875
PHY-3002 : Step(71): len = 64236.1, overlap = 17.9375
PHY-3002 : Step(72): len = 63329.1, overlap = 18.125
PHY-3002 : Step(73): len = 62355.2, overlap = 17.75
PHY-3002 : Step(74): len = 62119.4, overlap = 19.8125
PHY-3002 : Step(75): len = 61411.9, overlap = 19.8125
PHY-3002 : Step(76): len = 61197.5, overlap = 17.4375
PHY-3002 : Step(77): len = 61097.3, overlap = 17.1875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000181635
PHY-3002 : Step(78): len = 61229.3, overlap = 14.9375
PHY-3002 : Step(79): len = 61310.9, overlap = 17.3125
PHY-3002 : Step(80): len = 61332.5, overlap = 17.3125
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000363271
PHY-3002 : Step(81): len = 61641.9, overlap = 15.0625
PHY-3002 : Step(82): len = 62012.9, overlap = 15.0625
PHY-3002 : Step(83): len = 62279.8, overlap = 17.3125
PHY-3002 : Step(84): len = 61292.2, overlap = 15.125
PHY-3002 : Step(85): len = 61161.5, overlap = 15.125
PHY-3002 : Step(86): len = 61169.4, overlap = 15.125
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.000726542
PHY-3002 : Step(87): len = 61346.6, overlap = 15.125
PHY-3002 : Step(88): len = 61351.6, overlap = 15.125
PHY-3002 : Step(89): len = 61344.3, overlap = 15.125
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.00145308
PHY-3002 : Step(90): len = 61424.5, overlap = 15.125
PHY-3002 : Step(91): len = 61410.6, overlap = 15.125
PHY-3001 : Before Legalized: Len = 61410.6
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005320s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (293.7%)

PHY-3001 : After Legalized: Len = 64615.1, Over = 1.625
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2118 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.063429s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(92): len = 64489.3, overlap = 7.46875
PHY-3002 : Step(93): len = 63773.7, overlap = 7.25
PHY-3002 : Step(94): len = 62794.5, overlap = 7.25
PHY-3002 : Step(95): len = 62135.8, overlap = 7.4375
PHY-3002 : Step(96): len = 61179.8, overlap = 7.53125
PHY-3002 : Step(97): len = 60057.9, overlap = 9.34375
PHY-3002 : Step(98): len = 59363.9, overlap = 8.40625
PHY-3002 : Step(99): len = 58803.8, overlap = 9.34375
PHY-3002 : Step(100): len = 58268.5, overlap = 9.21875
PHY-3002 : Step(101): len = 57572.5, overlap = 9.21875
PHY-3002 : Step(102): len = 56923.9, overlap = 9.59375
PHY-3002 : Step(103): len = 56284.5, overlap = 9.5625
PHY-3002 : Step(104): len = 55833.9, overlap = 10.6875
PHY-3002 : Step(105): len = 55462.8, overlap = 10.625
PHY-3002 : Step(106): len = 55060.9, overlap = 10.8125
PHY-3002 : Step(107): len = 54767.2, overlap = 11.4062
PHY-3002 : Step(108): len = 54558.8, overlap = 10.7812
PHY-3002 : Step(109): len = 54142.2, overlap = 10.5312
PHY-3002 : Step(110): len = 53646.1, overlap = 10.2812
PHY-3002 : Step(111): len = 52930.4, overlap = 10.1562
PHY-3002 : Step(112): len = 52840, overlap = 9.125
PHY-3002 : Step(113): len = 52561.4, overlap = 9.1875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000497686
PHY-3002 : Step(114): len = 52425.9, overlap = 9.15625
PHY-3002 : Step(115): len = 52473.5, overlap = 7.8125
PHY-3002 : Step(116): len = 52500.2, overlap = 7.75
PHY-3002 : Step(117): len = 52603.8, overlap = 7.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000995373
PHY-3002 : Step(118): len = 52451.2, overlap = 7.5
PHY-3002 : Step(119): len = 52091.5, overlap = 7.625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2118 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.059878s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000115301
PHY-3002 : Step(120): len = 52347.6, overlap = 51.625
PHY-3002 : Step(121): len = 53081, overlap = 49.4062
PHY-3002 : Step(122): len = 53538.8, overlap = 49.6562
PHY-3002 : Step(123): len = 53173.5, overlap = 45.4375
PHY-3002 : Step(124): len = 52846.8, overlap = 45.0938
PHY-3002 : Step(125): len = 52665, overlap = 45.9688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000230602
PHY-3002 : Step(126): len = 52733.2, overlap = 45.125
PHY-3002 : Step(127): len = 53013.9, overlap = 44.7188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000412699
PHY-3002 : Step(128): len = 53396.7, overlap = 43.25
PHY-3002 : Step(129): len = 55721.7, overlap = 34.9688
PHY-3002 : Step(130): len = 56304.2, overlap = 34.0625
PHY-3002 : Step(131): len = 55986.1, overlap = 30.75
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7545, tnet num: 2118, tinst num: 1574, tnode num: 10709, tedge num: 12817.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 89.00 peak overflow 2.62
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2120.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58888, over cnt = 231(0%), over = 978, worst = 22
PHY-1001 : End global iterations;  0.068347s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (137.2%)

PHY-1001 : Congestion index: top1 = 43.69, top5 = 24.99, top10 = 16.37, top15 = 11.76.
PHY-1001 : End incremental global routing;  0.120079s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (104.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2118 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.077778s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (100.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.230658s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (101.6%)

OPT-1001 : Current memory(MB): used = 209, reserve = 176, peak = 209.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1637/2120.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58888, over cnt = 231(0%), over = 978, worst = 22
PHY-1002 : len = 65504, over cnt = 138(0%), over = 383, worst = 19
PHY-1002 : len = 69744, over cnt = 49(0%), over = 98, worst = 9
PHY-1002 : len = 70576, over cnt = 11(0%), over = 12, worst = 2
PHY-1002 : len = 71056, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.087816s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (142.3%)

PHY-1001 : Congestion index: top1 = 37.09, top5 = 24.69, top10 = 18.04, top15 = 13.48.
OPT-1001 : End congestion update;  0.136199s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (126.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2118 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.070912s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (110.2%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.210623s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (118.7%)

OPT-1001 : Current memory(MB): used = 213, reserve = 180, peak = 213.
OPT-1001 : End physical optimization;  0.696379s wall, 0.796875s user + 0.015625s system = 0.812500s CPU (116.7%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 371 LUT to BLE ...
SYN-4008 : Packed 371 LUT and 190 SEQ to BLE.
SYN-4003 : Packing 765 remaining SEQ's ...
SYN-4005 : Packed 86 SEQ with LUT/SLICE
SYN-4006 : 111 single LUT's are left
SYN-4006 : 679 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1050/1364 primitive instances ...
PHY-3001 : End packing;  0.055775s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (112.1%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 805 instances
RUN-1001 : 377 mslices, 377 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1938 nets
RUN-1001 : 1368 nets have 2 pins
RUN-1001 : 466 nets have [3 - 5] pins
RUN-1001 : 62 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 803 instances, 754 slices, 20 macros(199 instances: 129 mslices 70 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 55773, Over = 53
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6341, tnet num: 1936, tinst num: 803, tnode num: 8649, tedge num: 11224.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1936 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.324307s wall, 0.312500s user + 0.015625s system = 0.328125s CPU (101.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.93809e-05
PHY-3002 : Step(132): len = 55210.7, overlap = 57
PHY-3002 : Step(133): len = 54477, overlap = 58.5
PHY-3002 : Step(134): len = 54009.5, overlap = 61.25
PHY-3002 : Step(135): len = 54087.4, overlap = 58.5
PHY-3002 : Step(136): len = 53981.6, overlap = 60
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.87618e-05
PHY-3002 : Step(137): len = 53974.2, overlap = 58.75
PHY-3002 : Step(138): len = 54356.6, overlap = 54.75
PHY-3002 : Step(139): len = 54847.2, overlap = 53.25
PHY-3002 : Step(140): len = 55157.4, overlap = 51.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000157524
PHY-3002 : Step(141): len = 55631.1, overlap = 47.5
PHY-3002 : Step(142): len = 56295.9, overlap = 45.75
PHY-3001 : Before Legalized: Len = 56295.9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.086295s wall, 0.093750s user + 0.125000s system = 0.218750s CPU (253.5%)

PHY-3001 : After Legalized: Len = 68814.2, Over = 0
PHY-3001 : Trial Legalized: Len = 68814.2
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1936 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.063992s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (97.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000916793
PHY-3002 : Step(143): len = 64750.7, overlap = 8
PHY-3002 : Step(144): len = 63305.2, overlap = 13.75
PHY-3002 : Step(145): len = 61562.1, overlap = 14.25
PHY-3002 : Step(146): len = 60847.6, overlap = 14.25
PHY-3002 : Step(147): len = 60258.6, overlap = 16.75
PHY-3002 : Step(148): len = 59766.6, overlap = 18
PHY-3002 : Step(149): len = 59396.1, overlap = 19.75
PHY-3002 : Step(150): len = 59159, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00169622
PHY-3002 : Step(151): len = 59546.7, overlap = 20.75
PHY-3002 : Step(152): len = 59687.7, overlap = 20.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00339244
PHY-3002 : Step(153): len = 59806.1, overlap = 19.5
PHY-3002 : Step(154): len = 59851.2, overlap = 18.75
PHY-3001 : Before Legalized: Len = 59851.2
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006357s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (245.8%)

PHY-3001 : After Legalized: Len = 63641, Over = 0
PHY-3001 : Legalized: Len = 63641, Over = 0
PHY-3001 : Spreading special nets. 7 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.007396s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (211.3%)

PHY-3001 : 11 instances has been re-located, deltaX = 0, deltaY = 12, maxDist = 2.
PHY-3001 : Final: Len = 63787, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6341, tnet num: 1936, tinst num: 803, tnode num: 8649, tedge num: 11224.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 34/1938.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70000, over cnt = 144(0%), over = 228, worst = 7
PHY-1002 : len = 71120, over cnt = 77(0%), over = 94, worst = 4
PHY-1002 : len = 72016, over cnt = 19(0%), over = 24, worst = 4
PHY-1002 : len = 72328, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 72424, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.134213s wall, 0.140625s user + 0.046875s system = 0.187500s CPU (139.7%)

PHY-1001 : Congestion index: top1 = 31.12, top5 = 23.08, top10 = 17.69, top15 = 13.79.
PHY-1001 : End incremental global routing;  0.187832s wall, 0.203125s user + 0.046875s system = 0.250000s CPU (133.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1936 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.062983s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.281578s wall, 0.296875s user + 0.046875s system = 0.343750s CPU (122.1%)

OPT-1001 : Current memory(MB): used = 214, reserve = 183, peak = 216.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1702/1938.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72424, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005302s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.12, top5 = 23.08, top10 = 17.69, top15 = 13.79.
OPT-1001 : End congestion update;  0.054452s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (86.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1936 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049783s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.2%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 763 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 803 instances, 754 slices, 20 macros(199 instances: 129 mslices 70 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 63802, Over = 0
PHY-3001 : End spreading;  0.004692s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (333.0%)

PHY-3001 : Final: Len = 63802, Over = 0
PHY-3001 : End incremental legalization;  0.034930s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (89.5%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.153814s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (101.6%)

OPT-1001 : Current memory(MB): used = 219, reserve = 187, peak = 219.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1936 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047911s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.8%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1698/1938.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72424, over cnt = 1(0%), over = 2, worst = 2
PHY-1002 : len = 72424, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72440, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.024687s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (63.3%)

PHY-1001 : Congestion index: top1 = 31.16, top5 = 23.06, top10 = 17.70, top15 = 13.79.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1936 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048568s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (96.5%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.689655
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.895440s wall, 0.859375s user + 0.093750s system = 0.953125s CPU (106.4%)

RUN-1003 : finish command "place" in  5.371727s wall, 7.062500s user + 3.281250s system = 10.343750s CPU (192.6%)

RUN-1004 : used memory is 197 MB, reserved memory is 164 MB, peak memory is 219 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 805 instances
RUN-1001 : 377 mslices, 377 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1938 nets
RUN-1001 : 1368 nets have 2 pins
RUN-1001 : 466 nets have [3 - 5] pins
RUN-1001 : 62 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6341, tnet num: 1936, tinst num: 803, tnode num: 8649, tedge num: 11224.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 377 mslices, 377 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1936 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69392, over cnt = 138(0%), over = 221, worst = 7
PHY-1002 : len = 70480, over cnt = 80(0%), over = 98, worst = 4
PHY-1002 : len = 71656, over cnt = 8(0%), over = 9, worst = 2
PHY-1002 : len = 71832, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.117123s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (146.7%)

PHY-1001 : Congestion index: top1 = 30.99, top5 = 23.06, top10 = 17.66, top15 = 13.75.
PHY-1001 : End global routing;  0.166351s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (131.5%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 236, reserve = 204, peak = 236.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 494, reserve = 465, peak = 494.
PHY-1001 : End build detailed router design. 3.471713s wall, 3.406250s user + 0.031250s system = 3.437500s CPU (99.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 30104, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.108978s wall, 1.093750s user + 0.015625s system = 1.109375s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 526, reserve = 499, peak = 526.
PHY-1001 : End phase 1; 1.115749s wall, 1.093750s user + 0.015625s system = 1.109375s CPU (99.4%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 179056, over cnt = 38(0%), over = 38, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 528, reserve = 500, peak = 528.
PHY-1001 : End initial routed; 1.643585s wall, 2.359375s user + 0.125000s system = 2.484375s CPU (151.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1720(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.776   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.959   |  -45.840  |  19   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.360399s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 530, reserve = 501, peak = 530.
PHY-1001 : End phase 2; 2.004080s wall, 2.718750s user + 0.125000s system = 2.843750s CPU (141.9%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 179056, over cnt = 38(0%), over = 38, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014820s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (105.4%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 178872, over cnt = 6(0%), over = 6, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.041870s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (112.0%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 178936, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.021975s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (142.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1720(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.776   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.959   |  -45.840  |  19   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.372804s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (100.6%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 7 feed throughs used by 6 nets
PHY-1001 : End commit to database; 0.186464s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (100.6%)

PHY-1001 : Current memory(MB): used = 544, reserve = 516, peak = 544.
PHY-1001 : End phase 3; 0.778198s wall, 0.765625s user + 0.015625s system = 0.781250s CPU (100.4%)

PHY-1003 : Routed, final wirelength = 178936
PHY-1001 : Current memory(MB): used = 544, reserve = 516, peak = 544.
PHY-1001 : End export database. 0.010657s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  7.562724s wall, 8.171875s user + 0.187500s system = 8.359375s CPU (110.5%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6341, tnet num: 1936, tinst num: 803, tnode num: 8649, tedge num: 11224.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[3] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[4] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[2] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_52.mi[0] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_52.mi[1] slack -2959ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_55.mi[0] slack -2827ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_55.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_58.mi[0] slack -2827ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_58.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_61.mi[0] slack -2719ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_61.mi[1] slack -2947ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_64.mi[0] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_64.mi[1] slack -2959ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_67.mi[0] slack -2719ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_67.mi[1] slack -2947ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_70.mi[0] slack -2947ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_70.mi[1] slack -2947ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_73.mi[0] slack -2959ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_73.mi[1] slack -2719ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6445, tnet num: 1988, tinst num: 855, tnode num: 8753, tedge num: 11328.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_73_mi[0] slack -853ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_73_mi[1] slack -478ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_61_mi[0] slack -494ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_58_mi[1] slack -615ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_58_mi[0] slack -322ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_61_mi[1] slack -304ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_64_mi[0] slack -423ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_64_mi[1] slack -608ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_55_mi[0] slack -500ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_52_mi[0] slack -528ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_70_mi[0] slack -390ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_55_mi[1] slack -375ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_70_mi[1] slack -893ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_52_mi[1] slack -247ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg2_syn_67_mi[0] slack -522ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_67_mi[1] slack -441ps
RUN-1001 : End hold fix;  3.236916s wall, 3.296875s user + 0.250000s system = 3.546875s CPU (109.6%)

RUN-1003 : finish command "route" in  11.304294s wall, 12.015625s user + 0.453125s system = 12.468750s CPU (110.3%)

RUN-1004 : used memory is 499 MB, reserved memory is 471 MB, peak memory is 544 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      879   out of  19600    4.48%
#reg                     1053   out of  19600    5.37%
#le                      1558
  #lut only               505   out of   1558   32.41%
  #reg only               679   out of   1558   43.58%
  #lut&reg                374   out of   1558   24.01%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                                               Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0                                484
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3                                100
#3        wendu/clk_us                    GCLK               lslice             signal_process/ctrl_signal/modulate_reg_syn_34.q0    43
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4                                1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di                                      1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1558   |680     |199     |1086    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1049   |275     |135     |863     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |28     |24      |4       |21      |0       |0       |
|    demodu                  |Demodulation                                     |431    |93      |41      |353     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |55     |39      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |12      |0       |16      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |13     |7       |0       |13      |0       |0       |
|    integ                   |Integration                                      |135    |24      |15      |107     |0       |0       |
|    modu                    |Modulation                                       |102    |38      |21      |98      |0       |1       |
|    rs422                   |Rs422Output                                      |317    |68      |46      |262     |0       |4       |
|    trans                   |SquareWaveGenerator                              |36     |28      |8       |22      |0       |0       |
|  u_uart                    |UART_Control                                     |178    |131     |7       |118     |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |25     |18      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |116    |85      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |307    |262     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1381  
    #2          2       329   
    #3          3       110   
    #4          4        27   
    #5        5-10       66   
    #6        11-50      32   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6445, tnet num: 1988, tinst num: 855, tnode num: 8753, tedge num: 11328.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1988 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 855
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1990, pip num: 14747
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 9
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1291 valid insts, and 39415 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  4.194937s wall, 23.921875s user + 0.046875s system = 23.968750s CPU (571.4%)

RUN-1004 : used memory is 518 MB, reserved memory is 493 MB, peak memory is 664 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250804_154727.log"
