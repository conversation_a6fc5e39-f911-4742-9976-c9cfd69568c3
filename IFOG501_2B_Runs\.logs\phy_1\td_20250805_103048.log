============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Tue Aug  5 10:30:48 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1586 instances
RUN-0007 : 377 luts, 966 seqs, 122 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2119 nets
RUN-1001 : 1541 nets have 2 pins
RUN-1001 : 477 nets have [3 - 5] pins
RUN-1001 : 59 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     263     
RUN-1001 :   No   |  No   |  Yes  |     124     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1584 instances, 377 luts, 966 seqs, 192 slices, 19 macros(192 instances: 122 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7554, tnet num: 2117, tinst num: 1584, tnode num: 10723, tedge num: 12800.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2117 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.257468s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (97.1%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 531657
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1584.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 453827, overlap = 20.25
PHY-3002 : Step(2): len = 429931, overlap = 20.25
PHY-3002 : Step(3): len = 413713, overlap = 18
PHY-3002 : Step(4): len = 404320, overlap = 13.5
PHY-3002 : Step(5): len = 390997, overlap = 11.25
PHY-3002 : Step(6): len = 378825, overlap = 13.5
PHY-3002 : Step(7): len = 370760, overlap = 11.25
PHY-3002 : Step(8): len = 361989, overlap = 18
PHY-3002 : Step(9): len = 350933, overlap = 15.75
PHY-3002 : Step(10): len = 343699, overlap = 13.5
PHY-3002 : Step(11): len = 337081, overlap = 13.5
PHY-3002 : Step(12): len = 327353, overlap = 13.5
PHY-3002 : Step(13): len = 319108, overlap = 15.75
PHY-3002 : Step(14): len = 313501, overlap = 15.75
PHY-3002 : Step(15): len = 305386, overlap = 15.75
PHY-3002 : Step(16): len = 297120, overlap = 15.75
PHY-3002 : Step(17): len = 291681, overlap = 15.75
PHY-3002 : Step(18): len = 286134, overlap = 15.75
PHY-3002 : Step(19): len = 279050, overlap = 13.5
PHY-3002 : Step(20): len = 273404, overlap = 13.5
PHY-3002 : Step(21): len = 268859, overlap = 13.5
PHY-3002 : Step(22): len = 260860, overlap = 13.5
PHY-3002 : Step(23): len = 254452, overlap = 13.5
PHY-3002 : Step(24): len = 250839, overlap = 13.5
PHY-3002 : Step(25): len = 245609, overlap = 18
PHY-3002 : Step(26): len = 233556, overlap = 20.25
PHY-3002 : Step(27): len = 228643, overlap = 20.25
PHY-3002 : Step(28): len = 226010, overlap = 20.25
PHY-3002 : Step(29): len = 216963, overlap = 20.25
PHY-3002 : Step(30): len = 202064, overlap = 20.25
PHY-3002 : Step(31): len = 198899, overlap = 20.25
PHY-3002 : Step(32): len = 195303, overlap = 20.25
PHY-3002 : Step(33): len = 128770, overlap = 18
PHY-3002 : Step(34): len = 124099, overlap = 20.25
PHY-3002 : Step(35): len = 123077, overlap = 20.25
PHY-3002 : Step(36): len = 117703, overlap = 18
PHY-3002 : Step(37): len = 112660, overlap = 20.25
PHY-3002 : Step(38): len = 109930, overlap = 20.25
PHY-3002 : Step(39): len = 108074, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.88895e-05
PHY-3002 : Step(40): len = 107891, overlap = 13.5
PHY-3002 : Step(41): len = 107378, overlap = 15.75
PHY-3002 : Step(42): len = 106332, overlap = 18
PHY-3002 : Step(43): len = 105541, overlap = 13.5
PHY-3002 : Step(44): len = 102530, overlap = 13.5
PHY-3002 : Step(45): len = 99178.5, overlap = 15.75
PHY-3002 : Step(46): len = 96933.7, overlap = 15.75
PHY-3002 : Step(47): len = 96145.6, overlap = 15.75
PHY-3002 : Step(48): len = 93882.3, overlap = 15.75
PHY-3002 : Step(49): len = 91699, overlap = 15.75
PHY-3002 : Step(50): len = 89687.4, overlap = 15.75
PHY-3002 : Step(51): len = 88990.8, overlap = 13.5
PHY-3002 : Step(52): len = 87371.7, overlap = 13.5
PHY-3002 : Step(53): len = 85443.8, overlap = 13.5
PHY-3002 : Step(54): len = 84812.6, overlap = 15.75
PHY-3002 : Step(55): len = 83720, overlap = 15.75
PHY-3002 : Step(56): len = 82806.6, overlap = 18
PHY-3002 : Step(57): len = 80382.3, overlap = 18
PHY-3002 : Step(58): len = 79978.1, overlap = 18
PHY-3002 : Step(59): len = 78226.9, overlap = 15.75
PHY-3002 : Step(60): len = 76344.5, overlap = 13.5
PHY-3002 : Step(61): len = 74645.5, overlap = 13.5
PHY-3002 : Step(62): len = 74501.3, overlap = 13.5
PHY-3002 : Step(63): len = 72670.6, overlap = 15.75
PHY-3002 : Step(64): len = 70405.9, overlap = 18
PHY-3002 : Step(65): len = 70302.9, overlap = 18
PHY-3002 : Step(66): len = 69142.5, overlap = 15.75
PHY-3002 : Step(67): len = 67740.8, overlap = 11.5
PHY-3002 : Step(68): len = 67927.8, overlap = 14.0625
PHY-3002 : Step(69): len = 67108.7, overlap = 14.4375
PHY-3002 : Step(70): len = 66156.2, overlap = 15.1875
PHY-3002 : Step(71): len = 66029.1, overlap = 15.5625
PHY-3002 : Step(72): len = 65246.4, overlap = 15.625
PHY-3002 : Step(73): len = 64115.3, overlap = 15.625
PHY-3002 : Step(74): len = 63801.4, overlap = 16.0625
PHY-3002 : Step(75): len = 63004.2, overlap = 15.875
PHY-3002 : Step(76): len = 62541.8, overlap = 15.6875
PHY-3002 : Step(77): len = 62423.4, overlap = 15.6875
PHY-3002 : Step(78): len = 61911.5, overlap = 13.1875
PHY-3002 : Step(79): len = 61524.7, overlap = 13.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000157779
PHY-3002 : Step(80): len = 61962.1, overlap = 13.125
PHY-3002 : Step(81): len = 62179.2, overlap = 13.125
PHY-3002 : Step(82): len = 62219.6, overlap = 13.125
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000315558
PHY-3002 : Step(83): len = 62265.2, overlap = 13.125
PHY-3002 : Step(84): len = 62304.2, overlap = 13.125
PHY-3001 : Before Legalized: Len = 62304.2
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007054s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 66701.7, Over = 1.875
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2117 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.060427s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (103.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(85): len = 66745.5, overlap = 6.375
PHY-3002 : Step(86): len = 65662.6, overlap = 5.6875
PHY-3002 : Step(87): len = 64665.1, overlap = 5.4375
PHY-3002 : Step(88): len = 63647.7, overlap = 5.6875
PHY-3002 : Step(89): len = 62824.4, overlap = 5.6875
PHY-3002 : Step(90): len = 61359.9, overlap = 5.25
PHY-3002 : Step(91): len = 60143.4, overlap = 5.6875
PHY-3002 : Step(92): len = 58982.6, overlap = 6.5
PHY-3002 : Step(93): len = 58508.7, overlap = 7.28125
PHY-3002 : Step(94): len = 57781, overlap = 7.28125
PHY-3002 : Step(95): len = 57068.8, overlap = 7.28125
PHY-3002 : Step(96): len = 56409.6, overlap = 8.46875
PHY-3002 : Step(97): len = 55970.2, overlap = 9.15625
PHY-3002 : Step(98): len = 55308.3, overlap = 9.15625
PHY-3002 : Step(99): len = 54448, overlap = 9.65625
PHY-3002 : Step(100): len = 53429.8, overlap = 9.71875
PHY-3002 : Step(101): len = 52863.4, overlap = 12.4688
PHY-3002 : Step(102): len = 52263.2, overlap = 12.7812
PHY-3002 : Step(103): len = 52083.7, overlap = 15.6562
PHY-3002 : Step(104): len = 51872.3, overlap = 15.7188
PHY-3002 : Step(105): len = 51573.6, overlap = 16.125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000295936
PHY-3002 : Step(106): len = 51606.9, overlap = 13.4375
PHY-3002 : Step(107): len = 51324.8, overlap = 13
PHY-3002 : Step(108): len = 51434.6, overlap = 13
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000591872
PHY-3002 : Step(109): len = 51413.1, overlap = 13.0625
PHY-3002 : Step(110): len = 51512.9, overlap = 13.0312
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2117 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.070817s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (110.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.59186e-05
PHY-3002 : Step(111): len = 51370.8, overlap = 58.2188
PHY-3002 : Step(112): len = 52732.8, overlap = 53.6875
PHY-3002 : Step(113): len = 53223.6, overlap = 51.2188
PHY-3002 : Step(114): len = 52733.4, overlap = 51.0625
PHY-3002 : Step(115): len = 52458.2, overlap = 50.6875
PHY-3002 : Step(116): len = 52469.1, overlap = 50.3125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000171837
PHY-3002 : Step(117): len = 52544, overlap = 49.6875
PHY-3002 : Step(118): len = 53028.3, overlap = 47.8125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000343675
PHY-3002 : Step(119): len = 53068.3, overlap = 46.4375
PHY-3002 : Step(120): len = 55164.5, overlap = 39.25
PHY-3002 : Step(121): len = 55163.8, overlap = 37.6875
PHY-3002 : Step(122): len = 55227.2, overlap = 36.6562
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7554, tnet num: 2117, tinst num: 1584, tnode num: 10723, tedge num: 12800.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 85.97 peak overflow 2.62
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2119.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 57312, over cnt = 238(0%), over = 1048, worst = 19
PHY-1001 : End global iterations;  0.063317s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (172.7%)

PHY-1001 : Congestion index: top1 = 43.81, top5 = 25.18, top10 = 16.10, top15 = 11.43.
PHY-1001 : End incremental global routing;  0.113986s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (137.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2117 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067043s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (116.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.210258s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (126.3%)

OPT-1001 : Current memory(MB): used = 209, reserve = 176, peak = 209.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1601/2119.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 57312, over cnt = 238(0%), over = 1048, worst = 19
PHY-1002 : len = 63728, over cnt = 167(0%), over = 477, worst = 15
PHY-1002 : len = 68248, over cnt = 58(0%), over = 143, worst = 12
PHY-1002 : len = 69552, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 70096, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.101072s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (139.1%)

PHY-1001 : Congestion index: top1 = 38.08, top5 = 25.05, top10 = 18.09, top15 = 13.39.
OPT-1001 : End congestion update;  0.144854s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (118.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2117 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057069s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (109.5%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.205147s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (114.2%)

OPT-1001 : Current memory(MB): used = 212, reserve = 179, peak = 212.
OPT-1001 : End physical optimization;  0.661159s wall, 0.703125s user + 0.031250s system = 0.734375s CPU (111.1%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 377 LUT to BLE ...
SYN-4008 : Packed 377 LUT and 191 SEQ to BLE.
SYN-4003 : Packing 775 remaining SEQ's ...
SYN-4005 : Packed 81 SEQ with LUT/SLICE
SYN-4006 : 121 single LUT's are left
SYN-4006 : 694 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1071/1378 primitive instances ...
PHY-3001 : End packing;  0.051854s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (90.4%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 808 instances
RUN-1001 : 378 mslices, 379 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1936 nets
RUN-1001 : 1363 nets have 2 pins
RUN-1001 : 469 nets have [3 - 5] pins
RUN-1001 : 61 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 806 instances, 757 slices, 19 macros(192 instances: 122 mslices 70 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 55008.8, Over = 58.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6335, tnet num: 1934, tinst num: 806, tnode num: 8635, tedge num: 11190.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.281632s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.45705e-05
PHY-3002 : Step(123): len = 54521.1, overlap = 60.5
PHY-3002 : Step(124): len = 53968.3, overlap = 62
PHY-3002 : Step(125): len = 53441.2, overlap = 63.75
PHY-3002 : Step(126): len = 53422.1, overlap = 63.75
PHY-3002 : Step(127): len = 53588.7, overlap = 62.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.91409e-05
PHY-3002 : Step(128): len = 53763.4, overlap = 62.25
PHY-3002 : Step(129): len = 54348.4, overlap = 58.75
PHY-3002 : Step(130): len = 54823.9, overlap = 58
PHY-3002 : Step(131): len = 55077.3, overlap = 56
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000138282
PHY-3002 : Step(132): len = 55484.9, overlap = 54.5
PHY-3002 : Step(133): len = 56037.3, overlap = 53.25
PHY-3001 : Before Legalized: Len = 56037.3
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.072598s wall, 0.078125s user + 0.109375s system = 0.187500s CPU (258.3%)

PHY-3001 : After Legalized: Len = 69458.3, Over = 0
PHY-3001 : Trial Legalized: Len = 69458.3
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.047905s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.0013262
PHY-3002 : Step(134): len = 65795.9, overlap = 6.5
PHY-3002 : Step(135): len = 64562.2, overlap = 11
PHY-3002 : Step(136): len = 62894.1, overlap = 13.5
PHY-3002 : Step(137): len = 62137.9, overlap = 15.5
PHY-3002 : Step(138): len = 61045.2, overlap = 17.25
PHY-3002 : Step(139): len = 60590, overlap = 17.75
PHY-3002 : Step(140): len = 60070.4, overlap = 20.25
PHY-3002 : Step(141): len = 59828.5, overlap = 21.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00235822
PHY-3002 : Step(142): len = 60046.1, overlap = 21.75
PHY-3002 : Step(143): len = 60079.4, overlap = 23
PHY-3002 : Step(144): len = 59986.9, overlap = 22.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00471644
PHY-3002 : Step(145): len = 60083.5, overlap = 23
PHY-3002 : Step(146): len = 60171.4, overlap = 23
PHY-3001 : Before Legalized: Len = 60171.4
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005221s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 63984.6, Over = 0
PHY-3001 : Legalized: Len = 63984.6, Over = 0
PHY-3001 : Spreading special nets. 6 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005177s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (301.8%)

PHY-3001 : 11 instances has been re-located, deltaX = 2, deltaY = 9, maxDist = 1.
PHY-3001 : Final: Len = 63996.6, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6335, tnet num: 1934, tinst num: 806, tnode num: 8635, tedge num: 11190.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 37/1936.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70104, over cnt = 151(0%), over = 231, worst = 7
PHY-1002 : len = 71136, over cnt = 86(0%), over = 109, worst = 3
PHY-1002 : len = 72080, over cnt = 31(0%), over = 37, worst = 2
PHY-1002 : len = 72520, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 72552, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.118140s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (119.0%)

PHY-1001 : Congestion index: top1 = 32.44, top5 = 23.35, top10 = 17.81, top15 = 13.84.
PHY-1001 : End incremental global routing;  0.170021s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (119.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059007s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.9%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.257791s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (115.2%)

OPT-1001 : Current memory(MB): used = 217, reserve = 184, peak = 217.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1709/1936.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72552, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006319s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.44, top5 = 23.35, top10 = 17.81, top15 = 13.84.
OPT-1001 : End congestion update;  0.053231s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048600s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (96.5%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 766 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 806 instances, 757 slices, 19 macros(192 instances: 122 mslices 70 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 64052.8, Over = 0
PHY-3001 : End spreading;  0.004677s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (334.1%)

PHY-3001 : Final: Len = 64052.8, Over = 0
PHY-3001 : End incremental legalization;  0.033992s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (91.9%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 0 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.148805s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (94.5%)

OPT-1001 : Current memory(MB): used = 221, reserve = 189, peak = 222.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047294s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (99.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1705/1936.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72584, over cnt = 1(0%), over = 2, worst = 2
PHY-1002 : len = 72600, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.015069s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (103.7%)

PHY-1001 : Congestion index: top1 = 32.41, top5 = 23.35, top10 = 17.82, top15 = 13.85.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050066s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (124.8%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 32.034483
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.857544s wall, 0.875000s user + 0.015625s system = 0.890625s CPU (103.9%)

RUN-1003 : finish command "place" in  4.890291s wall, 6.828125s user + 2.609375s system = 9.437500s CPU (193.0%)

RUN-1004 : used memory is 198 MB, reserved memory is 165 MB, peak memory is 222 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 808 instances
RUN-1001 : 378 mslices, 379 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1936 nets
RUN-1001 : 1363 nets have 2 pins
RUN-1001 : 469 nets have [3 - 5] pins
RUN-1001 : 61 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6335, tnet num: 1934, tinst num: 806, tnode num: 8635, tedge num: 11190.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 378 mslices, 379 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69736, over cnt = 145(0%), over = 227, worst = 7
PHY-1002 : len = 70824, over cnt = 86(0%), over = 107, worst = 4
PHY-1002 : len = 71520, over cnt = 42(0%), over = 49, worst = 4
PHY-1002 : len = 72184, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.113518s wall, 0.140625s user + 0.031250s system = 0.171875s CPU (151.4%)

PHY-1001 : Congestion index: top1 = 32.48, top5 = 23.11, top10 = 17.66, top15 = 13.75.
PHY-1001 : End global routing;  0.164712s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (132.8%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 237, reserve = 205, peak = 237.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 496, reserve = 468, peak = 496.
PHY-1001 : End build detailed router design. 3.154246s wall, 3.140625s user + 0.000000s system = 3.140625s CPU (99.6%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 29960, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.063759s wall, 1.062500s user + 0.000000s system = 1.062500s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 528, reserve = 502, peak = 528.
PHY-1001 : End phase 1; 1.070432s wall, 1.062500s user + 0.000000s system = 1.062500s CPU (99.3%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 30% nets.
PHY-1001 : Routed 37% nets.
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 181632, over cnt = 32(0%), over = 32, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 531, reserve = 503, peak = 532.
PHY-1001 : End initial routed; 1.525066s wall, 2.343750s user + 0.140625s system = 2.484375s CPU (162.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1724(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.720   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.827   |  -43.857  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.339445s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (96.7%)

PHY-1001 : Current memory(MB): used = 533, reserve = 505, peak = 533.
PHY-1001 : End phase 2; 1.864618s wall, 2.671875s user + 0.156250s system = 2.828125s CPU (151.7%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 181632, over cnt = 32(0%), over = 32, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.016580s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (94.2%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 181576, over cnt = 5(0%), over = 5, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.026851s wall, 0.015625s user + 0.015625s system = 0.031250s CPU (116.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 181624, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.020734s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (75.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1724(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.720   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.827   |  -43.857  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.362277s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (99.2%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 3 feed throughs used by 3 nets
PHY-1001 : End commit to database; 0.173052s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (99.3%)

PHY-1001 : Current memory(MB): used = 547, reserve = 519, peak = 547.
PHY-1001 : End phase 3; 0.730838s wall, 0.703125s user + 0.015625s system = 0.718750s CPU (98.3%)

PHY-1003 : Routed, final wirelength = 181624
PHY-1001 : Current memory(MB): used = 547, reserve = 520, peak = 547.
PHY-1001 : End export database. 0.009844s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (158.7%)

PHY-1001 : End detail routing;  7.007264s wall, 7.781250s user + 0.171875s system = 7.953125s CPU (113.5%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6335, tnet num: 1934, tinst num: 806, tnode num: 8635, tedge num: 11190.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : Too much hold violation, please check the src code.
RUN-1003 : finish command "route" in  7.847520s wall, 8.640625s user + 0.203125s system = 8.843750s CPU (112.7%)

RUN-1004 : used memory is 501 MB, reserved memory is 472 MB, peak memory is 547 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      767   out of  19600    3.91%
#reg                     1053   out of  19600    5.37%
#le                      1461
  #lut only               408   out of   1461   27.93%
  #reg only               694   out of   1461   47.50%
  #lut&reg                359   out of   1461   24.57%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                      Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0       479
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3       101
#3        wendu/clk_us                    GCLK               lslice             wendu/cnt_b[2]_syn_11.q0    41
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4       1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di             1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1461   |575     |192     |1086    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1048   |265     |129     |863     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |35     |30      |5       |21      |0       |0       |
|    demodu                  |Demodulation                                     |420    |73      |41      |350     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |55     |27      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |6       |0       |16      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |13     |5       |0       |13      |0       |0       |
|    integ                   |Integration                                      |142    |31      |15      |114     |0       |0       |
|    modu                    |Modulation                                       |100    |36      |14      |98      |0       |1       |
|    rs422                   |Rs422Output                                      |315    |67      |46      |260     |0       |4       |
|    trans                   |SquareWaveGenerator                              |36     |28      |8       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |177    |130     |7       |118     |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |26     |19      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |114    |83      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |214    |169     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1324  
    #2          2       332   
    #3          3       108   
    #4          4        29   
    #5        5-10       67   
    #6        11-50      31   
    #7       101-500     1    
  Average     1.98            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6335, tnet num: 1934, tinst num: 806, tnode num: 8635, tedge num: 11190.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 806
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1936, pip num: 14589
BIT-1002 : Init feedthrough completely, num: 3
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1349 valid insts, and 38083 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.177110s wall, 18.046875s user + 0.046875s system = 18.093750s CPU (569.5%)

RUN-1004 : used memory is 537 MB, reserved memory is 509 MB, peak memory is 663 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250805_103048.log"
