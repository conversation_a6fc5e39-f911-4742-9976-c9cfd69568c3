# 新协议测试示例

## 测试数据示例

假设输入数据：
- **32bit角速度数据**: `0x12345678` (十六进制)
- **16bit温度数据**: `0xABCD` (十六进制)

## 数据包分解

### 32bit角速度数据分解 (0x12345678)
- 二进制: `0001 0010 0011 0100 0101 0110 0111 1000`
- D31~D28: `0001` (0x1)
- D27~D21: `001 0010` (0x12)
- D20~D14: `011 0100` (0x34) 
- D13~D7:  `101 0110` (0x56)
- D6~D0:   `111 1000` (0x78)

### 16bit温度数据分解 (0xABCD)
- 二进制: `1010 1011 1100 1101`
- T13~T7: `101 0101` (0x55)
- T6~T0:  `110 1101` (0x6D)

## 预期输出数据包

| 字节 | 内容 | 二进制 | 十六进制 | 说明 |
|------|------|--------|----------|------|
| 第1字节 | 帧头 | `1000 0000` | 0x80 | 固定帧头 |
| 第2字节 | D6~D0 | `0111 1000` | 0x78 | 高1位补0 + D6~D0 |
| 第3字节 | D13~D7 | `0101 0110` | 0x56 | 高1位补0 + D13~D7 |
| 第4字节 | D20~D14 | `0011 0100` | 0x34 | 高1位补0 + D20~D14 |
| 第5字节 | D27~D21 | `0001 0010` | 0x12 | 高1位补0 + D27~D21 |
| 第6字节 | D31~D28 | `0000 0001` | 0x01 | 高4位补0 + D31~D28 |
| 第7字节 | 校验1 | `0xxx xxxx` | 0x?? | 高1位补0 + C6~C0 (第2-6字节XOR) |
| 第8字节 | T6~T0 | `0110 1101` | 0x6D | 高1位补0 + T6~T0 |
| 第9字节 | T13~T7 | `0101 0101` | 0x55 | 高1位补0 + T13~T7 |
| 第10字节 | 校验2 | `0xxx xxxx` | 0x?? | 高1位补0 + C6~C0 (第2-9字节XOR) |

## 校验计算示例

### 第7字节校验计算 (第2-6字节XOR)
```
第2字节: 0111 1000 (0x78)
第3字节: 0101 0110 (0x56)  
第4字节: 0011 0100 (0x34)
第5字节: 0001 0010 (0x12)
第6字节: 0000 0001 (0x01)
XOR结果: 0100 1111 (0x4F)
```

### 第10字节校验计算 (第2-9字节XOR)
```
第2-6字节XOR: 0100 1111 (0x4F)
第7字节:      0100 1111 (0x4F)
第8字节:      0110 1101 (0x6D)
第9字节:      0101 0101 (0x55)
XOR结果:      0001 0100 (0x14)
```

## 完整数据包
```
80 78 56 34 12 01 4F 6D 55 14
```

## Verilog仿真验证要点

1. **输入信号设置**:
   - `data_Packet = 32'h12345678`
   - `temp_data = 16'hABCD`

2. **预期输出序列**:
   - 检查每个tx_state状态下的tx_data_dy输出
   - 验证校验位计算的正确性

3. **时序验证**:
   - 确认状态机按正确顺序转换
   - 验证tx_done信号的响应

4. **边界条件测试**:
   - 全0数据: `data_Packet = 32'h00000000, temp_data = 16'h0000`
   - 全1数据: `data_Packet = 32'hFFFFFFFF, temp_data = 16'hFFFF`
