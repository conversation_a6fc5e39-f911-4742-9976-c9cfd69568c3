============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Mon Aug  4 15:46:05 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1569 instances
RUN-0007 : 367 luts, 952 seqs, 129 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2109 nets
RUN-1001 : 1539 nets have 2 pins
RUN-1001 : 468 nets have [3 - 5] pins
RUN-1001 : 60 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     251     
RUN-1001 :   No   |  No   |  Yes  |     122     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1567 instances, 367 luts, 952 seqs, 199 slices, 20 macros(199 instances: 129 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7512, tnet num: 2107, tinst num: 1567, tnode num: 10664, tedge num: 12763.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2107 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.256229s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (97.6%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 543352
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1567.
PHY-3001 : End clustering;  0.000030s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 479390, overlap = 18
PHY-3002 : Step(2): len = 453492, overlap = 13.5
PHY-3002 : Step(3): len = 440904, overlap = 15.75
PHY-3002 : Step(4): len = 428085, overlap = 9
PHY-3002 : Step(5): len = 412096, overlap = 18
PHY-3002 : Step(6): len = 396842, overlap = 11.25
PHY-3002 : Step(7): len = 388034, overlap = 13.5
PHY-3002 : Step(8): len = 376334, overlap = 13.5
PHY-3002 : Step(9): len = 365314, overlap = 13.5
PHY-3002 : Step(10): len = 356501, overlap = 11.25
PHY-3002 : Step(11): len = 348717, overlap = 11.25
PHY-3002 : Step(12): len = 337094, overlap = 11.25
PHY-3002 : Step(13): len = 330642, overlap = 13.5
PHY-3002 : Step(14): len = 322216, overlap = 13.5
PHY-3002 : Step(15): len = 315051, overlap = 13.5
PHY-3002 : Step(16): len = 306129, overlap = 13.5
PHY-3002 : Step(17): len = 301620, overlap = 13.5
PHY-3002 : Step(18): len = 293174, overlap = 13.5
PHY-3002 : Step(19): len = 285988, overlap = 13.5
PHY-3002 : Step(20): len = 279878, overlap = 13.5
PHY-3002 : Step(21): len = 275253, overlap = 13.5
PHY-3002 : Step(22): len = 268491, overlap = 13.5
PHY-3002 : Step(23): len = 263671, overlap = 13.5
PHY-3002 : Step(24): len = 255773, overlap = 13.5
PHY-3002 : Step(25): len = 251686, overlap = 11.25
PHY-3002 : Step(26): len = 246698, overlap = 11.25
PHY-3002 : Step(27): len = 239223, overlap = 20.25
PHY-3002 : Step(28): len = 231704, overlap = 20.25
PHY-3002 : Step(29): len = 229425, overlap = 20.25
PHY-3002 : Step(30): len = 220603, overlap = 20.25
PHY-3002 : Step(31): len = 211257, overlap = 20.25
PHY-3002 : Step(32): len = 207333, overlap = 20.25
PHY-3002 : Step(33): len = 204356, overlap = 20.25
PHY-3002 : Step(34): len = 136450, overlap = 13.5
PHY-3002 : Step(35): len = 135205, overlap = 15.75
PHY-3002 : Step(36): len = 132691, overlap = 15.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000119926
PHY-3002 : Step(37): len = 133958, overlap = 13.5
PHY-3002 : Step(38): len = 132151, overlap = 13.5
PHY-3002 : Step(39): len = 130809, overlap = 13.5
PHY-3002 : Step(40): len = 128147, overlap = 11.25
PHY-3002 : Step(41): len = 125083, overlap = 13.5
PHY-3002 : Step(42): len = 122032, overlap = 9
PHY-3002 : Step(43): len = 121163, overlap = 11.25
PHY-3002 : Step(44): len = 117510, overlap = 13.5
PHY-3002 : Step(45): len = 114608, overlap = 11.25
PHY-3002 : Step(46): len = 112442, overlap = 9
PHY-3002 : Step(47): len = 112078, overlap = 9
PHY-3002 : Step(48): len = 109924, overlap = 13.5
PHY-3002 : Step(49): len = 107712, overlap = 15.75
PHY-3002 : Step(50): len = 105546, overlap = 15.75
PHY-3002 : Step(51): len = 104579, overlap = 13.5
PHY-3002 : Step(52): len = 102524, overlap = 13.5
PHY-3002 : Step(53): len = 101225, overlap = 13.5
PHY-3002 : Step(54): len = 100532, overlap = 15.75
PHY-3002 : Step(55): len = 99242.7, overlap = 15.75
PHY-3002 : Step(56): len = 96535.8, overlap = 15.75
PHY-3002 : Step(57): len = 95107.7, overlap = 15.75
PHY-3002 : Step(58): len = 93455.4, overlap = 15.75
PHY-3002 : Step(59): len = 92539.5, overlap = 15.75
PHY-3002 : Step(60): len = 90508.2, overlap = 15.75
PHY-3002 : Step(61): len = 85562.9, overlap = 9.6875
PHY-3002 : Step(62): len = 81795.7, overlap = 13.5
PHY-3002 : Step(63): len = 81709.8, overlap = 15.9375
PHY-3002 : Step(64): len = 80471, overlap = 15.8125
PHY-3002 : Step(65): len = 79571, overlap = 15.75
PHY-3002 : Step(66): len = 79364.6, overlap = 15.6875
PHY-3002 : Step(67): len = 78210.6, overlap = 18.3125
PHY-3002 : Step(68): len = 77009.6, overlap = 18.4375
PHY-3002 : Step(69): len = 75555.2, overlap = 18.375
PHY-3002 : Step(70): len = 73601.1, overlap = 18.3125
PHY-3002 : Step(71): len = 72783.6, overlap = 18.0625
PHY-3002 : Step(72): len = 72053.1, overlap = 17.75
PHY-3002 : Step(73): len = 71069.2, overlap = 17.5625
PHY-3002 : Step(74): len = 69829.2, overlap = 17.5
PHY-3002 : Step(75): len = 69131.7, overlap = 17.5625
PHY-3002 : Step(76): len = 67879, overlap = 17.4375
PHY-3002 : Step(77): len = 67524.4, overlap = 14.875
PHY-3002 : Step(78): len = 66574.3, overlap = 17.75
PHY-3002 : Step(79): len = 66456.1, overlap = 17.75
PHY-3002 : Step(80): len = 65465.6, overlap = 15.25
PHY-3002 : Step(81): len = 64670.6, overlap = 14.9375
PHY-3002 : Step(82): len = 64406.2, overlap = 14.875
PHY-3002 : Step(83): len = 63342.9, overlap = 14.75
PHY-3002 : Step(84): len = 63325.3, overlap = 14.625
PHY-3002 : Step(85): len = 62184, overlap = 14.25
PHY-3002 : Step(86): len = 62118.7, overlap = 14
PHY-3002 : Step(87): len = 60625.6, overlap = 13.625
PHY-3002 : Step(88): len = 60162, overlap = 13.5
PHY-3002 : Step(89): len = 59747.7, overlap = 13.5
PHY-3002 : Step(90): len = 59459.9, overlap = 13.5
PHY-3002 : Step(91): len = 59527.7, overlap = 13.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000239852
PHY-3002 : Step(92): len = 59536.5, overlap = 13.5
PHY-3002 : Step(93): len = 59291.4, overlap = 13.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000479704
PHY-3002 : Step(94): len = 59671.1, overlap = 13.5
PHY-3002 : Step(95): len = 59782.9, overlap = 11.25
PHY-3002 : Step(96): len = 59811.2, overlap = 11.25
PHY-3001 : Before Legalized: Len = 59811.2
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008156s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 64273, Over = 0
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2107 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.066356s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (94.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(97): len = 64239.2, overlap = 7.5
PHY-3002 : Step(98): len = 64298.6, overlap = 7.625
PHY-3002 : Step(99): len = 63446.4, overlap = 7.78125
PHY-3002 : Step(100): len = 63449.9, overlap = 8.09375
PHY-3002 : Step(101): len = 63164.6, overlap = 7.8125
PHY-3002 : Step(102): len = 62076.9, overlap = 7.28125
PHY-3002 : Step(103): len = 60900.1, overlap = 8.5625
PHY-3002 : Step(104): len = 60631.6, overlap = 6.9375
PHY-3002 : Step(105): len = 60171.8, overlap = 6.75
PHY-3002 : Step(106): len = 58966.8, overlap = 6.9375
PHY-3002 : Step(107): len = 57657, overlap = 7.5
PHY-3002 : Step(108): len = 56871.3, overlap = 8.0625
PHY-3002 : Step(109): len = 56477.3, overlap = 8.0625
PHY-3002 : Step(110): len = 55419.1, overlap = 11.1562
PHY-3002 : Step(111): len = 54469, overlap = 11.75
PHY-3002 : Step(112): len = 54032.5, overlap = 13
PHY-3002 : Step(113): len = 53474.1, overlap = 13.5938
PHY-3002 : Step(114): len = 53186.8, overlap = 13.5312
PHY-3002 : Step(115): len = 52866.1, overlap = 13.7188
PHY-3002 : Step(116): len = 52114.9, overlap = 15.0625
PHY-3002 : Step(117): len = 51846.9, overlap = 15.375
PHY-3002 : Step(118): len = 51464.4, overlap = 15.4375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000671405
PHY-3002 : Step(119): len = 51251, overlap = 15.4375
PHY-3002 : Step(120): len = 51295.8, overlap = 15.4375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00134281
PHY-3002 : Step(121): len = 51151.7, overlap = 15.5
PHY-3002 : Step(122): len = 51120.2, overlap = 15.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2107 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.061318s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.05257e-05
PHY-3002 : Step(123): len = 51286.6, overlap = 51.7188
PHY-3002 : Step(124): len = 51658.3, overlap = 49.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000141051
PHY-3002 : Step(125): len = 51371.4, overlap = 49.2812
PHY-3002 : Step(126): len = 52875.3, overlap = 42.875
PHY-3002 : Step(127): len = 53231.2, overlap = 40.0625
PHY-3002 : Step(128): len = 53573.3, overlap = 40.375
PHY-3002 : Step(129): len = 54002, overlap = 42.3438
PHY-3002 : Step(130): len = 54035.2, overlap = 42.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000282103
PHY-3002 : Step(131): len = 54018.1, overlap = 41.2188
PHY-3002 : Step(132): len = 54132.2, overlap = 40.25
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000564206
PHY-3002 : Step(133): len = 54980.2, overlap = 35.9688
PHY-3002 : Step(134): len = 55541.6, overlap = 33.6875
PHY-3002 : Step(135): len = 56107.6, overlap = 30.7812
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7512, tnet num: 2107, tinst num: 1567, tnode num: 10664, tedge num: 12763.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 90.41 peak overflow 2.84
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2109.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 59648, over cnt = 248(0%), over = 1100, worst = 19
PHY-1001 : End global iterations;  0.071966s wall, 0.093750s user + 0.015625s system = 0.109375s CPU (152.0%)

PHY-1001 : Congestion index: top1 = 46.47, top5 = 26.82, top10 = 17.02, top15 = 12.07.
PHY-1001 : End incremental global routing;  0.122641s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (140.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2107 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.070647s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (88.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.225119s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (124.9%)

OPT-1001 : Current memory(MB): used = 208, reserve = 176, peak = 208.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1630/2109.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 59648, over cnt = 248(0%), over = 1100, worst = 19
PHY-1002 : len = 65888, over cnt = 180(0%), over = 534, worst = 19
PHY-1002 : len = 70584, over cnt = 72(0%), over = 209, worst = 14
PHY-1002 : len = 72576, over cnt = 18(0%), over = 21, worst = 2
PHY-1002 : len = 73760, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.107861s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (101.4%)

PHY-1001 : Congestion index: top1 = 39.44, top5 = 25.75, top10 = 18.64, top15 = 13.87.
OPT-1001 : End congestion update;  0.153086s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (91.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2107 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058986s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (106.0%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.215789s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (94.1%)

OPT-1001 : Current memory(MB): used = 211, reserve = 178, peak = 211.
OPT-1001 : End physical optimization;  0.696964s wall, 0.734375s user + 0.015625s system = 0.750000s CPU (107.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 367 LUT to BLE ...
SYN-4008 : Packed 367 LUT and 187 SEQ to BLE.
SYN-4003 : Packing 765 remaining SEQ's ...
SYN-4005 : Packed 101 SEQ with LUT/SLICE
SYN-4006 : 94 single LUT's are left
SYN-4006 : 664 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1031/1345 primitive instances ...
PHY-3001 : End packing;  0.048079s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.5%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 792 instances
RUN-1001 : 371 mslices, 370 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1930 nets
RUN-1001 : 1364 nets have 2 pins
RUN-1001 : 463 nets have [3 - 5] pins
RUN-1001 : 60 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 790 instances, 741 slices, 20 macros(199 instances: 129 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 56164.4, Over = 60
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6299, tnet num: 1928, tinst num: 790, tnode num: 8584, tedge num: 11151.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.283211s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (99.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.5418e-05
PHY-3002 : Step(136): len = 55217.4, overlap = 61.75
PHY-3002 : Step(137): len = 54626.1, overlap = 61.25
PHY-3002 : Step(138): len = 54219.7, overlap = 59.25
PHY-3002 : Step(139): len = 54298.7, overlap = 59.75
PHY-3002 : Step(140): len = 54235.9, overlap = 57.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.0836e-05
PHY-3002 : Step(141): len = 54667.2, overlap = 56.75
PHY-3002 : Step(142): len = 55071.2, overlap = 56.5
PHY-3002 : Step(143): len = 55677.8, overlap = 54.75
PHY-3002 : Step(144): len = 56063.7, overlap = 49.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000181672
PHY-3002 : Step(145): len = 56673.3, overlap = 42.5
PHY-3002 : Step(146): len = 57051.3, overlap = 40.75
PHY-3002 : Step(147): len = 57150.9, overlap = 40
PHY-3001 : Before Legalized: Len = 57150.9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.094072s wall, 0.046875s user + 0.125000s system = 0.171875s CPU (182.7%)

PHY-3001 : After Legalized: Len = 69318.1, Over = 0
PHY-3001 : Trial Legalized: Len = 69318.1
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.051006s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00170283
PHY-3002 : Step(148): len = 66648.2, overlap = 4.5
PHY-3002 : Step(149): len = 64004.4, overlap = 9.75
PHY-3002 : Step(150): len = 62243, overlap = 10.25
PHY-3002 : Step(151): len = 61538.8, overlap = 11.75
PHY-3002 : Step(152): len = 60077.4, overlap = 16.5
PHY-3002 : Step(153): len = 59536.3, overlap = 19.5
PHY-3002 : Step(154): len = 59462.8, overlap = 19.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00340567
PHY-3002 : Step(155): len = 59695.1, overlap = 18.75
PHY-3002 : Step(156): len = 59710.6, overlap = 19
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00681134
PHY-3002 : Step(157): len = 59747, overlap = 19.25
PHY-3002 : Step(158): len = 59720.5, overlap = 19.25
PHY-3001 : Before Legalized: Len = 59720.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005323s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 64368.4, Over = 0
PHY-3001 : Legalized: Len = 64368.4, Over = 0
PHY-3001 : Spreading special nets. 6 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006978s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (223.9%)

PHY-3001 : 11 instances has been re-located, deltaX = 0, deltaY = 11, maxDist = 1.
PHY-3001 : Final: Len = 64430.4, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6299, tnet num: 1928, tinst num: 790, tnode num: 8584, tedge num: 11151.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 153/1930.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71632, over cnt = 154(0%), over = 241, worst = 6
PHY-1002 : len = 72192, over cnt = 103(0%), over = 150, worst = 6
PHY-1002 : len = 73976, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 74040, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 74072, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.140996s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (99.7%)

PHY-1001 : Congestion index: top1 = 32.24, top5 = 23.17, top10 = 18.01, top15 = 14.04.
PHY-1001 : End incremental global routing;  0.193500s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (105.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060037s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (78.1%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.281976s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (99.7%)

OPT-1001 : Current memory(MB): used = 212, reserve = 180, peak = 214.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1695/1930.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 74072, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006268s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (249.3%)

PHY-1001 : Congestion index: top1 = 32.24, top5 = 23.17, top10 = 18.01, top15 = 14.04.
OPT-1001 : End congestion update;  0.053048s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (117.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051392s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.2%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 750 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 790 instances, 741 slices, 20 macros(199 instances: 129 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 64465.4, Over = 0
PHY-3001 : End spreading;  0.005539s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 64465.4, Over = 0
PHY-3001 : End incremental legalization;  0.035416s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (88.2%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.152987s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (153.2%)

OPT-1001 : Current memory(MB): used = 217, reserve = 185, peak = 217.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049472s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.7%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1691/1930.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 74112, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007379s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (211.7%)

PHY-1001 : Congestion index: top1 = 32.26, top5 = 23.14, top10 = 17.99, top15 = 14.03.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052554s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (89.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.793103
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.863878s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (106.7%)

RUN-1003 : finish command "place" in  5.340033s wall, 7.156250s user + 2.921875s system = 10.078125s CPU (188.7%)

RUN-1004 : used memory is 197 MB, reserved memory is 164 MB, peak memory is 218 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 792 instances
RUN-1001 : 371 mslices, 370 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1930 nets
RUN-1001 : 1364 nets have 2 pins
RUN-1001 : 463 nets have [3 - 5] pins
RUN-1001 : 60 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6299, tnet num: 1928, tinst num: 790, tnode num: 8584, tedge num: 11151.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 371 mslices, 370 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69576, over cnt = 145(0%), over = 234, worst = 6
PHY-1002 : len = 70824, over cnt = 79(0%), over = 97, worst = 4
PHY-1002 : len = 71720, over cnt = 26(0%), over = 29, worst = 3
PHY-1002 : len = 72200, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.127741s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (97.9%)

PHY-1001 : Congestion index: top1 = 31.55, top5 = 22.56, top10 = 17.58, top15 = 13.71.
PHY-1001 : End global routing;  0.178807s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (104.9%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 231, reserve = 199, peak = 234.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 493, reserve = 465, peak = 493.
PHY-1001 : End build detailed router design. 3.192467s wall, 3.125000s user + 0.062500s system = 3.187500s CPU (99.8%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 31192, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.110955s wall, 1.125000s user + 0.000000s system = 1.125000s CPU (101.3%)

PHY-1001 : Current memory(MB): used = 525, reserve = 498, peak = 525.
PHY-1001 : End phase 1; 1.117231s wall, 1.125000s user + 0.000000s system = 1.125000s CPU (100.7%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 30% nets.
PHY-1001 : Routed 37% nets.
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 180624, over cnt = 31(0%), over = 31, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 527, reserve = 499, peak = 528.
PHY-1001 : End initial routed; 1.680884s wall, 2.562500s user + 0.203125s system = 2.765625s CPU (164.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1712(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -46.745  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.349753s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (98.3%)

PHY-1001 : Current memory(MB): used = 530, reserve = 502, peak = 530.
PHY-1001 : End phase 2; 2.030725s wall, 2.906250s user + 0.203125s system = 3.109375s CPU (153.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 180624, over cnt = 31(0%), over = 31, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014468s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (108.0%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 180528, over cnt = 6(0%), over = 6, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.028575s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (109.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 180520, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.024538s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (63.7%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 180536, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.019494s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (80.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1712(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -46.745  |  20   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.342296s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (100.4%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 2 feed throughs used by 2 nets
PHY-1001 : End commit to database; 0.166556s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (103.2%)

PHY-1001 : Current memory(MB): used = 545, reserve = 516, peak = 545.
PHY-1001 : End phase 3; 0.731363s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (100.4%)

PHY-1003 : Routed, final wirelength = 180536
PHY-1001 : Current memory(MB): used = 545, reserve = 517, peak = 545.
PHY-1001 : End export database. 0.010947s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (142.7%)

PHY-1001 : End detail routing;  7.266305s wall, 8.078125s user + 0.265625s system = 8.343750s CPU (114.8%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6299, tnet num: 1928, tinst num: 790, tnode num: 8584, tedge num: 11151.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[30] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dib[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[38] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[3] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_74.mi[0] slack -2679ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_74.mi[1] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_77.mi[0] slack -2897ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_77.mi[1] slack -2684ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_80.mi[0] slack -2911ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_80.mi[1] slack -2911ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_83.mi[0] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_83.mi[1] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_87.mi[0] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_87.mi[1] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_90.mi[0] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_90.mi[1] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_93.mi[0] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_93.mi[1] slack -2679ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_95.mi[0] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/tx_data_dy_b[3]_syn_24.mi[0] slack -2679ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6405, tnet num: 1981, tinst num: 843, tnode num: 8690, tedge num: 11257.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/tx_data_dy_b[3]_syn_24_mi[0] slack -497ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_87_mi[1] slack -271ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_87_mi[0] slack -455ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_83_mi[0] slack -955ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_83_mi[1] slack -517ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_74_mi[0] slack -249ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_74_mi[1] slack -147ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_77_mi[0] slack -720ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg2_syn_80_mi[1] slack -497ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_77_mi[1] slack -573ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_80_mi[0] slack -601ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_93_mi[0] slack -438ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_93_mi[1] slack -476ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_90_mi[0] slack -848ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_90_mi[1] slack -748ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_95_mi[0] slack -707ps
RUN-1001 : End hold fix;  3.093845s wall, 3.406250s user + 0.218750s system = 3.625000s CPU (117.2%)

RUN-1003 : finish command "route" in  10.867531s wall, 11.984375s user + 0.500000s system = 12.484375s CPU (114.9%)

RUN-1004 : used memory is 520 MB, reserved memory is 492 MB, peak memory is 545 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      876   out of  19600    4.47%
#reg                     1049   out of  19600    5.35%
#le                      1540
  #lut only               491   out of   1540   31.88%
  #reg only               664   out of   1540   43.12%
  #lut&reg                385   out of   1540   25.00%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         477
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         101
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q0    40
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1540   |677     |199     |1082    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1042   |272     |135     |860     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |24     |18      |6       |21      |0       |0       |
|    demodu                  |Demodulation                                     |432    |82      |41      |351     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |54     |29      |6       |45      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |5       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |13     |8       |0       |13      |0       |0       |
|    integ                   |Integration                                      |140    |21      |15      |112     |0       |0       |
|    modu                    |Modulation                                       |101    |59      |21      |97      |0       |1       |
|    rs422                   |Rs422Output                                      |316    |69      |46      |261     |0       |4       |
|    trans                   |SquareWaveGenerator                              |29     |23      |6       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |168    |132     |7       |119     |0       |0       |
|    U0                      |speed_select_Tx                                  |34     |27      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |27     |21      |0       |18      |0       |0       |
|    U2                      |Ctrl_Data                                        |107    |84      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |306    |261     |45      |70      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1378  
    #2          2       327   
    #3          3       109   
    #4          4        27   
    #5        5-10       65   
    #6        11-50      32   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6405, tnet num: 1981, tinst num: 843, tnode num: 8690, tedge num: 11257.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1981 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 843
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1983, pip num: 14580
BIT-1002 : Init feedthrough completely, num: 6
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1331 valid insts, and 38951 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.351952s wall, 18.812500s user + 0.078125s system = 18.890625s CPU (563.6%)

RUN-1004 : used memory is 516 MB, reserved memory is 489 MB, peak memory is 667 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250804_154605.log"
