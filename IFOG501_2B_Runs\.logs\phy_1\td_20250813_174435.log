============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Wed Aug 13 17:44:35 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1570 instances
RUN-0007 : 368 luts, 950 seqs, 131 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2119 nets
RUN-1001 : 1546 nets have 2 pins
RUN-1001 : 472 nets have [3 - 5] pins
RUN-1001 : 58 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     246     
RUN-1001 :   No   |  No   |  Yes  |     125     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1568 instances, 368 luts, 950 seqs, 201 slices, 20 macros(201 instances: 131 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7546, tnet num: 2117, tinst num: 1568, tnode num: 10707, tedge num: 12849.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2117 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.243523s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (102.7%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 535454
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1568.
PHY-3001 : End clustering;  0.000058s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 467145, overlap = 20.25
PHY-3002 : Step(2): len = 440691, overlap = 13.5
PHY-3002 : Step(3): len = 428206, overlap = 20.25
PHY-3002 : Step(4): len = 415339, overlap = 13.5
PHY-3002 : Step(5): len = 406297, overlap = 15.75
PHY-3002 : Step(6): len = 384349, overlap = 18
PHY-3002 : Step(7): len = 377236, overlap = 15.75
PHY-3002 : Step(8): len = 366864, overlap = 15.75
PHY-3002 : Step(9): len = 355655, overlap = 13.5
PHY-3002 : Step(10): len = 347103, overlap = 13.5
PHY-3002 : Step(11): len = 341570, overlap = 13.5
PHY-3002 : Step(12): len = 332521, overlap = 13.5
PHY-3002 : Step(13): len = 326891, overlap = 13.5
PHY-3002 : Step(14): len = 320101, overlap = 13.5
PHY-3002 : Step(15): len = 314788, overlap = 13.5
PHY-3002 : Step(16): len = 306273, overlap = 15.75
PHY-3002 : Step(17): len = 301225, overlap = 15.75
PHY-3002 : Step(18): len = 295623, overlap = 15.75
PHY-3002 : Step(19): len = 289698, overlap = 15.75
PHY-3002 : Step(20): len = 280072, overlap = 15.75
PHY-3002 : Step(21): len = 276387, overlap = 15.75
PHY-3002 : Step(22): len = 271128, overlap = 15.75
PHY-3002 : Step(23): len = 265383, overlap = 15.75
PHY-3002 : Step(24): len = 256259, overlap = 13.5
PHY-3002 : Step(25): len = 253073, overlap = 13.5
PHY-3002 : Step(26): len = 247202, overlap = 20.25
PHY-3002 : Step(27): len = 231757, overlap = 20.25
PHY-3002 : Step(28): len = 223662, overlap = 20.25
PHY-3002 : Step(29): len = 222038, overlap = 20.25
PHY-3002 : Step(30): len = 171135, overlap = 18
PHY-3002 : Step(31): len = 159818, overlap = 20.25
PHY-3002 : Step(32): len = 158125, overlap = 20.25
PHY-3002 : Step(33): len = 143544, overlap = 18
PHY-3002 : Step(34): len = 136077, overlap = 18
PHY-3002 : Step(35): len = 134256, overlap = 20.25
PHY-3002 : Step(36): len = 132373, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00011085
PHY-3002 : Step(37): len = 132985, overlap = 13.5
PHY-3002 : Step(38): len = 131624, overlap = 15.75
PHY-3002 : Step(39): len = 130607, overlap = 18
PHY-3002 : Step(40): len = 129030, overlap = 13.5
PHY-3002 : Step(41): len = 125557, overlap = 11.25
PHY-3002 : Step(42): len = 123826, overlap = 11.25
PHY-3002 : Step(43): len = 121338, overlap = 9
PHY-3002 : Step(44): len = 118954, overlap = 9
PHY-3002 : Step(45): len = 116056, overlap = 9
PHY-3002 : Step(46): len = 113429, overlap = 9
PHY-3002 : Step(47): len = 111987, overlap = 9
PHY-3002 : Step(48): len = 109705, overlap = 9
PHY-3002 : Step(49): len = 106983, overlap = 9
PHY-3002 : Step(50): len = 105287, overlap = 13.5
PHY-3002 : Step(51): len = 104159, overlap = 13.5
PHY-3002 : Step(52): len = 101631, overlap = 15.75
PHY-3002 : Step(53): len = 97321.2, overlap = 15.75
PHY-3002 : Step(54): len = 95913.2, overlap = 15.75
PHY-3002 : Step(55): len = 95050.8, overlap = 13.5
PHY-3002 : Step(56): len = 93535.7, overlap = 15.75
PHY-3002 : Step(57): len = 91988, overlap = 15.75
PHY-3002 : Step(58): len = 90784.2, overlap = 18
PHY-3002 : Step(59): len = 88726.7, overlap = 15.75
PHY-3002 : Step(60): len = 87166.1, overlap = 15.75
PHY-3002 : Step(61): len = 85012.5, overlap = 15.75
PHY-3002 : Step(62): len = 84579.4, overlap = 15.75
PHY-3002 : Step(63): len = 82960.4, overlap = 15.75
PHY-3002 : Step(64): len = 81792.3, overlap = 15.75
PHY-3002 : Step(65): len = 81611.7, overlap = 18
PHY-3002 : Step(66): len = 79883, overlap = 15.9375
PHY-3002 : Step(67): len = 79037.2, overlap = 16.125
PHY-3002 : Step(68): len = 78862.1, overlap = 16.3125
PHY-3002 : Step(69): len = 78176.7, overlap = 16.9375
PHY-3002 : Step(70): len = 76863.5, overlap = 17.25
PHY-3002 : Step(71): len = 76464.8, overlap = 17.5
PHY-3002 : Step(72): len = 74233.2, overlap = 18.25
PHY-3002 : Step(73): len = 73461.9, overlap = 18.75
PHY-3002 : Step(74): len = 72253.2, overlap = 18.75
PHY-3002 : Step(75): len = 71581.6, overlap = 19.125
PHY-3002 : Step(76): len = 71318.5, overlap = 19.125
PHY-3002 : Step(77): len = 70973.8, overlap = 19
PHY-3002 : Step(78): len = 70646.3, overlap = 18.9375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000221701
PHY-3002 : Step(79): len = 71045.2, overlap = 18.875
PHY-3002 : Step(80): len = 71064.4, overlap = 18.875
PHY-3002 : Step(81): len = 71011.5, overlap = 18.875
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000443401
PHY-3002 : Step(82): len = 71176.7, overlap = 18.8125
PHY-3002 : Step(83): len = 71103.3, overlap = 20.8125
PHY-3001 : Before Legalized: Len = 71103.3
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007512s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (208.0%)

PHY-3001 : After Legalized: Len = 75788.6, Over = 5.0625
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2117 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.058169s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (107.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(84): len = 75590, overlap = 13.9688
PHY-3002 : Step(85): len = 74016.2, overlap = 14.9375
PHY-3002 : Step(86): len = 73046.6, overlap = 14.625
PHY-3002 : Step(87): len = 72204.6, overlap = 15.4062
PHY-3002 : Step(88): len = 71278, overlap = 15.3438
PHY-3002 : Step(89): len = 69918.1, overlap = 18.1562
PHY-3002 : Step(90): len = 68332.1, overlap = 14.5312
PHY-3002 : Step(91): len = 66707.9, overlap = 13.3125
PHY-3002 : Step(92): len = 66121.8, overlap = 13.4062
PHY-3002 : Step(93): len = 65014.8, overlap = 13.4688
PHY-3002 : Step(94): len = 64589.2, overlap = 13.125
PHY-3002 : Step(95): len = 63546.1, overlap = 12.125
PHY-3002 : Step(96): len = 63217.8, overlap = 9.8125
PHY-3002 : Step(97): len = 61853.9, overlap = 10.75
PHY-3002 : Step(98): len = 60370.2, overlap = 10.625
PHY-3002 : Step(99): len = 59722.1, overlap = 11.125
PHY-3002 : Step(100): len = 59077, overlap = 13.125
PHY-3002 : Step(101): len = 58444.3, overlap = 9.8125
PHY-3002 : Step(102): len = 58244.6, overlap = 9.25
PHY-3002 : Step(103): len = 58094.6, overlap = 9.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000902375
PHY-3002 : Step(104): len = 57836.6, overlap = 9.625
PHY-3002 : Step(105): len = 57885.4, overlap = 9.53125
PHY-3002 : Step(106): len = 57690.3, overlap = 9.03125
PHY-3002 : Step(107): len = 57728.6, overlap = 6.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2117 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.066349s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (94.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00014305
PHY-3002 : Step(108): len = 57991.1, overlap = 48.9688
PHY-3002 : Step(109): len = 58634, overlap = 48.875
PHY-3002 : Step(110): len = 59219.2, overlap = 41.5
PHY-3002 : Step(111): len = 59709.1, overlap = 35.5312
PHY-3002 : Step(112): len = 59579.2, overlap = 32.9062
PHY-3002 : Step(113): len = 59328.4, overlap = 31.5625
PHY-3002 : Step(114): len = 58507.4, overlap = 31.7812
PHY-3002 : Step(115): len = 58556.5, overlap = 34.1875
PHY-3002 : Step(116): len = 57959.9, overlap = 32.5312
PHY-3002 : Step(117): len = 57533.7, overlap = 34.0312
PHY-3002 : Step(118): len = 57147.5, overlap = 33.875
PHY-3002 : Step(119): len = 56954, overlap = 34.1562
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000286101
PHY-3002 : Step(120): len = 56774.4, overlap = 28.25
PHY-3002 : Step(121): len = 56830.3, overlap = 26.8125
PHY-3002 : Step(122): len = 56931.1, overlap = 27.1875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000572202
PHY-3002 : Step(123): len = 56918, overlap = 27.0312
PHY-3002 : Step(124): len = 56985.4, overlap = 27
PHY-3002 : Step(125): len = 57323.6, overlap = 27.9688
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.0011444
PHY-3002 : Step(126): len = 57454.5, overlap = 27.375
PHY-3002 : Step(127): len = 57708.8, overlap = 27
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00228881
PHY-3002 : Step(128): len = 57793.1, overlap = 26.3438
PHY-3002 : Step(129): len = 57831.8, overlap = 26.125
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.00457761
PHY-3002 : Step(130): len = 57930.8, overlap = 26.0312
PHY-3002 : Step(131): len = 57980.4, overlap = 25.875
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 0.00915523
PHY-3002 : Step(132): len = 57871.2, overlap = 26.4688
PHY-3002 : Step(133): len = 57871.2, overlap = 26.4688
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 0.0183105
PHY-3002 : Step(134): len = 57958.5, overlap = 26.4688
PHY-3002 : Step(135): len = 57958.5, overlap = 26.4688
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.0366209
PHY-3002 : Step(136): len = 57943.3, overlap = 26.4688
PHY-3002 : Step(137): len = 57943.3, overlap = 26.4688
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7546, tnet num: 2117, tinst num: 1568, tnode num: 10707, tedge num: 12849.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 90.75 peak overflow 2.75
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2119.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 60384, over cnt = 219(0%), over = 954, worst = 22
PHY-1001 : End global iterations;  0.067052s wall, 0.078125s user + 0.015625s system = 0.093750s CPU (139.8%)

PHY-1001 : Congestion index: top1 = 42.89, top5 = 25.00, top10 = 16.45, top15 = 11.88.
PHY-1001 : End incremental global routing;  0.117326s wall, 0.125000s user + 0.031250s system = 0.156250s CPU (133.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2117 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.068487s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (91.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.214093s wall, 0.218750s user + 0.031250s system = 0.250000s CPU (116.8%)

OPT-1001 : Current memory(MB): used = 209, reserve = 176, peak = 209.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1642/2119.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 60384, over cnt = 219(0%), over = 954, worst = 22
PHY-1002 : len = 66592, over cnt = 171(0%), over = 458, worst = 18
PHY-1002 : len = 71416, over cnt = 46(0%), over = 121, worst = 13
PHY-1002 : len = 72816, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 72944, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.077639s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (140.9%)

PHY-1001 : Congestion index: top1 = 37.41, top5 = 24.85, top10 = 18.22, top15 = 13.75.
OPT-1001 : End congestion update;  0.121524s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (128.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2117 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057047s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (109.6%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.183346s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (119.3%)

OPT-1001 : Current memory(MB): used = 212, reserve = 179, peak = 212.
OPT-1001 : End physical optimization;  0.636572s wall, 0.671875s user + 0.031250s system = 0.703125s CPU (110.5%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 368 LUT to BLE ...
SYN-4008 : Packed 368 LUT and 190 SEQ to BLE.
SYN-4003 : Packing 760 remaining SEQ's ...
SYN-4005 : Packed 90 SEQ with LUT/SLICE
SYN-4006 : 102 single LUT's are left
SYN-4006 : 670 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1038/1354 primitive instances ...
PHY-3001 : End packing;  0.045546s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (102.9%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 799 instances
RUN-1001 : 374 mslices, 374 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1937 nets
RUN-1001 : 1366 nets have 2 pins
RUN-1001 : 470 nets have [3 - 5] pins
RUN-1001 : 59 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 797 instances, 748 slices, 20 macros(201 instances: 131 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 58038.4, Over = 50.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6323, tnet num: 1935, tinst num: 797, tnode num: 8609, tedge num: 11225.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1935 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.281049s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.01084e-05
PHY-3002 : Step(138): len = 56950.5, overlap = 54.25
PHY-3002 : Step(139): len = 56549, overlap = 53.75
PHY-3002 : Step(140): len = 56323.1, overlap = 54.75
PHY-3002 : Step(141): len = 56388.7, overlap = 50.25
PHY-3002 : Step(142): len = 56368.7, overlap = 51
PHY-3002 : Step(143): len = 55984.4, overlap = 52.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000100217
PHY-3002 : Step(144): len = 56262.2, overlap = 53.25
PHY-3002 : Step(145): len = 56687.5, overlap = 52.25
PHY-3002 : Step(146): len = 57247, overlap = 48.75
PHY-3002 : Step(147): len = 57481.2, overlap = 48
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000200434
PHY-3002 : Step(148): len = 57633, overlap = 46.75
PHY-3002 : Step(149): len = 58422, overlap = 44
PHY-3001 : Before Legalized: Len = 58422
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.084729s wall, 0.078125s user + 0.171875s system = 0.250000s CPU (295.1%)

PHY-3001 : After Legalized: Len = 70308.3, Over = 0
PHY-3001 : Trial Legalized: Len = 70308.3
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1935 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.048126s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00120898
PHY-3002 : Step(150): len = 66983.4, overlap = 4.5
PHY-3002 : Step(151): len = 64801.2, overlap = 11.25
PHY-3002 : Step(152): len = 62922.4, overlap = 13.25
PHY-3002 : Step(153): len = 61775.8, overlap = 17.5
PHY-3002 : Step(154): len = 60938.6, overlap = 20
PHY-3002 : Step(155): len = 60530.7, overlap = 20.5
PHY-3002 : Step(156): len = 60068.4, overlap = 19.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00241795
PHY-3002 : Step(157): len = 60317.5, overlap = 19.25
PHY-3002 : Step(158): len = 60354.4, overlap = 18.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0048359
PHY-3002 : Step(159): len = 60360.8, overlap = 18.75
PHY-3002 : Step(160): len = 60382.5, overlap = 18.75
PHY-3001 : Before Legalized: Len = 60382.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005136s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 64987.3, Over = 0
PHY-3001 : Legalized: Len = 64987.3, Over = 0
PHY-3001 : Spreading special nets. 12 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005499s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 21 instances has been re-located, deltaX = 7, deltaY = 14, maxDist = 1.
PHY-3001 : Final: Len = 65225.3, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6323, tnet num: 1935, tinst num: 797, tnode num: 8609, tedge num: 11225.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 73/1937.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71424, over cnt = 155(0%), over = 234, worst = 7
PHY-1002 : len = 72648, over cnt = 75(0%), over = 83, worst = 3
PHY-1002 : len = 73416, over cnt = 24(0%), over = 25, worst = 2
PHY-1002 : len = 73736, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 73768, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.120182s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (130.0%)

PHY-1001 : Congestion index: top1 = 31.40, top5 = 22.79, top10 = 17.76, top15 = 13.92.
PHY-1001 : End incremental global routing;  0.172944s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (126.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1935 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.066464s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (94.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.270291s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (115.6%)

OPT-1001 : Current memory(MB): used = 212, reserve = 180, peak = 214.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1716/1937.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73768, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005595s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.40, top5 = 22.79, top10 = 17.76, top15 = 13.92.
OPT-1001 : End congestion update;  0.051670s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (90.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1935 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046842s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.1%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 757 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 797 instances, 748 slices, 20 macros(201 instances: 131 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 65225.4, Over = 0
PHY-3001 : End spreading;  0.005794s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 65225.4, Over = 0
PHY-3001 : End incremental legalization;  0.034917s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (134.2%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.149454s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (94.1%)

OPT-1001 : Current memory(MB): used = 217, reserve = 185, peak = 217.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1935 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056245s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (111.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1708/1937.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73768, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008478s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (184.3%)

PHY-1001 : Congestion index: top1 = 31.44, top5 = 22.78, top10 = 17.72, top15 = 13.89.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1935 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047494s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.7%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.034483
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.837511s wall, 0.906250s user + 0.046875s system = 0.953125s CPU (113.8%)

RUN-1003 : finish command "place" in  5.074297s wall, 7.453125s user + 2.671875s system = 10.125000s CPU (199.5%)

RUN-1004 : used memory is 200 MB, reserved memory is 167 MB, peak memory is 217 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 799 instances
RUN-1001 : 374 mslices, 374 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1937 nets
RUN-1001 : 1366 nets have 2 pins
RUN-1001 : 470 nets have [3 - 5] pins
RUN-1001 : 59 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6323, tnet num: 1935, tinst num: 797, tnode num: 8609, tedge num: 11225.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 374 mslices, 374 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1935 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70768, over cnt = 152(0%), over = 236, worst = 7
PHY-1002 : len = 72080, over cnt = 76(0%), over = 84, worst = 2
PHY-1002 : len = 73064, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 73168, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.113579s wall, 0.140625s user + 0.031250s system = 0.171875s CPU (151.3%)

PHY-1001 : Congestion index: top1 = 31.70, top5 = 22.73, top10 = 17.63, top15 = 13.81.
PHY-1001 : End global routing;  0.165218s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (132.4%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 231, reserve = 199, peak = 233.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 493, reserve = 465, peak = 493.
PHY-1001 : End build detailed router design. 3.070042s wall, 3.000000s user + 0.078125s system = 3.078125s CPU (100.3%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 31288, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.097684s wall, 1.078125s user + 0.015625s system = 1.093750s CPU (99.6%)

PHY-1001 : Current memory(MB): used = 525, reserve = 498, peak = 525.
PHY-1001 : End phase 1; 1.103460s wall, 1.078125s user + 0.015625s system = 1.093750s CPU (99.1%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 29% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 177696, over cnt = 31(0%), over = 31, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 528, reserve = 500, peak = 528.
PHY-1001 : End initial routed; 1.685004s wall, 2.750000s user + 0.078125s system = 2.828125s CPU (167.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1717(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.003   |  -45.763  |  21   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.330547s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (99.3%)

PHY-1001 : Current memory(MB): used = 529, reserve = 500, peak = 529.
PHY-1001 : End phase 2; 2.015638s wall, 3.078125s user + 0.078125s system = 3.156250s CPU (156.6%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 177696, over cnt = 31(0%), over = 31, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015017s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (104.1%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 177424, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.029038s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (107.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 177464, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.023923s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (130.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1717(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.800   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.003   |  -45.727  |  21   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.328291s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (99.9%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 13 feed throughs used by 12 nets
PHY-1001 : End commit to database; 0.165557s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (94.4%)

PHY-1001 : Current memory(MB): used = 543, reserve = 515, peak = 543.
PHY-1001 : End phase 3; 0.689497s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (99.7%)

PHY-1003 : Routed, final wirelength = 177464
PHY-1001 : Current memory(MB): used = 543, reserve = 515, peak = 543.
PHY-1001 : End export database. 0.010701s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (146.0%)

PHY-1001 : End detail routing;  7.059790s wall, 8.031250s user + 0.171875s system = 8.203125s CPU (116.2%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6323, tnet num: 1935, tinst num: 797, tnode num: 8609, tedge num: 11225.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[7] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[3] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[0] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[0] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[3] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[4] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[4] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[2] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[3] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[13] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[4] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[15] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[16] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/asy_w_rst1 endpin signal_process/demodu/fifo/rd_to_wr_cross_inst/reg2_syn_25.sr slack -65ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_60.mi[0] slack -2871ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_60.mi[1] slack -2763ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_63.mi[0] slack -2727ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_63.mi[1] slack -2627ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_66.mi[0] slack -2991ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_66.mi[1] slack -2857ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_69.mi[0] slack -2811ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_69.mi[1] slack -2859ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_72.mi[0] slack -3003ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_72.mi[1] slack -2871ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_75.mi[0] slack -2632ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_75.mi[1] slack -2871ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_78.mi[0] slack -2763ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_78.mi[1] slack -2991ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_81.mi[0] slack -3003ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_81.mi[1] slack -2763ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6447, tnet num: 1997, tinst num: 859, tnode num: 8733, tedge num: 11349.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_66_mi[1] slack -759ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_66_mi[0] slack -661ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_69_mi[1] slack -574ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_72_mi[1] slack -442ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_72_mi[0] slack -335ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_78_mi[1] slack -311ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_75_mi[1] slack -450ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_75_mi[0] slack -450ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_78_mi[0] slack -663ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_81_mi[0] slack -59ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_81_mi[1] slack -526ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_60_mi[1] slack -152ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_60_mi[0] slack -558ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_63_mi[1] slack -238ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_63_mi[0] slack -653ps
RUN-1001 : End hold fix;  4.033230s wall, 4.218750s user + 0.250000s system = 4.468750s CPU (110.8%)

RUN-1003 : finish command "route" in  11.581220s wall, 12.781250s user + 0.453125s system = 13.234375s CPU (114.3%)

RUN-1004 : used memory is 520 MB, reserved memory is 493 MB, peak memory is 543 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   15
  #output                  21
  #inout                    1

Utilization Statistics
#lut                      900   out of  19600    4.59%
#reg                     1052   out of  19600    5.37%
#le                      1570
  #lut only               518   out of   1570   32.99%
  #reg only               670   out of   1570   42.68%
  #lut&reg                382   out of   1570   24.33%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    13
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                      Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0       479
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3       100
#3        wendu/clk_us                    GCLK               mslice             wendu/cnt_b[2]_syn_11.q0    39
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4       1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di             1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
  RxTransmit       INPUT        M14        LVCMOS33          N/A          PULLUP      IREG    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1570   |699     |201     |1086    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1077   |296     |137     |860     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |23     |19      |4       |20      |0       |0       |
|    demodu                  |Demodulation                                     |453    |104     |41      |352     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |57     |33      |6       |45      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |7       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |14     |7       |0       |14      |0       |0       |
|    integ                   |Integration                                      |142    |16      |15      |114     |0       |0       |
|    modu                    |Modulation                                       |103    |54      |23      |99      |0       |1       |
|    rs422                   |Rs422Output                                      |320    |75      |46      |255     |0       |4       |
|    trans                   |SquareWaveGenerator                              |36     |28      |8       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |172    |139     |7       |116     |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |26      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |26     |25      |0       |15      |0       |0       |
|    U2                      |Ctrl_Data                                        |111    |88      |0       |85      |0       |0       |
|  wendu                     |DS18B20                                          |297    |252     |45      |75      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1389  
    #2          2       333   
    #3          3       105   
    #4          4        32   
    #5        5-10       63   
    #6        11-50      32   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6447, tnet num: 1997, tinst num: 859, tnode num: 8733, tedge num: 11349.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1997 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 859
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1999, pip num: 14654
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 17
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1333 valid insts, and 39332 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  7.921196s wall, 43.406250s user + 0.203125s system = 43.609375s CPU (550.5%)

RUN-1004 : used memory is 517 MB, reserved memory is 490 MB, peak memory is 669 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250813_174435.log"
