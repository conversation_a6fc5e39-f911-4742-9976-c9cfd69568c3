============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Tue Aug  5 11:09:26 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1567 instances
RUN-0007 : 362 luts, 951 seqs, 133 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2111 nets
RUN-1001 : 1540 nets have 2 pins
RUN-1001 : 468 nets have [3 - 5] pins
RUN-1001 : 61 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     251     
RUN-1001 :   No   |  No   |  Yes  |     121     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1565 instances, 362 luts, 951 seqs, 203 slices, 21 macros(203 instances: 133 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7519, tnet num: 2109, tinst num: 1565, tnode num: 10681, tedge num: 12784.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2109 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.284037s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (104.5%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 513782
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1565.
PHY-3001 : End clustering;  0.000022s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 446760, overlap = 20.25
PHY-3002 : Step(2): len = 417787, overlap = 11.25
PHY-3002 : Step(3): len = 406984, overlap = 20.25
PHY-3002 : Step(4): len = 396596, overlap = 13.5
PHY-3002 : Step(5): len = 383218, overlap = 20.25
PHY-3002 : Step(6): len = 371440, overlap = 13.5
PHY-3002 : Step(7): len = 363779, overlap = 13.5
PHY-3002 : Step(8): len = 356058, overlap = 13.5
PHY-3002 : Step(9): len = 344121, overlap = 15.75
PHY-3002 : Step(10): len = 336958, overlap = 15.75
PHY-3002 : Step(11): len = 330972, overlap = 13.5
PHY-3002 : Step(12): len = 320227, overlap = 13.5
PHY-3002 : Step(13): len = 312942, overlap = 13.5
PHY-3002 : Step(14): len = 308173, overlap = 13.5
PHY-3002 : Step(15): len = 298209, overlap = 13.5
PHY-3002 : Step(16): len = 290696, overlap = 13.5
PHY-3002 : Step(17): len = 286881, overlap = 11.25
PHY-3002 : Step(18): len = 278758, overlap = 13.5
PHY-3002 : Step(19): len = 268303, overlap = 13.5
PHY-3002 : Step(20): len = 264119, overlap = 13.5
PHY-3002 : Step(21): len = 259796, overlap = 13.5
PHY-3002 : Step(22): len = 245674, overlap = 13.5
PHY-3002 : Step(23): len = 238810, overlap = 13.5
PHY-3002 : Step(24): len = 236591, overlap = 13.5
PHY-3002 : Step(25): len = 226347, overlap = 15.75
PHY-3002 : Step(26): len = 199767, overlap = 18
PHY-3002 : Step(27): len = 195425, overlap = 20.25
PHY-3002 : Step(28): len = 193708, overlap = 20.25
PHY-3002 : Step(29): len = 159586, overlap = 15.75
PHY-3002 : Step(30): len = 150907, overlap = 18
PHY-3002 : Step(31): len = 148791, overlap = 18
PHY-3002 : Step(32): len = 144834, overlap = 20.25
PHY-3002 : Step(33): len = 138677, overlap = 20.25
PHY-3002 : Step(34): len = 137067, overlap = 20.25
PHY-3002 : Step(35): len = 131697, overlap = 18
PHY-3002 : Step(36): len = 127515, overlap = 20.25
PHY-3002 : Step(37): len = 125063, overlap = 20.25
PHY-3002 : Step(38): len = 123263, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000102727
PHY-3002 : Step(39): len = 123337, overlap = 11.25
PHY-3002 : Step(40): len = 122576, overlap = 13.5
PHY-3002 : Step(41): len = 121691, overlap = 9
PHY-3002 : Step(42): len = 120504, overlap = 11.25
PHY-3002 : Step(43): len = 116113, overlap = 11.25
PHY-3002 : Step(44): len = 113725, overlap = 9
PHY-3002 : Step(45): len = 112067, overlap = 9
PHY-3002 : Step(46): len = 109384, overlap = 11.25
PHY-3002 : Step(47): len = 107202, overlap = 11.25
PHY-3002 : Step(48): len = 105291, overlap = 9
PHY-3002 : Step(49): len = 103699, overlap = 13.5
PHY-3002 : Step(50): len = 101052, overlap = 13.5
PHY-3002 : Step(51): len = 98386.2, overlap = 13.5
PHY-3002 : Step(52): len = 97174, overlap = 13.5
PHY-3002 : Step(53): len = 93805.8, overlap = 11.25
PHY-3002 : Step(54): len = 90631.8, overlap = 11.25
PHY-3002 : Step(55): len = 89015.6, overlap = 13.5
PHY-3002 : Step(56): len = 87579.1, overlap = 13.5
PHY-3002 : Step(57): len = 85487.2, overlap = 13.5
PHY-3002 : Step(58): len = 84235.2, overlap = 13.5
PHY-3002 : Step(59): len = 81719.1, overlap = 13.5
PHY-3002 : Step(60): len = 79267.2, overlap = 15.75
PHY-3002 : Step(61): len = 78004.3, overlap = 13.5
PHY-3002 : Step(62): len = 77036.1, overlap = 13.5
PHY-3002 : Step(63): len = 75008.5, overlap = 15.75
PHY-3002 : Step(64): len = 74129.4, overlap = 15.75
PHY-3002 : Step(65): len = 73035.6, overlap = 18.1875
PHY-3002 : Step(66): len = 71092.4, overlap = 16.1875
PHY-3002 : Step(67): len = 70091.1, overlap = 16.375
PHY-3002 : Step(68): len = 68915.3, overlap = 14.125
PHY-3002 : Step(69): len = 68699.7, overlap = 14
PHY-3002 : Step(70): len = 67985.3, overlap = 13.875
PHY-3002 : Step(71): len = 67030.2, overlap = 16.3125
PHY-3002 : Step(72): len = 66530.9, overlap = 16.3125
PHY-3002 : Step(73): len = 63984.3, overlap = 16.4375
PHY-3002 : Step(74): len = 63692, overlap = 16.375
PHY-3002 : Step(75): len = 63126.4, overlap = 13.9375
PHY-3002 : Step(76): len = 62161.3, overlap = 13.875
PHY-3002 : Step(77): len = 60705, overlap = 15.75
PHY-3002 : Step(78): len = 59528.2, overlap = 15.75
PHY-3002 : Step(79): len = 59517.8, overlap = 15.75
PHY-3002 : Step(80): len = 59252.7, overlap = 15.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000205454
PHY-3002 : Step(81): len = 59344.2, overlap = 15.75
PHY-3002 : Step(82): len = 59303, overlap = 15.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000410907
PHY-3002 : Step(83): len = 59575.2, overlap = 15.75
PHY-3002 : Step(84): len = 59585.2, overlap = 15.75
PHY-3001 : Before Legalized: Len = 59585.2
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006823s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 63517.3, Over = 0
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2109 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.062538s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(85): len = 63494.3, overlap = 4.09375
PHY-3002 : Step(86): len = 63585.5, overlap = 4.125
PHY-3002 : Step(87): len = 62531.1, overlap = 4.5625
PHY-3002 : Step(88): len = 62483.3, overlap = 4.5
PHY-3002 : Step(89): len = 61281.1, overlap = 6.34375
PHY-3002 : Step(90): len = 60761.4, overlap = 7.28125
PHY-3002 : Step(91): len = 60586.8, overlap = 7.03125
PHY-3002 : Step(92): len = 60030.8, overlap = 6.84375
PHY-3002 : Step(93): len = 59122.2, overlap = 10.5938
PHY-3002 : Step(94): len = 58596.8, overlap = 11.4062
PHY-3002 : Step(95): len = 57522, overlap = 13.125
PHY-3002 : Step(96): len = 56925.1, overlap = 12.75
PHY-3002 : Step(97): len = 56252.1, overlap = 13.4375
PHY-3002 : Step(98): len = 56031.6, overlap = 12.75
PHY-3002 : Step(99): len = 55789.4, overlap = 12.3438
PHY-3002 : Step(100): len = 55420.2, overlap = 12.1875
PHY-3002 : Step(101): len = 54973.4, overlap = 11.9375
PHY-3002 : Step(102): len = 54581.6, overlap = 11.8125
PHY-3002 : Step(103): len = 54534.8, overlap = 11.2812
PHY-3002 : Step(104): len = 54064.8, overlap = 11.2812
PHY-3002 : Step(105): len = 53786, overlap = 11.1562
PHY-3002 : Step(106): len = 53785.9, overlap = 10.9688
PHY-3002 : Step(107): len = 53537.1, overlap = 9.40625
PHY-3002 : Step(108): len = 53210.4, overlap = 10
PHY-3002 : Step(109): len = 53206.9, overlap = 10.125
PHY-3002 : Step(110): len = 52636.6, overlap = 11.9375
PHY-3002 : Step(111): len = 52433.6, overlap = 11.625
PHY-3002 : Step(112): len = 52343.9, overlap = 11.9375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00206446
PHY-3002 : Step(113): len = 52092.1, overlap = 11.625
PHY-3002 : Step(114): len = 52044, overlap = 11.6875
PHY-3002 : Step(115): len = 52160.7, overlap = 12
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00412891
PHY-3002 : Step(116): len = 52002, overlap = 11.9375
PHY-3002 : Step(117): len = 51943.9, overlap = 11.9375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2109 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.072203s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (108.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.76342e-05
PHY-3002 : Step(118): len = 52282.5, overlap = 57.0312
PHY-3002 : Step(119): len = 53187.2, overlap = 54.9375
PHY-3002 : Step(120): len = 53755.7, overlap = 51.4375
PHY-3002 : Step(121): len = 53409.3, overlap = 51.5938
PHY-3002 : Step(122): len = 53109, overlap = 51.4375
PHY-3002 : Step(123): len = 52719.2, overlap = 52.0938
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000175268
PHY-3002 : Step(124): len = 52754.6, overlap = 52
PHY-3002 : Step(125): len = 53409.7, overlap = 48.3125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000350537
PHY-3002 : Step(126): len = 53365.9, overlap = 47.375
PHY-3002 : Step(127): len = 54391.8, overlap = 43
PHY-3002 : Step(128): len = 55475.5, overlap = 35.3125
PHY-3002 : Step(129): len = 55640.5, overlap = 34.6875
PHY-3002 : Step(130): len = 55532.5, overlap = 31.8125
PHY-3002 : Step(131): len = 55403.5, overlap = 31.9688
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7519, tnet num: 2109, tinst num: 1565, tnode num: 10681, tedge num: 12784.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 93.03 peak overflow 2.69
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2111.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58384, over cnt = 233(0%), over = 1069, worst = 25
PHY-1001 : End global iterations;  0.063179s wall, 0.093750s user + 0.046875s system = 0.140625s CPU (222.6%)

PHY-1001 : Congestion index: top1 = 47.24, top5 = 26.16, top10 = 16.53, top15 = 11.79.
PHY-1001 : End incremental global routing;  0.115084s wall, 0.140625s user + 0.046875s system = 0.187500s CPU (162.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2109 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067385s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (92.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.211414s wall, 0.234375s user + 0.046875s system = 0.281250s CPU (133.0%)

OPT-1001 : Current memory(MB): used = 208, reserve = 175, peak = 208.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1598/2111.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58384, over cnt = 233(0%), over = 1069, worst = 25
PHY-1002 : len = 66592, over cnt = 184(0%), over = 462, worst = 16
PHY-1002 : len = 71016, over cnt = 48(0%), over = 99, worst = 16
PHY-1002 : len = 72304, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 73024, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.114452s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (109.2%)

PHY-1001 : Congestion index: top1 = 40.09, top5 = 26.18, top10 = 18.57, top15 = 13.78.
OPT-1001 : End congestion update;  0.158966s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (108.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2109 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056997s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (109.7%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.218889s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (107.1%)

OPT-1001 : Current memory(MB): used = 211, reserve = 178, peak = 211.
OPT-1001 : End physical optimization;  0.688772s wall, 0.812500s user + 0.078125s system = 0.890625s CPU (129.3%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 362 LUT to BLE ...
SYN-4008 : Packed 362 LUT and 186 SEQ to BLE.
SYN-4003 : Packing 765 remaining SEQ's ...
SYN-4005 : Packed 82 SEQ with LUT/SLICE
SYN-4006 : 108 single LUT's are left
SYN-4006 : 683 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1045/1363 primitive instances ...
PHY-3001 : End packing;  0.052153s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (119.8%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 801 instances
RUN-1001 : 375 mslices, 375 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1933 nets
RUN-1001 : 1369 nets have 2 pins
RUN-1001 : 458 nets have [3 - 5] pins
RUN-1001 : 64 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 799 instances, 750 slices, 21 macros(203 instances: 133 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 55562.4, Over = 56
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6303, tnet num: 1931, tinst num: 799, tnode num: 8597, tedge num: 11164.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1931 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.291988s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (101.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.39312e-05
PHY-3002 : Step(132): len = 54513, overlap = 59.5
PHY-3002 : Step(133): len = 54110.1, overlap = 58.75
PHY-3002 : Step(134): len = 54002.8, overlap = 59.75
PHY-3002 : Step(135): len = 54110.2, overlap = 59.5
PHY-3002 : Step(136): len = 53939.8, overlap = 58
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.78624e-05
PHY-3002 : Step(137): len = 54069.3, overlap = 56.5
PHY-3002 : Step(138): len = 54503.2, overlap = 56
PHY-3002 : Step(139): len = 55115, overlap = 54.25
PHY-3002 : Step(140): len = 55384.1, overlap = 51.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000135725
PHY-3002 : Step(141): len = 55627.2, overlap = 50.5
PHY-3002 : Step(142): len = 56014.5, overlap = 49.75
PHY-3001 : Before Legalized: Len = 56014.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.086559s wall, 0.093750s user + 0.125000s system = 0.218750s CPU (252.7%)

PHY-3001 : After Legalized: Len = 70324.5, Over = 0
PHY-3001 : Trial Legalized: Len = 70324.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1931 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.057471s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00137463
PHY-3002 : Step(143): len = 66487, overlap = 7
PHY-3002 : Step(144): len = 64818.6, overlap = 13.25
PHY-3002 : Step(145): len = 63149.4, overlap = 14
PHY-3002 : Step(146): len = 61414.8, overlap = 19
PHY-3002 : Step(147): len = 60607.4, overlap = 19.75
PHY-3002 : Step(148): len = 60306.7, overlap = 21
PHY-3002 : Step(149): len = 59868.3, overlap = 22.5
PHY-3002 : Step(150): len = 59522.6, overlap = 24.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00253134
PHY-3002 : Step(151): len = 59700.3, overlap = 24.5
PHY-3002 : Step(152): len = 59640.8, overlap = 24
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00506267
PHY-3002 : Step(153): len = 59760.5, overlap = 22.5
PHY-3002 : Step(154): len = 59813.5, overlap = 22.25
PHY-3001 : Before Legalized: Len = 59813.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005393s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 64803.8, Over = 0
PHY-3001 : Legalized: Len = 64803.8, Over = 0
PHY-3001 : Spreading special nets. 9 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006174s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (253.1%)

PHY-3001 : 15 instances has been re-located, deltaX = 2, deltaY = 13, maxDist = 1.
PHY-3001 : Final: Len = 65071.8, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6303, tnet num: 1931, tinst num: 799, tnode num: 8597, tedge num: 11164.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 126/1933.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72744, over cnt = 133(0%), over = 204, worst = 7
PHY-1002 : len = 73192, over cnt = 87(0%), over = 110, worst = 4
PHY-1002 : len = 74288, over cnt = 20(0%), over = 23, worst = 2
PHY-1002 : len = 74584, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 74600, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.121175s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (116.1%)

PHY-1001 : Congestion index: top1 = 32.05, top5 = 22.61, top10 = 17.51, top15 = 13.83.
PHY-1001 : End incremental global routing;  0.172274s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (108.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1931 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060662s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.262973s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (106.9%)

OPT-1001 : Current memory(MB): used = 215, reserve = 182, peak = 215.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1697/1933.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 74600, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005299s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.05, top5 = 22.61, top10 = 17.51, top15 = 13.83.
OPT-1001 : End congestion update;  0.055324s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (84.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1931 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060491s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.3%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 759 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 799 instances, 750 slices, 21 macros(203 instances: 133 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 65072.4, Over = 0
PHY-3001 : End spreading;  0.006621s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (236.0%)

PHY-3001 : Final: Len = 65072.4, Over = 0
PHY-3001 : End incremental legalization;  0.037556s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (124.8%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.167347s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (93.4%)

OPT-1001 : Current memory(MB): used = 219, reserve = 186, peak = 219.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1931 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059861s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.4%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1693/1933.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 74600, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006949s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (224.8%)

PHY-1001 : Congestion index: top1 = 32.05, top5 = 22.61, top10 = 17.51, top15 = 13.83.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1931 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049027s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.6%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.620690
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.924841s wall, 0.921875s user + 0.015625s system = 0.937500s CPU (101.4%)

RUN-1003 : finish command "place" in  5.251030s wall, 7.203125s user + 3.140625s system = 10.343750s CPU (197.0%)

RUN-1004 : used memory is 202 MB, reserved memory is 170 MB, peak memory is 220 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 801 instances
RUN-1001 : 375 mslices, 375 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1933 nets
RUN-1001 : 1369 nets have 2 pins
RUN-1001 : 458 nets have [3 - 5] pins
RUN-1001 : 64 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6303, tnet num: 1931, tinst num: 799, tnode num: 8597, tedge num: 11164.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 375 mslices, 375 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1931 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70480, over cnt = 141(0%), over = 212, worst = 6
PHY-1002 : len = 71320, over cnt = 97(0%), over = 119, worst = 3
PHY-1002 : len = 72648, over cnt = 17(0%), over = 19, worst = 2
PHY-1002 : len = 72888, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.116776s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (160.6%)

PHY-1001 : Congestion index: top1 = 31.57, top5 = 22.38, top10 = 17.18, top15 = 13.55.
PHY-1001 : End global routing;  0.167195s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (140.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 235, reserve = 203, peak = 235.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 494, reserve = 466, peak = 494.
PHY-1001 : End build detailed router design. 3.190023s wall, 3.156250s user + 0.015625s system = 3.171875s CPU (99.4%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 30296, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.087814s wall, 1.062500s user + 0.015625s system = 1.078125s CPU (99.1%)

PHY-1001 : Current memory(MB): used = 526, reserve = 499, peak = 526.
PHY-1001 : End phase 1; 1.094422s wall, 1.062500s user + 0.015625s system = 1.078125s CPU (98.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 30% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 45% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 179144, over cnt = 25(0%), over = 25, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 528, reserve = 500, peak = 529.
PHY-1001 : End initial routed; 1.763208s wall, 2.609375s user + 0.171875s system = 2.781250s CPU (157.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1712(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.776   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.003   |  -45.800  |  21   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.352892s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (101.8%)

PHY-1001 : Current memory(MB): used = 530, reserve = 501, peak = 530.
PHY-1001 : End phase 2; 2.116192s wall, 2.968750s user + 0.171875s system = 3.140625s CPU (148.4%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 179144, over cnt = 25(0%), over = 25, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014901s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (104.9%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 178968, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.033620s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (92.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 178992, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.021832s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (71.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1712(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.776   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.003   |  -45.800  |  21   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.377612s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (99.3%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 7 feed throughs used by 6 nets
PHY-1001 : End commit to database; 0.167907s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (102.4%)

PHY-1001 : Current memory(MB): used = 544, reserve = 515, peak = 544.
PHY-1001 : End phase 3; 0.747087s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (100.4%)

PHY-1003 : Routed, final wirelength = 178992
PHY-1001 : Current memory(MB): used = 544, reserve = 516, peak = 544.
PHY-1001 : End export database. 0.010377s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  7.349516s wall, 8.125000s user + 0.218750s system = 8.343750s CPU (113.5%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6303, tnet num: 1931, tinst num: 799, tnode num: 8597, tedge num: 11164.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[34] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dib[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[3] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[41] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[5] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[6] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[8] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[0] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[3] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[1] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[2] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[16] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_52.mi[0] slack -2845ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_52.mi[1] slack -2627ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_55.mi[0] slack -3003ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_55.mi[1] slack -2632ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_58.mi[0] slack -2991ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_58.mi[1] slack -2871ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_61.mi[0] slack -2991ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_61.mi[1] slack -3003ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_64.mi[0] slack -3003ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_64.mi[1] slack -2991ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_67.mi[0] slack -2632ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_67.mi[1] slack -2871ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_70.mi[0] slack -2871ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_70.mi[1] slack -2763ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_73.mi[0] slack -2763ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_73.mi[1] slack -2632ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6427, tnet num: 1993, tinst num: 861, tnode num: 8721, tedge num: 11288.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_67_mi[0] slack -796ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_67_mi[1] slack -553ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_64_mi[1] slack -567ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_64_mi[0] slack -172ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_73_mi[1] slack -537ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_70_mi[0] slack -580ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_70_mi[1] slack -582ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_73_mi[0] slack -309ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_61_mi[1] slack -789ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg2_syn_58_mi[1] slack -255ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_55_mi[0] slack -1042ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_58_mi[0] slack -698ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_61_mi[0] slack -718ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_52_mi[1] slack -245ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_52_mi[0] slack -714ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_55_mi[1] slack -474ps
RUN-1001 : End hold fix;  3.103494s wall, 3.156250s user + 0.125000s system = 3.281250s CPU (105.7%)

RUN-1003 : finish command "route" in  10.959006s wall, 11.859375s user + 0.343750s system = 12.203125s CPU (111.4%)

RUN-1004 : used memory is 522 MB, reserved memory is 493 MB, peak memory is 544 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      896   out of  19600    4.57%
#reg                     1053   out of  19600    5.37%
#le                      1579
  #lut only               526   out of   1579   33.31%
  #reg only               683   out of   1579   43.26%
  #lut&reg                370   out of   1579   23.43%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                                      Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0                       478
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3                       101
#3        wendu/clk_us                    GCLK               lslice             signal_process/trans/clk_out_n_syn_52.q0    42
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4                       1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di                             1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1579   |693     |203     |1086    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1079   |299     |142     |865     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |23     |16      |7       |18      |0       |0       |
|    demodu                  |Demodulation                                     |458    |104     |45      |351     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |54     |29      |6       |45      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |6       |0       |16      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |13     |7       |0       |13      |0       |0       |
|    integ                   |Integration                                      |140    |26      |15      |112     |0       |0       |
|    modu                    |Modulation                                       |107    |57      |21      |103     |0       |1       |
|    rs422                   |Rs422Output                                      |313    |66      |46      |257     |0       |4       |
|    trans                   |SquareWaveGenerator                              |38     |30      |8       |24      |0       |0       |
|  u_uart                    |UART_Control                                     |177    |125     |7       |118     |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |26     |18      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |114    |79      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |305    |260     |45      |69      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1392  
    #2          2       332   
    #3          3       102   
    #4          4        24   
    #5        5-10       69   
    #6        11-50      31   
    #7       101-500     1    
  Average     1.93            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6427, tnet num: 1993, tinst num: 861, tnode num: 8721, tedge num: 11288.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1993 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 861
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1995, pip num: 14579
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 12
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1349 valid insts, and 39401 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.359804s wall, 18.875000s user + 0.109375s system = 18.984375s CPU (565.0%)

RUN-1004 : used memory is 520 MB, reserved memory is 493 MB, peak memory is 668 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250805_110926.log"
