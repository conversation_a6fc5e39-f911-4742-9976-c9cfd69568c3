============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Fri Aug 15 14:29:48 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1591 instances
RUN-0007 : 379 luts, 955 seqs, 136 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2140 nets
RUN-1001 : 1557 nets have 2 pins
RUN-1001 : 478 nets have [3 - 5] pins
RUN-1001 : 61 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     252     
RUN-1001 :   No   |  No   |  Yes  |     124     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1589 instances, 379 luts, 955 seqs, 206 slices, 21 macros(206 instances: 136 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7624, tnet num: 2138, tinst num: 1589, tnode num: 10797, tedge num: 12963.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2138 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.302857s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (98.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 578501
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1589.
PHY-3001 : End clustering;  0.000020s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 499809, overlap = 18
PHY-3002 : Step(2): len = 457124, overlap = 20.25
PHY-3002 : Step(3): len = 443911, overlap = 18
PHY-3002 : Step(4): len = 422930, overlap = 15.75
PHY-3002 : Step(5): len = 414209, overlap = 11.25
PHY-3002 : Step(6): len = 389411, overlap = 13.5
PHY-3002 : Step(7): len = 373358, overlap = 9
PHY-3002 : Step(8): len = 365567, overlap = 18
PHY-3002 : Step(9): len = 353890, overlap = 9
PHY-3002 : Step(10): len = 344292, overlap = 13.5
PHY-3002 : Step(11): len = 336954, overlap = 13.5
PHY-3002 : Step(12): len = 330705, overlap = 13.5
PHY-3002 : Step(13): len = 319313, overlap = 15.75
PHY-3002 : Step(14): len = 313165, overlap = 15.75
PHY-3002 : Step(15): len = 306043, overlap = 15.75
PHY-3002 : Step(16): len = 297766, overlap = 15.75
PHY-3002 : Step(17): len = 289333, overlap = 15.75
PHY-3002 : Step(18): len = 285648, overlap = 15.75
PHY-3002 : Step(19): len = 274706, overlap = 15.75
PHY-3002 : Step(20): len = 268437, overlap = 15.75
PHY-3002 : Step(21): len = 263917, overlap = 20.25
PHY-3002 : Step(22): len = 258140, overlap = 20.25
PHY-3002 : Step(23): len = 244977, overlap = 20.25
PHY-3002 : Step(24): len = 241539, overlap = 20.25
PHY-3002 : Step(25): len = 236649, overlap = 20.25
PHY-3002 : Step(26): len = 220740, overlap = 20.25
PHY-3002 : Step(27): len = 211327, overlap = 20.25
PHY-3002 : Step(28): len = 209884, overlap = 20.25
PHY-3002 : Step(29): len = 188452, overlap = 18
PHY-3002 : Step(30): len = 160605, overlap = 20.25
PHY-3002 : Step(31): len = 157505, overlap = 20.25
PHY-3002 : Step(32): len = 153232, overlap = 18
PHY-3002 : Step(33): len = 140305, overlap = 20.25
PHY-3002 : Step(34): len = 137891, overlap = 18
PHY-3002 : Step(35): len = 136733, overlap = 20.25
PHY-3002 : Step(36): len = 134366, overlap = 15.75
PHY-3002 : Step(37): len = 130092, overlap = 18
PHY-3002 : Step(38): len = 128059, overlap = 15.75
PHY-3002 : Step(39): len = 124041, overlap = 18
PHY-3002 : Step(40): len = 118277, overlap = 13.5
PHY-3002 : Step(41): len = 116859, overlap = 18
PHY-3002 : Step(42): len = 110884, overlap = 15.75
PHY-3002 : Step(43): len = 107082, overlap = 13.5
PHY-3002 : Step(44): len = 105114, overlap = 13.5
PHY-3002 : Step(45): len = 103167, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.70803e-05
PHY-3002 : Step(46): len = 103130, overlap = 13.5
PHY-3002 : Step(47): len = 102799, overlap = 13.5
PHY-3002 : Step(48): len = 102599, overlap = 15.75
PHY-3002 : Step(49): len = 102018, overlap = 15.75
PHY-3002 : Step(50): len = 100243, overlap = 13.5
PHY-3002 : Step(51): len = 96862.2, overlap = 13.5
PHY-3002 : Step(52): len = 95962.5, overlap = 13.5
PHY-3002 : Step(53): len = 94237.7, overlap = 13.5
PHY-3002 : Step(54): len = 93681.2, overlap = 15.75
PHY-3002 : Step(55): len = 91533.3, overlap = 13.5
PHY-3002 : Step(56): len = 90239.5, overlap = 13.5
PHY-3002 : Step(57): len = 87252.4, overlap = 15.75
PHY-3002 : Step(58): len = 86600, overlap = 15.75
PHY-3002 : Step(59): len = 85320.2, overlap = 13.5
PHY-3002 : Step(60): len = 83032.9, overlap = 11.25
PHY-3002 : Step(61): len = 80372.6, overlap = 11.25
PHY-3002 : Step(62): len = 79938.2, overlap = 11.25
PHY-3002 : Step(63): len = 79111.1, overlap = 11.25
PHY-3002 : Step(64): len = 77827.7, overlap = 16
PHY-3002 : Step(65): len = 77483.8, overlap = 16.375
PHY-3002 : Step(66): len = 76846.9, overlap = 16.4375
PHY-3002 : Step(67): len = 75204.1, overlap = 14.25
PHY-3002 : Step(68): len = 74652, overlap = 14.375
PHY-3002 : Step(69): len = 73773.5, overlap = 14.375
PHY-3002 : Step(70): len = 73240.5, overlap = 14.4375
PHY-3002 : Step(71): len = 72873.1, overlap = 14.375
PHY-3002 : Step(72): len = 71527.6, overlap = 18.75
PHY-3002 : Step(73): len = 70392.9, overlap = 12.0625
PHY-3002 : Step(74): len = 68955, overlap = 9.625
PHY-3002 : Step(75): len = 67913.4, overlap = 12
PHY-3002 : Step(76): len = 67849.1, overlap = 14.4375
PHY-3002 : Step(77): len = 67290, overlap = 16.75
PHY-3002 : Step(78): len = 65299.4, overlap = 17.3125
PHY-3002 : Step(79): len = 64640.8, overlap = 17.125
PHY-3002 : Step(80): len = 63623, overlap = 17.125
PHY-3002 : Step(81): len = 63158, overlap = 14.5
PHY-3002 : Step(82): len = 62402.7, overlap = 14.25
PHY-3002 : Step(83): len = 61419.1, overlap = 14.25
PHY-3002 : Step(84): len = 61150.2, overlap = 12.625
PHY-3002 : Step(85): len = 60904.7, overlap = 12.625
PHY-3002 : Step(86): len = 60742.1, overlap = 14.875
PHY-3002 : Step(87): len = 60667.1, overlap = 14.6875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000154161
PHY-3002 : Step(88): len = 60852.1, overlap = 14.6875
PHY-3002 : Step(89): len = 60738.3, overlap = 14.6875
PHY-3002 : Step(90): len = 60661.4, overlap = 12.4375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000308321
PHY-3002 : Step(91): len = 60876.8, overlap = 12.4375
PHY-3002 : Step(92): len = 60947.9, overlap = 12.5
PHY-3001 : Before Legalized: Len = 60947.9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007232s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 64176.8, Over = 1.25
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2138 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.062573s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(93): len = 64057.6, overlap = 9
PHY-3002 : Step(94): len = 64329.7, overlap = 10.2188
PHY-3002 : Step(95): len = 63358, overlap = 9.78125
PHY-3002 : Step(96): len = 63241.1, overlap = 9.65625
PHY-3002 : Step(97): len = 62018.7, overlap = 11.9688
PHY-3002 : Step(98): len = 61375.2, overlap = 11.6562
PHY-3002 : Step(99): len = 61219.8, overlap = 12.1875
PHY-3002 : Step(100): len = 60609.3, overlap = 10.3125
PHY-3002 : Step(101): len = 59258.3, overlap = 11.625
PHY-3002 : Step(102): len = 58475, overlap = 11.6875
PHY-3002 : Step(103): len = 57374.7, overlap = 10.375
PHY-3002 : Step(104): len = 56864.1, overlap = 10.3125
PHY-3002 : Step(105): len = 56623.3, overlap = 11.5
PHY-3002 : Step(106): len = 56494, overlap = 11.8125
PHY-3002 : Step(107): len = 55977.6, overlap = 12
PHY-3002 : Step(108): len = 55732.4, overlap = 12.125
PHY-3002 : Step(109): len = 55459, overlap = 12.125
PHY-3002 : Step(110): len = 55091.5, overlap = 12.5
PHY-3002 : Step(111): len = 54827.2, overlap = 12.6875
PHY-3002 : Step(112): len = 54591.5, overlap = 12.875
PHY-3002 : Step(113): len = 53930.4, overlap = 13.125
PHY-3002 : Step(114): len = 53462.6, overlap = 13.875
PHY-3002 : Step(115): len = 53240.9, overlap = 16.9688
PHY-3002 : Step(116): len = 52849, overlap = 15.5938
PHY-3002 : Step(117): len = 52283, overlap = 15.7188
PHY-3002 : Step(118): len = 52004.8, overlap = 16.3438
PHY-3002 : Step(119): len = 51710.4, overlap = 16.5625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000330459
PHY-3002 : Step(120): len = 51587.6, overlap = 16.3438
PHY-3002 : Step(121): len = 51607.2, overlap = 16.3438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000660918
PHY-3002 : Step(122): len = 51568.5, overlap = 16.2812
PHY-3002 : Step(123): len = 51709.8, overlap = 16.5938
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2138 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.067437s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (92.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.63205e-05
PHY-3002 : Step(124): len = 51740.7, overlap = 60.8438
PHY-3002 : Step(125): len = 52727.7, overlap = 54.25
PHY-3002 : Step(126): len = 53256.5, overlap = 54.9688
PHY-3002 : Step(127): len = 53112.6, overlap = 53.875
PHY-3002 : Step(128): len = 52721.4, overlap = 54.5312
PHY-3002 : Step(129): len = 52654.9, overlap = 54.1875
PHY-3002 : Step(130): len = 52579.9, overlap = 43.875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000172641
PHY-3002 : Step(131): len = 52557.7, overlap = 43.75
PHY-3002 : Step(132): len = 53457, overlap = 42.4062
PHY-3002 : Step(133): len = 53644.3, overlap = 42.1875
PHY-3002 : Step(134): len = 53649.6, overlap = 41.4375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000345282
PHY-3002 : Step(135): len = 54057.9, overlap = 39.8438
PHY-3002 : Step(136): len = 54783.3, overlap = 37.8438
PHY-3002 : Step(137): len = 55101.9, overlap = 37.2812
PHY-3002 : Step(138): len = 55032.5, overlap = 36.375
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7624, tnet num: 2138, tinst num: 1589, tnode num: 10797, tedge num: 12963.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 92.69 peak overflow 2.62
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2140.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 57808, over cnt = 233(0%), over = 992, worst = 23
PHY-1001 : End global iterations;  0.062101s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (125.8%)

PHY-1001 : Congestion index: top1 = 46.38, top5 = 25.61, top10 = 16.35, top15 = 11.63.
PHY-1001 : End incremental global routing;  0.110116s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (113.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2138 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067472s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (92.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.206086s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (106.1%)

OPT-1001 : Current memory(MB): used = 209, reserve = 176, peak = 209.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1638/2140.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 57808, over cnt = 233(0%), over = 992, worst = 23
PHY-1002 : len = 65152, over cnt = 176(0%), over = 359, worst = 22
PHY-1002 : len = 69352, over cnt = 30(0%), over = 39, worst = 6
PHY-1002 : len = 69736, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 70152, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.089118s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (122.7%)

PHY-1001 : Congestion index: top1 = 39.44, top5 = 25.34, top10 = 18.17, top15 = 13.47.
OPT-1001 : End congestion update;  0.132270s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (106.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2138 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.054809s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (114.0%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.190309s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (106.7%)

OPT-1001 : Current memory(MB): used = 212, reserve = 179, peak = 212.
OPT-1001 : End physical optimization;  0.641393s wall, 0.656250s user + 0.000000s system = 0.656250s CPU (102.3%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 379 LUT to BLE ...
SYN-4008 : Packed 379 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 766 remaining SEQ's ...
SYN-4005 : Packed 96 SEQ with LUT/SLICE
SYN-4006 : 111 single LUT's are left
SYN-4006 : 670 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1049/1370 primitive instances ...
PHY-3001 : End packing;  0.061135s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.2%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 809 instances
RUN-1001 : 379 mslices, 379 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1959 nets
RUN-1001 : 1382 nets have 2 pins
RUN-1001 : 467 nets have [3 - 5] pins
RUN-1001 : 65 nets have [6 - 10] pins
RUN-1001 : 24 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 807 instances, 758 slices, 21 macros(206 instances: 136 mslices 70 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 55078, Over = 60
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6409, tnet num: 1957, tinst num: 807, tnode num: 8713, tedge num: 11351.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1957 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.283936s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (104.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.26394e-05
PHY-3002 : Step(139): len = 54238.1, overlap = 58.75
PHY-3002 : Step(140): len = 53546.8, overlap = 58.5
PHY-3002 : Step(141): len = 53390.5, overlap = 58.75
PHY-3002 : Step(142): len = 53703, overlap = 57.75
PHY-3002 : Step(143): len = 53526.8, overlap = 59
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.52789e-05
PHY-3002 : Step(144): len = 53802.9, overlap = 59.25
PHY-3002 : Step(145): len = 54614.5, overlap = 63
PHY-3002 : Step(146): len = 55061.3, overlap = 61.75
PHY-3002 : Step(147): len = 55023, overlap = 61
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000130558
PHY-3002 : Step(148): len = 55293.2, overlap = 59.75
PHY-3002 : Step(149): len = 55855.7, overlap = 57.5
PHY-3001 : Before Legalized: Len = 55855.7
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.090804s wall, 0.046875s user + 0.140625s system = 0.187500s CPU (206.5%)

PHY-3001 : After Legalized: Len = 70180.5, Over = 0
PHY-3001 : Trial Legalized: Len = 70180.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1957 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.046561s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000735031
PHY-3002 : Step(150): len = 66187.5, overlap = 10.25
PHY-3002 : Step(151): len = 64961.7, overlap = 14.5
PHY-3002 : Step(152): len = 62901.7, overlap = 18.75
PHY-3002 : Step(153): len = 61687.4, overlap = 22.25
PHY-3002 : Step(154): len = 61041.5, overlap = 26
PHY-3002 : Step(155): len = 60816.9, overlap = 29
PHY-3002 : Step(156): len = 60480.1, overlap = 28.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00147006
PHY-3002 : Step(157): len = 60828.7, overlap = 28
PHY-3002 : Step(158): len = 61016.3, overlap = 27.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00294012
PHY-3002 : Step(159): len = 61175.9, overlap = 27.5
PHY-3002 : Step(160): len = 61289.7, overlap = 27
PHY-3001 : Before Legalized: Len = 61289.7
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005261s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 65339.4, Over = 0
PHY-3001 : Legalized: Len = 65339.4, Over = 0
PHY-3001 : Spreading special nets. 8 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005293s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 11 instances has been re-located, deltaX = 7, deltaY = 5, maxDist = 2.
PHY-3001 : Final: Len = 65683.4, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6409, tnet num: 1957, tinst num: 807, tnode num: 8713, tedge num: 11351.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 94/1959.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72664, over cnt = 146(0%), over = 218, worst = 7
PHY-1002 : len = 73272, over cnt = 79(0%), over = 98, worst = 3
PHY-1002 : len = 74544, over cnt = 4(0%), over = 5, worst = 2
PHY-1002 : len = 74608, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 74640, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.139619s wall, 0.203125s user + 0.046875s system = 0.250000s CPU (179.1%)

PHY-1001 : Congestion index: top1 = 31.90, top5 = 23.39, top10 = 18.19, top15 = 14.21.
PHY-1001 : End incremental global routing;  0.194520s wall, 0.265625s user + 0.046875s system = 0.312500s CPU (160.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1957 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059776s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (78.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.284518s wall, 0.343750s user + 0.046875s system = 0.390625s CPU (137.3%)

OPT-1001 : Current memory(MB): used = 211, reserve = 180, peak = 214.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1727/1959.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 74640, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005954s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (262.4%)

PHY-1001 : Congestion index: top1 = 31.90, top5 = 23.39, top10 = 18.19, top15 = 14.21.
OPT-1001 : End congestion update;  0.053465s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (116.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1957 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048821s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (96.0%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 767 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 807 instances, 758 slices, 21 macros(206 instances: 136 mslices 70 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 65644, Over = 0
PHY-3001 : Spreading special nets. 1 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006200s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 2 instances has been re-located, deltaX = 2, deltaY = 4, maxDist = 3.
PHY-3001 : Final: Len = 65750, Over = 0
PHY-3001 : End incremental legalization;  0.034769s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (89.9%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 4 cells processed and 250 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.150633s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (176.3%)

OPT-1001 : Current memory(MB): used = 216, reserve = 184, peak = 216.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1957 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049108s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.5%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1709/1959.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 74648, over cnt = 2(0%), over = 4, worst = 2
PHY-1002 : len = 74664, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 74696, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.026107s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (119.7%)

PHY-1001 : Congestion index: top1 = 31.85, top5 = 23.39, top10 = 18.21, top15 = 14.21.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1957 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047075s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (99.6%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.482759
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.868090s wall, 1.031250s user + 0.062500s system = 1.093750s CPU (126.0%)

RUN-1003 : finish command "place" in  5.074706s wall, 7.234375s user + 3.000000s system = 10.234375s CPU (201.7%)

RUN-1004 : used memory is 195 MB, reserved memory is 162 MB, peak memory is 217 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 809 instances
RUN-1001 : 379 mslices, 379 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1959 nets
RUN-1001 : 1382 nets have 2 pins
RUN-1001 : 467 nets have [3 - 5] pins
RUN-1001 : 65 nets have [6 - 10] pins
RUN-1001 : 24 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6409, tnet num: 1957, tinst num: 807, tnode num: 8713, tedge num: 11351.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 379 mslices, 379 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1957 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71288, over cnt = 142(0%), over = 219, worst = 7
PHY-1002 : len = 72184, over cnt = 81(0%), over = 98, worst = 3
PHY-1002 : len = 73568, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 73600, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.118052s wall, 0.125000s user + 0.046875s system = 0.171875s CPU (145.6%)

PHY-1001 : Congestion index: top1 = 32.11, top5 = 23.35, top10 = 18.06, top15 = 14.06.
PHY-1001 : End global routing;  0.168082s wall, 0.171875s user + 0.046875s system = 0.218750s CPU (130.1%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 231, reserve = 200, peak = 234.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 492, reserve = 464, peak = 492.
PHY-1001 : End build detailed router design. 3.065794s wall, 3.000000s user + 0.062500s system = 3.062500s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 32304, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.058835s wall, 1.046875s user + 0.000000s system = 1.046875s CPU (98.9%)

PHY-1001 : Current memory(MB): used = 524, reserve = 497, peak = 524.
PHY-1001 : End phase 1; 1.064750s wall, 1.046875s user + 0.000000s system = 1.046875s CPU (98.3%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 30% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 181312, over cnt = 33(0%), over = 33, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 527, reserve = 498, peak = 527.
PHY-1001 : End initial routed; 1.406068s wall, 2.109375s user + 0.109375s system = 2.218750s CPU (157.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1735(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.776   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.827   |  -43.633  |  22   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.329818s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (104.2%)

PHY-1001 : Current memory(MB): used = 530, reserve = 501, peak = 530.
PHY-1001 : End phase 2; 1.735975s wall, 2.453125s user + 0.109375s system = 2.562500s CPU (147.6%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 181312, over cnt = 33(0%), over = 33, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014049s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (111.2%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 181088, over cnt = 9(0%), over = 9, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.027973s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (111.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 181168, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.020217s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (77.3%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 181200, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.021530s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (72.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1735(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.776   |   0.000   |   0   
RUN-1001 :   Hold   |  -2.827   |  -43.633  |  22   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.324317s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (101.2%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 7 feed throughs used by 6 nets
PHY-1001 : End commit to database; 0.170243s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (101.0%)

PHY-1001 : Current memory(MB): used = 544, reserve = 516, peak = 544.
PHY-1001 : End phase 3; 0.707589s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (99.4%)

PHY-1003 : Routed, final wirelength = 181200
PHY-1001 : Current memory(MB): used = 545, reserve = 517, peak = 545.
PHY-1001 : End export database. 0.009392s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (166.4%)

PHY-1001 : End detail routing;  6.759454s wall, 7.375000s user + 0.187500s system = 7.562500s CPU (111.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6409, tnet num: 1957, tinst num: 807, tnode num: 8713, tedge num: 11351.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[18] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dia[0] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[21] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dia[3] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[31] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dib[4] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[35] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dib[8] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[6] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[4] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[5] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[0] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dia[0] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[2] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_52.mi[0] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_52.mi[1] slack -2827ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_55.mi[0] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_55.mi[1] slack -2587ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_58.mi[0] slack -2827ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_58.mi[1] slack -2695ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_61.mi[0] slack -2681ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_61.mi[1] slack -2695ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_64.mi[0] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_64.mi[1] slack -2451ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_67.mi[0] slack -2456ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_67.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_70.mi[0] slack -2695ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_70.mi[1] slack -2669ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_73.mi[0] slack -2695ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_73.mi[1] slack -2827ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6525, tnet num: 2015, tinst num: 865, tnode num: 8829, tedge num: 11467.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_52_mi[0] slack -146ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_55_mi[1] slack -869ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_52_mi[1] slack -623ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_58_mi[0] slack -623ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_58_mi[1] slack -533ps
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_64_mi[1] slack -125ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_64_mi[0] slack -21ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg2_syn_67_mi[0] slack -413ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_67_mi[1] slack -120ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_73_mi[1] slack -173ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_73_mi[0] slack -195ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_70_mi[0] slack -128ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_70_mi[1] slack -247ps
RUN-1001 : End hold fix;  3.038174s wall, 3.140625s user + 0.218750s system = 3.359375s CPU (110.6%)

RUN-1003 : finish command "route" in  10.275328s wall, 11.015625s user + 0.453125s system = 11.468750s CPU (111.6%)

RUN-1004 : used memory is 501 MB, reserved memory is 478 MB, peak memory is 545 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   15
  #output                  21
  #inout                    1

Utilization Statistics
#lut                      913   out of  19600    4.66%
#reg                     1058   out of  19600    5.40%
#le                      1583
  #lut only               525   out of   1583   33.16%
  #reg only               670   out of   1583   42.32%
  #lut&reg                388   out of   1583   24.51%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    13
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                                      Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0                       483
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3                       103
#3        wendu/clk_us                    GCLK               lslice             signal_process/trans/clk_out_n_syn_48.q0    40
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4                       1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di                             1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
  RxTransmit       INPUT        M14        LVCMOS33          N/A          PULLUP      IREG    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1583   |707     |206     |1092    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1072   |299     |142     |868     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |27     |23      |4       |22      |0       |0       |
|    demodu                  |Demodulation                                     |450    |103     |46      |350     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |54     |33      |6       |46      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |14     |9       |0       |14      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |14     |6       |0       |14      |0       |0       |
|    integ                   |Integration                                      |141    |18      |15      |113     |0       |0       |
|    modu                    |Modulation                                       |102    |52      |23      |98      |0       |1       |
|    rs422                   |Rs422Output                                      |318    |77      |46      |263     |0       |4       |
|    trans                   |SquareWaveGenerator                              |34     |26      |8       |22      |0       |0       |
|  u_uart                    |UART_Control                                     |177    |131     |7       |117     |0       |0       |
|    U0                      |speed_select_Tx                                  |34     |27      |7       |14      |0       |0       |
|    U1                      |uart_tx                                          |26     |19      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |117    |85      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |310    |265     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1401  
    #2          2       337   
    #3          3       100   
    #4          4        30   
    #5        5-10       71   
    #6        11-50      33   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6525, tnet num: 2015, tinst num: 865, tnode num: 8829, tedge num: 11467.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2015 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 865
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2017, pip num: 14903
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 11
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1334 valid insts, and 39892 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.077543s wall, 17.687500s user + 0.031250s system = 17.718750s CPU (575.7%)

RUN-1004 : used memory is 518 MB, reserved memory is 490 MB, peak memory is 668 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250815_142948.log"
