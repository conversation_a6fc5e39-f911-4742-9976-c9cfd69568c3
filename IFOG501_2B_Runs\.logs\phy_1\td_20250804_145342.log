============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Mon Aug  4 14:53:42 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1584 instances
RUN-0007 : 373 luts, 964 seqs, 126 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2117 nets
RUN-1001 : 1541 nets have 2 pins
RUN-1001 : 474 nets have [3 - 5] pins
RUN-1001 : 60 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     263     
RUN-1001 :   No   |  No   |  Yes  |     122     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1582 instances, 373 luts, 964 seqs, 196 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7542, tnet num: 2115, tinst num: 1582, tnode num: 10711, tedge num: 12780.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2115 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.255528s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (104.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 540766
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1582.
PHY-3001 : End clustering;  0.000027s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 473601, overlap = 18
PHY-3002 : Step(2): len = 446089, overlap = 20.25
PHY-3002 : Step(3): len = 423642, overlap = 18
PHY-3002 : Step(4): len = 413738, overlap = 18
PHY-3002 : Step(5): len = 399592, overlap = 13.5
PHY-3002 : Step(6): len = 385490, overlap = 15.75
PHY-3002 : Step(7): len = 375406, overlap = 11.25
PHY-3002 : Step(8): len = 367187, overlap = 13.5
PHY-3002 : Step(9): len = 355276, overlap = 13.5
PHY-3002 : Step(10): len = 345582, overlap = 13.5
PHY-3002 : Step(11): len = 340754, overlap = 13.5
PHY-3002 : Step(12): len = 331605, overlap = 13.5
PHY-3002 : Step(13): len = 325894, overlap = 13.5
PHY-3002 : Step(14): len = 318965, overlap = 13.5
PHY-3002 : Step(15): len = 313376, overlap = 13.5
PHY-3002 : Step(16): len = 304785, overlap = 13.5
PHY-3002 : Step(17): len = 299636, overlap = 15.75
PHY-3002 : Step(18): len = 294140, overlap = 15.75
PHY-3002 : Step(19): len = 288131, overlap = 15.75
PHY-3002 : Step(20): len = 279964, overlap = 15.75
PHY-3002 : Step(21): len = 276204, overlap = 15.75
PHY-3002 : Step(22): len = 269585, overlap = 13.5
PHY-3002 : Step(23): len = 265673, overlap = 13.5
PHY-3002 : Step(24): len = 259406, overlap = 13.5
PHY-3002 : Step(25): len = 254670, overlap = 13.5
PHY-3002 : Step(26): len = 248367, overlap = 13.5
PHY-3002 : Step(27): len = 244423, overlap = 13.5
PHY-3002 : Step(28): len = 239570, overlap = 15.75
PHY-3002 : Step(29): len = 233817, overlap = 20.25
PHY-3002 : Step(30): len = 227599, overlap = 20.25
PHY-3002 : Step(31): len = 223930, overlap = 20.25
PHY-3002 : Step(32): len = 217937, overlap = 20.25
PHY-3002 : Step(33): len = 214073, overlap = 20.25
PHY-3002 : Step(34): len = 209393, overlap = 20.25
PHY-3002 : Step(35): len = 202207, overlap = 20.25
PHY-3002 : Step(36): len = 194591, overlap = 20.25
PHY-3002 : Step(37): len = 192843, overlap = 20.25
PHY-3002 : Step(38): len = 174751, overlap = 18
PHY-3002 : Step(39): len = 158963, overlap = 18
PHY-3002 : Step(40): len = 156013, overlap = 18
PHY-3002 : Step(41): len = 152952, overlap = 15.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000113038
PHY-3002 : Step(42): len = 152797, overlap = 11.25
PHY-3002 : Step(43): len = 152290, overlap = 11.25
PHY-3002 : Step(44): len = 151864, overlap = 11.25
PHY-3002 : Step(45): len = 150708, overlap = 11.25
PHY-3002 : Step(46): len = 146717, overlap = 9
PHY-3002 : Step(47): len = 145462, overlap = 9
PHY-3002 : Step(48): len = 143567, overlap = 11.25
PHY-3002 : Step(49): len = 140588, overlap = 11.25
PHY-3002 : Step(50): len = 136745, overlap = 11.25
PHY-3002 : Step(51): len = 134783, overlap = 9
PHY-3002 : Step(52): len = 133054, overlap = 11.25
PHY-3002 : Step(53): len = 128190, overlap = 18
PHY-3002 : Step(54): len = 123002, overlap = 18
PHY-3002 : Step(55): len = 121673, overlap = 18
PHY-3002 : Step(56): len = 117315, overlap = 11.25
PHY-3002 : Step(57): len = 114748, overlap = 11.25
PHY-3002 : Step(58): len = 112982, overlap = 13.5
PHY-3002 : Step(59): len = 110358, overlap = 18
PHY-3002 : Step(60): len = 109555, overlap = 18
PHY-3002 : Step(61): len = 108186, overlap = 15.75
PHY-3002 : Step(62): len = 106937, overlap = 13.5
PHY-3002 : Step(63): len = 102601, overlap = 13.5
PHY-3002 : Step(64): len = 99626.5, overlap = 11.25
PHY-3002 : Step(65): len = 98368.1, overlap = 15.75
PHY-3002 : Step(66): len = 97350.4, overlap = 18
PHY-3002 : Step(67): len = 95662.3, overlap = 18
PHY-3002 : Step(68): len = 94093.2, overlap = 15.75
PHY-3002 : Step(69): len = 88873.3, overlap = 9
PHY-3002 : Step(70): len = 86937.5, overlap = 11.25
PHY-3002 : Step(71): len = 86115.9, overlap = 11.25
PHY-3002 : Step(72): len = 85844, overlap = 9
PHY-3002 : Step(73): len = 83222.9, overlap = 6.75
PHY-3002 : Step(74): len = 81469.2, overlap = 9
PHY-3002 : Step(75): len = 80289.4, overlap = 6.75
PHY-3002 : Step(76): len = 77834, overlap = 9
PHY-3002 : Step(77): len = 76823.8, overlap = 11.25
PHY-3002 : Step(78): len = 76338.1, overlap = 6.75
PHY-3002 : Step(79): len = 75705.9, overlap = 6.75
PHY-3002 : Step(80): len = 74999.3, overlap = 6.75
PHY-3002 : Step(81): len = 74252.5, overlap = 9
PHY-3002 : Step(82): len = 73982.7, overlap = 11.25
PHY-3002 : Step(83): len = 73124.1, overlap = 9
PHY-3002 : Step(84): len = 72599.7, overlap = 9
PHY-3002 : Step(85): len = 72303.9, overlap = 9
PHY-3002 : Step(86): len = 69826.2, overlap = 4.5
PHY-3002 : Step(87): len = 67925.3, overlap = 6.75
PHY-3002 : Step(88): len = 67317.6, overlap = 4.5
PHY-3002 : Step(89): len = 67504.5, overlap = 6.75
PHY-3002 : Step(90): len = 67591, overlap = 6.75
PHY-3002 : Step(91): len = 67141.2, overlap = 6.75
PHY-3002 : Step(92): len = 67022.8, overlap = 9
PHY-3002 : Step(93): len = 65801.8, overlap = 9
PHY-3002 : Step(94): len = 62098.5, overlap = 9
PHY-3002 : Step(95): len = 61166.1, overlap = 9
PHY-3002 : Step(96): len = 60512.4, overlap = 9.3125
PHY-3002 : Step(97): len = 60242, overlap = 9.125
PHY-3002 : Step(98): len = 60222, overlap = 6.9375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000226076
PHY-3002 : Step(99): len = 60354.7, overlap = 6.9375
PHY-3002 : Step(100): len = 60276.6, overlap = 6.9375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000452152
PHY-3002 : Step(101): len = 60418, overlap = 9.1875
PHY-3002 : Step(102): len = 60465.7, overlap = 9.1875
PHY-3001 : Before Legalized: Len = 60465.7
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006741s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 62652.8, Over = 0.1875
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2115 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.066188s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (94.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(103): len = 62817, overlap = 4.5625
PHY-3002 : Step(104): len = 61582.3, overlap = 5.5
PHY-3002 : Step(105): len = 61010.1, overlap = 5.75
PHY-3002 : Step(106): len = 59687.4, overlap = 6.4375
PHY-3002 : Step(107): len = 58756.1, overlap = 6.5
PHY-3002 : Step(108): len = 57564.2, overlap = 8.125
PHY-3002 : Step(109): len = 56728.4, overlap = 8.5625
PHY-3002 : Step(110): len = 55368.4, overlap = 9.03125
PHY-3002 : Step(111): len = 53999.2, overlap = 11.2188
PHY-3002 : Step(112): len = 52401.5, overlap = 14.125
PHY-3002 : Step(113): len = 51286.8, overlap = 14.625
PHY-3002 : Step(114): len = 49471.6, overlap = 15.4375
PHY-3002 : Step(115): len = 47227.3, overlap = 18.2188
PHY-3002 : Step(116): len = 46342.4, overlap = 18.4375
PHY-3002 : Step(117): len = 45780.2, overlap = 19.4375
PHY-3002 : Step(118): len = 44828.4, overlap = 20.8125
PHY-3002 : Step(119): len = 44527.5, overlap = 21.4688
PHY-3002 : Step(120): len = 44216, overlap = 21.9062
PHY-3002 : Step(121): len = 44070.2, overlap = 22.125
PHY-3002 : Step(122): len = 43562.9, overlap = 22.7812
PHY-3002 : Step(123): len = 43520.1, overlap = 23
PHY-3002 : Step(124): len = 43393.1, overlap = 22.875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000137584
PHY-3002 : Step(125): len = 43281.7, overlap = 22.4375
PHY-3002 : Step(126): len = 43275, overlap = 22.375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000275168
PHY-3002 : Step(127): len = 43210.6, overlap = 22.3125
PHY-3002 : Step(128): len = 43267.8, overlap = 22.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2115 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.061764s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.44596e-05
PHY-3002 : Step(129): len = 43266.1, overlap = 71.75
PHY-3002 : Step(130): len = 43766.8, overlap = 70.4375
PHY-3002 : Step(131): len = 44112.3, overlap = 64
PHY-3002 : Step(132): len = 44630, overlap = 62.375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000108919
PHY-3002 : Step(133): len = 44572.4, overlap = 56.9375
PHY-3002 : Step(134): len = 45328.8, overlap = 52.0938
PHY-3002 : Step(135): len = 45857.5, overlap = 50.9375
PHY-3002 : Step(136): len = 46181.2, overlap = 46.4375
PHY-3002 : Step(137): len = 46374.2, overlap = 43.2812
PHY-3002 : Step(138): len = 46551.5, overlap = 43.0938
PHY-3002 : Step(139): len = 46801.7, overlap = 42.9688
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000217838
PHY-3002 : Step(140): len = 47170.1, overlap = 42.2188
PHY-3002 : Step(141): len = 47442.1, overlap = 40.9688
PHY-3002 : Step(142): len = 47756.5, overlap = 40.7188
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000435677
PHY-3002 : Step(143): len = 48350.5, overlap = 34.3438
PHY-3002 : Step(144): len = 48638.7, overlap = 33.6562
PHY-3002 : Step(145): len = 49609.9, overlap = 30.2812
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.000871353
PHY-3002 : Step(146): len = 49484.1, overlap = 30.5
PHY-3002 : Step(147): len = 50238.7, overlap = 29.6875
PHY-3002 : Step(148): len = 50622.9, overlap = 27.875
PHY-3002 : Step(149): len = 50762.2, overlap = 27.4375
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7542, tnet num: 2115, tinst num: 1582, tnode num: 10711, tedge num: 12780.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 88.22 peak overflow 2.75
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2117.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53056, over cnt = 237(0%), over = 931, worst = 17
PHY-1001 : End global iterations;  0.074619s wall, 0.093750s user + 0.015625s system = 0.109375s CPU (146.6%)

PHY-1001 : Congestion index: top1 = 38.94, top5 = 24.18, top10 = 15.69, top15 = 11.28.
PHY-1001 : End incremental global routing;  0.130102s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (120.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2115 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.068568s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (113.9%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.227965s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (109.7%)

OPT-1001 : Current memory(MB): used = 209, reserve = 176, peak = 209.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1659/2117.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53056, over cnt = 237(0%), over = 931, worst = 17
PHY-1002 : len = 57712, over cnt = 166(0%), over = 494, worst = 17
PHY-1002 : len = 63152, over cnt = 52(0%), over = 122, worst = 11
PHY-1002 : len = 63896, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 64056, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.087933s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (106.6%)

PHY-1001 : Congestion index: top1 = 34.94, top5 = 23.90, top10 = 17.29, top15 = 12.92.
OPT-1001 : End congestion update;  0.134373s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (104.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2115 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059298s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.4%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.196976s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (103.1%)

OPT-1001 : Current memory(MB): used = 213, reserve = 180, peak = 213.
OPT-1001 : End physical optimization;  0.677278s wall, 0.671875s user + 0.015625s system = 0.687500s CPU (101.5%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 373 LUT to BLE ...
SYN-4008 : Packed 373 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 775 remaining SEQ's ...
SYN-4005 : Packed 97 SEQ with LUT/SLICE
SYN-4006 : 103 single LUT's are left
SYN-4006 : 678 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1051/1362 primitive instances ...
PHY-3001 : End packing;  0.049818s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.1%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 800 instances
RUN-1001 : 374 mslices, 375 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1936 nets
RUN-1001 : 1367 nets have 2 pins
RUN-1001 : 463 nets have [3 - 5] pins
RUN-1001 : 65 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 798 instances, 749 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 50619.2, Over = 53
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6308, tnet num: 1934, tinst num: 798, tnode num: 8605, tedge num: 11134.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.277861s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (101.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.98587e-05
PHY-3002 : Step(150): len = 50145.9, overlap = 55.5
PHY-3002 : Step(151): len = 49570.6, overlap = 53.5
PHY-3002 : Step(152): len = 49299.6, overlap = 56.75
PHY-3002 : Step(153): len = 49302.7, overlap = 56.5
PHY-3002 : Step(154): len = 48809.9, overlap = 58.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.97175e-05
PHY-3002 : Step(155): len = 49178, overlap = 56.25
PHY-3002 : Step(156): len = 49605.7, overlap = 53
PHY-3002 : Step(157): len = 50408, overlap = 47.5
PHY-3002 : Step(158): len = 50653.5, overlap = 47.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000159435
PHY-3002 : Step(159): len = 50895.7, overlap = 46
PHY-3002 : Step(160): len = 51092.1, overlap = 44.5
PHY-3001 : Before Legalized: Len = 51092.1
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.088960s wall, 0.046875s user + 0.203125s system = 0.250000s CPU (281.0%)

PHY-3001 : After Legalized: Len = 63679.1, Over = 0
PHY-3001 : Trial Legalized: Len = 63679.1
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.051669s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (90.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000857486
PHY-3002 : Step(161): len = 60390.1, overlap = 6.75
PHY-3002 : Step(162): len = 58572.9, overlap = 10.75
PHY-3002 : Step(163): len = 56853.1, overlap = 16
PHY-3002 : Step(164): len = 55838.8, overlap = 20
PHY-3002 : Step(165): len = 55469.8, overlap = 21
PHY-3002 : Step(166): len = 55208.3, overlap = 22.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00171497
PHY-3002 : Step(167): len = 55417.8, overlap = 21.75
PHY-3002 : Step(168): len = 55445.1, overlap = 21.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00342995
PHY-3002 : Step(169): len = 55593.3, overlap = 21
PHY-3002 : Step(170): len = 55609.8, overlap = 21.5
PHY-3001 : Before Legalized: Len = 55609.8
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005122s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 60202.5, Over = 0
PHY-3001 : Legalized: Len = 60202.5, Over = 0
PHY-3001 : Spreading special nets. 8 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005494s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 11 instances has been re-located, deltaX = 5, deltaY = 6, maxDist = 1.
PHY-3001 : Final: Len = 60408.5, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6308, tnet num: 1934, tinst num: 798, tnode num: 8605, tedge num: 11134.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 41/1936.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 66096, over cnt = 128(0%), over = 198, worst = 6
PHY-1002 : len = 67144, over cnt = 61(0%), over = 72, worst = 4
PHY-1002 : len = 67944, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 67976, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 68120, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.138628s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (112.7%)

PHY-1001 : Congestion index: top1 = 31.08, top5 = 22.59, top10 = 17.35, top15 = 13.55.
PHY-1001 : End incremental global routing;  0.192526s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (105.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060967s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.282722s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (105.0%)

OPT-1001 : Current memory(MB): used = 212, reserve = 180, peak = 214.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1714/1936.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68120, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006148s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.08, top5 = 22.59, top10 = 17.35, top15 = 13.55.
OPT-1001 : End congestion update;  0.054466s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (114.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049733s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.3%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 758 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 798 instances, 749 slices, 20 macros(196 instances: 126 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 60455.2, Over = 0
PHY-3001 : End spreading;  0.004841s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 60455.2, Over = 0
PHY-3001 : End incremental legalization;  0.033487s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (93.3%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 2 cells processed and 150 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.151290s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (103.3%)

OPT-1001 : Current memory(MB): used = 217, reserve = 185, peak = 217.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050685s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.5%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1706/1936.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68144, over cnt = 1(0%), over = 2, worst = 2
PHY-1002 : len = 68144, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.018128s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (86.2%)

PHY-1001 : Congestion index: top1 = 31.08, top5 = 22.54, top10 = 17.35, top15 = 13.55.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.055475s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (112.7%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.655172
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.873031s wall, 0.875000s user + 0.015625s system = 0.890625s CPU (102.0%)

RUN-1003 : finish command "place" in  5.481239s wall, 7.734375s user + 3.734375s system = 11.468750s CPU (209.2%)

RUN-1004 : used memory is 201 MB, reserved memory is 168 MB, peak memory is 217 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 800 instances
RUN-1001 : 374 mslices, 375 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1936 nets
RUN-1001 : 1367 nets have 2 pins
RUN-1001 : 463 nets have [3 - 5] pins
RUN-1001 : 65 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6308, tnet num: 1934, tinst num: 798, tnode num: 8605, tedge num: 11134.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 374 mslices, 375 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 65264, over cnt = 131(0%), over = 203, worst = 6
PHY-1002 : len = 66368, over cnt = 65(0%), over = 78, worst = 4
PHY-1002 : len = 67344, over cnt = 4(0%), over = 5, worst = 2
PHY-1002 : len = 67448, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.121867s wall, 0.203125s user + 0.078125s system = 0.281250s CPU (230.8%)

PHY-1001 : Congestion index: top1 = 31.03, top5 = 22.45, top10 = 17.29, top15 = 13.47.
PHY-1001 : End global routing;  0.170876s wall, 0.250000s user + 0.078125s system = 0.328125s CPU (192.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 234, reserve = 202, peak = 234.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 492, reserve = 464, peak = 492.
PHY-1001 : End build detailed router design. 3.096309s wall, 3.046875s user + 0.031250s system = 3.078125s CPU (99.4%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 31864, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.106679s wall, 1.078125s user + 0.015625s system = 1.093750s CPU (98.8%)

PHY-1001 : Current memory(MB): used = 524, reserve = 497, peak = 524.
PHY-1001 : End phase 1; 1.112966s wall, 1.078125s user + 0.015625s system = 1.093750s CPU (98.3%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 29% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 178496, over cnt = 31(0%), over = 31, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 525, reserve = 497, peak = 526.
PHY-1001 : End initial routed; 1.310249s wall, 2.421875s user + 0.078125s system = 2.500000s CPU (190.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1721(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.613   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -47.164  |  23   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.332745s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (98.6%)

PHY-1001 : Current memory(MB): used = 528, reserve = 499, peak = 528.
PHY-1001 : End phase 2; 1.643088s wall, 2.750000s user + 0.078125s system = 2.828125s CPU (172.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 178496, over cnt = 31(0%), over = 31, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015313s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (102.0%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 178416, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.032141s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (145.8%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 178432, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.022308s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (140.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1721(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.613   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -47.164  |  23   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.344266s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (99.9%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 6 feed throughs used by 5 nets
PHY-1001 : End commit to database; 0.168357s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (102.1%)

PHY-1001 : Current memory(MB): used = 542, reserve = 514, peak = 542.
PHY-1001 : End phase 3; 0.709814s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (103.5%)

PHY-1003 : Routed, final wirelength = 178432
PHY-1001 : Current memory(MB): used = 543, reserve = 514, peak = 543.
PHY-1001 : End export database. 0.010584s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  6.744767s wall, 7.796875s user + 0.125000s system = 7.921875s CPU (117.5%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6308, tnet num: 1934, tinst num: 798, tnode num: 8605, tedge num: 11134.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : Too much hold violation, please check the src code.
RUN-1003 : finish command "route" in  7.573846s wall, 8.656250s user + 0.218750s system = 8.875000s CPU (117.2%)

RUN-1004 : used memory is 497 MB, reserved memory is 468 MB, peak memory is 543 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      767   out of  19600    3.91%
#reg                     1053   out of  19600    5.37%
#le                      1445
  #lut only               392   out of   1445   27.13%
  #reg only               678   out of   1445   46.92%
  #lut&reg                375   out of   1445   25.95%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                                      Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0                       483
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3                       102
#3        wendu/clk_us                    GCLK               lslice             signal_process/trans/clk_out_n_syn_40.q0    38
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4                       1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di                             1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1445   |571     |196     |1086    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1059   |270     |135     |863     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |36     |29      |7       |22      |0       |0       |
|    demodu                  |Demodulation                                     |430    |79      |45      |352     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |55     |30      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |5       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |14     |9       |0       |14      |0       |0       |
|    integ                   |Integration                                      |142    |23      |15      |114     |0       |0       |
|    modu                    |Modulation                                       |99     |45      |14      |97      |0       |1       |
|    rs422                   |Rs422Output                                      |325    |75      |46      |257     |0       |4       |
|    trans                   |SquareWaveGenerator                              |27     |19      |8       |21      |0       |0       |
|  u_uart                    |UART_Control                                     |158    |127     |7       |115     |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |27      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |22     |18      |0       |13      |0       |0       |
|    U2                      |Ctrl_Data                                        |100    |82      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |210    |165     |45      |74      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1328  
    #2          2       334   
    #3          3       102   
    #4          4        27   
    #5        5-10       69   
    #6        11-50      31   
    #7       101-500     1    
  Average     1.96            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6308, tnet num: 1934, tinst num: 798, tnode num: 8605, tedge num: 11134.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1934 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 798
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1936, pip num: 14353
BIT-1002 : Init feedthrough completely, num: 6
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1269 valid insts, and 37646 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.236408s wall, 17.156250s user + 0.078125s system = 17.234375s CPU (532.5%)

RUN-1004 : used memory is 513 MB, reserved memory is 487 MB, peak memory is 660 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250804_145342.log"
