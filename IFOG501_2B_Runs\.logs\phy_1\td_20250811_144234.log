============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Mon Aug 11 14:42:35 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "config_chipwatcher ../../sim/test.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 1 view nodes, 16 trigger nets, 16 data nets.
KIT-1004 : Chipwatcher code = 0001110111111000
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir C:/Anlogic/TD_5.6.5_SP3_151.449/cw/ -file IFOG501_2B_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file C:/Anlogic/TD_5.6.5_SP3_151.449/cw\bus_det.v
HDL-1007 : analyze verilog file C:/Anlogic/TD_5.6.5_SP3_151.449/cw\bus_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD_5.6.5_SP3_151.449/cw\cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD_5.6.5_SP3_151.449/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD_5.6.5_SP3_151.449/cw\cwc_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD_5.6.5_SP3_151.449/cw\detect_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD_5.6.5_SP3_151.449/cw\detect_non_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD_5.6.5_SP3_151.449/cw\emb_ctrl.v
HDL-1007 : analyze verilog file C:/Anlogic/TD_5.6.5_SP3_151.449/cw\register.v
HDL-1007 : analyze verilog file C:/Anlogic/TD_5.6.5_SP3_151.449/cw\tap.v
HDL-1007 : analyze verilog file C:/Anlogic/TD_5.6.5_SP3_151.449/cw\trigger.sv
HDL-1007 : analyze verilog file IFOG501_2B_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in IFOG501_2B_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=1,BUS_DIN_NUM=16,BUS_CTRL_NUM=36,BUS_WIDTH='{32'sb010000},BUS_DIN_POS='{32'sb0},BUS_CTRL_POS='{32'sb0}) in C:/Anlogic/TD_5.6.5_SP3_151.449/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=58) in C:/Anlogic/TD_5.6.5_SP3_151.449/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=58) in C:/Anlogic/TD_5.6.5_SP3_151.449/cw\register.v(21)
HDL-1007 : elaborate module tap in C:/Anlogic/TD_5.6.5_SP3_151.449/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=1,BUS_DIN_NUM=16,BUS_CTRL_NUM=36,BUS_WIDTH='{32'sb010000},BUS_DIN_POS='{32'sb0},BUS_CTRL_POS='{32'sb0}) in C:/Anlogic/TD_5.6.5_SP3_151.449/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=1,BUS_DIN_NUM=16,BUS_CTRL_NUM=36,BUS_WIDTH='{32'sb010000},BUS_DIN_POS='{32'sb0},BUS_CTRL_POS='{32'sb0}) in C:/Anlogic/TD_5.6.5_SP3_151.449/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in C:/Anlogic/TD_5.6.5_SP3_151.449/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in C:/Anlogic/TD_5.6.5_SP3_151.449/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=1,BUS_DIN_NUM=16,BUS_CTRL_NUM=36,BUS_WIDTH='{32'sb010000},BUS_DIN_POS='{32'sb0},BUS_CTRL_POS='{32'sb0})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=58)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=58)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=1,BUS_DIN_NUM=16,BUS_CTRL_NUM=36,BUS_WIDTH='{32'sb010000},BUS_DIN_POS='{32'sb0},BUS_CTRL_POS='{32'sb0})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=1,BUS_DIN_NUM=16,BUS_CTRL_NUM=36,BUS_WIDTH='{32'sb010000},BUS_DIN_POS='{32'sb0},BUS_CTRL_POS='{32'sb0})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 3034/8 useful/useless nets, 1919/2 useful/useless insts
SYN-1016 : Merged 12 instances.
SYN-1032 : 2867/2 useful/useless nets, 1752/2 useful/useless insts
SYN-1032 : 2847/20 useful/useless nets, 2192/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 1 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 1 mux instances.
SYN-1015 : Optimize round 1, 225 better
SYN-1014 : Optimize round 2
SYN-1032 : 2698/15 useful/useless nets, 2043/16 useful/useless insts
SYN-1015 : Optimize round 2, 32 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 37 IOs to PADs
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-2571 : Optimize after map_dsp, round 1, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 16 instances.
SYN-2501 : Optimize round 1, 32 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 12 macro adder
SYN-1019 : Optimized 5 mux instances.
SYN-1016 : Merged 9 instances.
SYN-1032 : 3042/2 useful/useless nets, 2388/1 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1002 : start command "get_pins config_inst.jtck"
RUN-1002 : start command "create_clock -name jtck -period 100 "
RUN-1102 : create_clock: clock name: jtck, type: 0, period: 100000, rise: 0, fall: 50000.
RUN-1002 : start command "get_clocks jtck"
RUN-1002 : start command "set_clock_uncertainty -hold 0.1 "
RUN-1002 : start command "get_clocks jtck"
RUN-1002 : start command "set_clock_groups -exclusive -group "
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 10548, tnet num: 3042, tinst num: 2387, tnode num: 14389, tedge num: 17071.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 3042 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 7 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 13 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 144 (3.74), #lev = 6 (1.85)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 144 (3.74), #lev = 6 (1.85)
SYN-3001 : Logic optimization runtime opt =   0.02 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 326 instances into 144 LUTs, name keeping = 76%.
SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 57 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 239 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 88 adder to BLE ...
SYN-4008 : Packed 88 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  1.156579s wall, 1.078125s user + 0.062500s system = 1.140625s CPU (98.6%)

RUN-1004 : used memory is 168 MB, reserved memory is 131 MB, peak memory is 199 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (135 clock/control pins, 0 other pins).
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 2015 instances
RUN-0007 : 515 luts, 1175 seqs, 166 mslices, 105 lslices, 37 pads, 6 brams, 5 dsps
RUN-1001 : There are total 2669 nets
RUN-1001 : 1863 nets have 2 pins
RUN-1001 : 658 nets have [3 - 5] pins
RUN-1001 : 92 nets have [6 - 10] pins
RUN-1001 : 25 nets have [11 - 20] pins
RUN-1001 : 24 nets have [21 - 99] pins
RUN-1001 : 7 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     250     
RUN-1001 :   No   |  No   |  Yes  |     215     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     260     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  18   |     9      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 28
PHY-3001 : Initial placement ...
PHY-3001 : design contains 2013 instances, 515 luts, 1175 seqs, 271 slices, 32 macros(271 instances: 166 mslices 105 lslices)
PHY-0007 : Cell area utilization is 6%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 9767, tnet num: 2667, tinst num: 2013, tnode num: 13703, tedge num: 16566.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2667 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 7 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 13 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.295076s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (100.6%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 699174
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 2013.
PHY-3001 : End clustering;  0.000028s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 625254, overlap = 24.75
PHY-3002 : Step(2): len = 587446, overlap = 15.75
PHY-3002 : Step(3): len = 565461, overlap = 24.75
PHY-3002 : Step(4): len = 541876, overlap = 13.5
PHY-3002 : Step(5): len = 529506, overlap = 20.25
PHY-3002 : Step(6): len = 450158, overlap = 13.5
PHY-3002 : Step(7): len = 425317, overlap = 11.25
PHY-3002 : Step(8): len = 417339, overlap = 13.5
PHY-3002 : Step(9): len = 402221, overlap = 11.25
PHY-3002 : Step(10): len = 391794, overlap = 18
PHY-3002 : Step(11): len = 379405, overlap = 13.5
PHY-3002 : Step(12): len = 370970, overlap = 18
PHY-3002 : Step(13): len = 360739, overlap = 18
PHY-3002 : Step(14): len = 351951, overlap = 18
PHY-3002 : Step(15): len = 343854, overlap = 15.75
PHY-3002 : Step(16): len = 336594, overlap = 18
PHY-3002 : Step(17): len = 328357, overlap = 15.75
PHY-3002 : Step(18): len = 320637, overlap = 18
PHY-3002 : Step(19): len = 315269, overlap = 15.75
PHY-3002 : Step(20): len = 303662, overlap = 18
PHY-3002 : Step(21): len = 296330, overlap = 18
PHY-3002 : Step(22): len = 292466, overlap = 18
PHY-3002 : Step(23): len = 280202, overlap = 15.75
PHY-3002 : Step(24): len = 263229, overlap = 24.75
PHY-3002 : Step(25): len = 259830, overlap = 24.75
PHY-3002 : Step(26): len = 253272, overlap = 24.75
PHY-3002 : Step(27): len = 231569, overlap = 22.5
PHY-3002 : Step(28): len = 223163, overlap = 24.75
PHY-3002 : Step(29): len = 220982, overlap = 24.75
PHY-3002 : Step(30): len = 209467, overlap = 24.75
PHY-3002 : Step(31): len = 183484, overlap = 24.75
PHY-3002 : Step(32): len = 180028, overlap = 24.75
PHY-3002 : Step(33): len = 176044, overlap = 24.75
PHY-3002 : Step(34): len = 169437, overlap = 24.75
PHY-3002 : Step(35): len = 167672, overlap = 24.75
PHY-3002 : Step(36): len = 156839, overlap = 22.5
PHY-3002 : Step(37): len = 152630, overlap = 24.75
PHY-3002 : Step(38): len = 150544, overlap = 24.75
PHY-3002 : Step(39): len = 147678, overlap = 24.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000235385
PHY-3002 : Step(40): len = 152023, overlap = 22.5
PHY-3002 : Step(41): len = 151603, overlap = 18
PHY-3002 : Step(42): len = 151243, overlap = 18
PHY-3002 : Step(43): len = 150624, overlap = 18
PHY-3002 : Step(44): len = 149331, overlap = 15.75
PHY-3002 : Step(45): len = 148997, overlap = 15.75
PHY-3002 : Step(46): len = 149176, overlap = 18
PHY-3002 : Step(47): len = 147261, overlap = 15.75
PHY-3002 : Step(48): len = 145264, overlap = 18
PHY-3002 : Step(49): len = 144746, overlap = 18
PHY-3002 : Step(50): len = 142336, overlap = 18
PHY-3002 : Step(51): len = 140119, overlap = 15.75
PHY-3002 : Step(52): len = 135197, overlap = 15.75
PHY-3002 : Step(53): len = 134468, overlap = 13.5
PHY-3002 : Step(54): len = 133173, overlap = 15.75
PHY-3002 : Step(55): len = 130811, overlap = 15.75
PHY-3002 : Step(56): len = 130731, overlap = 6.75
PHY-3002 : Step(57): len = 129462, overlap = 11.25
PHY-3002 : Step(58): len = 128875, overlap = 9
PHY-3002 : Step(59): len = 127341, overlap = 9
PHY-3002 : Step(60): len = 125056, overlap = 9
PHY-3002 : Step(61): len = 122747, overlap = 4.5
PHY-3002 : Step(62): len = 120637, overlap = 6.75
PHY-3002 : Step(63): len = 118795, overlap = 6.75
PHY-3002 : Step(64): len = 118619, overlap = 6.75
PHY-3002 : Step(65): len = 114458, overlap = 11.25
PHY-3002 : Step(66): len = 112216, overlap = 6.75
PHY-3002 : Step(67): len = 110656, overlap = 4.5
PHY-3002 : Step(68): len = 110301, overlap = 6.75
PHY-3002 : Step(69): len = 109017, overlap = 4.5
PHY-3002 : Step(70): len = 107955, overlap = 4.5
PHY-3002 : Step(71): len = 106966, overlap = 4.5
PHY-3002 : Step(72): len = 105244, overlap = 6.75
PHY-3002 : Step(73): len = 100951, overlap = 4.5
PHY-3002 : Step(74): len = 100150, overlap = 6.75
PHY-3002 : Step(75): len = 99122.4, overlap = 6.75
PHY-3002 : Step(76): len = 98196.5, overlap = 6.75
PHY-3002 : Step(77): len = 97394.4, overlap = 4.5
PHY-3002 : Step(78): len = 96097.4, overlap = 4.5
PHY-3002 : Step(79): len = 95737.4, overlap = 6.75
PHY-3002 : Step(80): len = 94348.7, overlap = 6.75
PHY-3002 : Step(81): len = 92721.1, overlap = 6.75
PHY-3002 : Step(82): len = 89873, overlap = 6.75
PHY-3002 : Step(83): len = 89440.1, overlap = 6.75
PHY-3002 : Step(84): len = 88413.9, overlap = 6.75
PHY-3002 : Step(85): len = 87101.4, overlap = 4.5
PHY-3002 : Step(86): len = 86749.8, overlap = 6.75
PHY-3002 : Step(87): len = 85885.3, overlap = 6.75
PHY-3002 : Step(88): len = 84779, overlap = 6.75
PHY-3002 : Step(89): len = 84084.6, overlap = 6.75
PHY-3002 : Step(90): len = 82531.1, overlap = 4.5
PHY-3002 : Step(91): len = 82480.8, overlap = 4.5
PHY-3002 : Step(92): len = 80864.9, overlap = 9
PHY-3002 : Step(93): len = 79107.6, overlap = 6.75
PHY-3002 : Step(94): len = 79149.5, overlap = 6.75
PHY-3002 : Step(95): len = 78928.6, overlap = 6.75
PHY-3002 : Step(96): len = 78642.6, overlap = 6.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00047077
PHY-3002 : Step(97): len = 78859.3, overlap = 6.75
PHY-3002 : Step(98): len = 78784.4, overlap = 6.75
PHY-3002 : Step(99): len = 78433.9, overlap = 6.9375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00094154
PHY-3002 : Step(100): len = 78542.1, overlap = 6.9375
PHY-3002 : Step(101): len = 78470.1, overlap = 7.125
PHY-3001 : Before Legalized: Len = 78470.1
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008121s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (384.8%)

PHY-3001 : After Legalized: Len = 79735.3, Over = 0.375
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 8%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2667 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.082957s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (94.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(102): len = 80570.7, overlap = 9.375
PHY-3002 : Step(103): len = 78042.9, overlap = 12.2188
PHY-3002 : Step(104): len = 75949.1, overlap = 12.9688
PHY-3002 : Step(105): len = 74546.1, overlap = 12.875
PHY-3002 : Step(106): len = 73289.7, overlap = 20.875
PHY-3002 : Step(107): len = 71902, overlap = 21.8125
PHY-3002 : Step(108): len = 70859.1, overlap = 22.25
PHY-3002 : Step(109): len = 69575.8, overlap = 24.125
PHY-3002 : Step(110): len = 68668.8, overlap = 25.2812
PHY-3002 : Step(111): len = 67202.3, overlap = 27.3125
PHY-3002 : Step(112): len = 66656.3, overlap = 29.25
PHY-3002 : Step(113): len = 65624.5, overlap = 31.7812
PHY-3002 : Step(114): len = 64771.6, overlap = 36.0312
PHY-3002 : Step(115): len = 63508.6, overlap = 36.4688
PHY-3002 : Step(116): len = 62834, overlap = 36.5938
PHY-3002 : Step(117): len = 61446.3, overlap = 38.375
PHY-3002 : Step(118): len = 61096.6, overlap = 38.8438
PHY-3002 : Step(119): len = 60438.7, overlap = 38.5
PHY-3002 : Step(120): len = 59967.8, overlap = 39.1562
PHY-3002 : Step(121): len = 58963.9, overlap = 39.1875
PHY-3002 : Step(122): len = 57530.2, overlap = 39.1875
PHY-3002 : Step(123): len = 56377, overlap = 39.3125
PHY-3002 : Step(124): len = 55901, overlap = 39.5938
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000282808
PHY-3002 : Step(125): len = 56026.4, overlap = 39.2812
PHY-3002 : Step(126): len = 55825.2, overlap = 37.4375
PHY-3002 : Step(127): len = 55944.5, overlap = 36.9375
PHY-3002 : Step(128): len = 56191.9, overlap = 35.8438
PHY-3002 : Step(129): len = 56347.1, overlap = 34.7188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000565615
PHY-3002 : Step(130): len = 56159.9, overlap = 34.3438
PHY-3002 : Step(131): len = 55664.7, overlap = 34.0625
PHY-3002 : Step(132): len = 55664.7, overlap = 34.0625
PHY-3002 : Step(133): len = 55491.9, overlap = 34
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 8%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2667 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.081292s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (96.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.72058e-05
PHY-3002 : Step(134): len = 56162.7, overlap = 73.375
PHY-3002 : Step(135): len = 56984.3, overlap = 72.0312
PHY-3002 : Step(136): len = 57413, overlap = 69.3438
PHY-3002 : Step(137): len = 57343.9, overlap = 68.6562
PHY-3002 : Step(138): len = 57624.6, overlap = 68.125
PHY-3002 : Step(139): len = 57662.3, overlap = 55.0625
PHY-3002 : Step(140): len = 57791.3, overlap = 51.9688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000114412
PHY-3002 : Step(141): len = 57943.9, overlap = 51.625
PHY-3002 : Step(142): len = 57982.6, overlap = 49.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000228823
PHY-3002 : Step(143): len = 58603.8, overlap = 49
PHY-3002 : Step(144): len = 59791.1, overlap = 43.8125
PHY-3002 : Step(145): len = 60632.6, overlap = 40.9062
PHY-3002 : Step(146): len = 60856.7, overlap = 35.5938
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 9767, tnet num: 2667, tinst num: 2013, tnode num: 13703, tedge num: 16566.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 108.09 peak overflow 2.78
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2669.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 64320, over cnt = 267(0%), over = 1147, worst = 25
PHY-1001 : End global iterations;  0.088793s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (158.4%)

PHY-1001 : Congestion index: top1 = 45.88, top5 = 27.05, top10 = 18.13, top15 = 13.26.
PHY-1001 : End incremental global routing;  0.140353s wall, 0.156250s user + 0.031250s system = 0.187500s CPU (133.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2667 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 7 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 13 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.094603s wall, 0.078125s user + 0.015625s system = 0.093750s CPU (99.1%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1971 has valid locations, 4 needs to be replaced
PHY-3001 : design contains 2016 instances, 515 luts, 1178 seqs, 271 slices, 32 macros(271 instances: 166 mslices 105 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 61133.6
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 8%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 9776, tnet num: 2670, tinst num: 2016, tnode num: 13718, tedge num: 16578.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2670 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 7 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 13 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.313772s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (99.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(147): len = 61121.9, overlap = 0
PHY-3002 : Step(148): len = 61159.6, overlap = 0
PHY-3002 : Step(149): len = 61193.4, overlap = 0
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 8%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2670 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.081009s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (96.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(150): len = 61189.8, overlap = 35.5938
PHY-3002 : Step(151): len = 61189.8, overlap = 35.5938
PHY-3001 : Final: Len = 61189.8, Over = 35.5938
PHY-3001 : End incremental placement;  0.474878s wall, 0.546875s user + 0.062500s system = 0.609375s CPU (128.3%)

OPT-1001 : Total overflow 108.09 peak overflow 2.78
OPT-1001 : End high-fanout net optimization;  0.755073s wall, 0.812500s user + 0.109375s system = 0.921875s CPU (122.1%)

OPT-1001 : Current memory(MB): used = 240, reserve = 201, peak = 240.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2017/2672.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 64544, over cnt = 269(0%), over = 1141, worst = 25
PHY-1002 : len = 72256, over cnt = 209(0%), over = 549, worst = 15
PHY-1002 : len = 77520, over cnt = 70(0%), over = 164, worst = 13
PHY-1002 : len = 80232, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 80752, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.120108s wall, 0.125000s user + 0.046875s system = 0.171875s CPU (143.1%)

PHY-1001 : Congestion index: top1 = 40.17, top5 = 27.13, top10 = 20.11, top15 = 15.44.
OPT-1001 : End congestion update;  0.165670s wall, 0.171875s user + 0.046875s system = 0.218750s CPU (132.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2670 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.077324s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (101.0%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.246941s wall, 0.250000s user + 0.046875s system = 0.296875s CPU (120.2%)

OPT-1001 : Current memory(MB): used = 240, reserve = 201, peak = 240.
OPT-1001 : End physical optimization;  1.270870s wall, 1.390625s user + 0.156250s system = 1.546875s CPU (121.7%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 515 LUT to BLE ...
SYN-4008 : Packed 515 LUT and 245 SEQ to BLE.
SYN-4003 : Packing 933 remaining SEQ's ...
SYN-4005 : Packed 228 SEQ with LUT/SLICE
SYN-4006 : 71 single LUT's are left
SYN-4006 : 705 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1220/1609 primitive instances ...
PHY-3001 : End packing;  0.079849s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (97.8%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 973 instances
RUN-1001 : 460 mslices, 459 lslices, 37 pads, 6 brams, 5 dsps
RUN-1001 : There are total 2436 nets
RUN-1001 : 1630 nets have 2 pins
RUN-1001 : 657 nets have [3 - 5] pins
RUN-1001 : 93 nets have [6 - 10] pins
RUN-1001 : 25 nets have [11 - 20] pins
RUN-1001 : 26 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 971 instances, 919 slices, 32 macros(271 instances: 166 mslices 105 lslices)
PHY-3001 : Cell area utilization is 12%
PHY-3001 : After packing: Len = 61693.4, Over = 61.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 12%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 8192, tnet num: 2434, tinst num: 971, tnode num: 11003, tedge num: 14441.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2434 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 7 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 13 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.330558s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (99.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.34979e-05
PHY-3002 : Step(152): len = 61071, overlap = 62
PHY-3002 : Step(153): len = 60401.7, overlap = 61.25
PHY-3002 : Step(154): len = 60174.4, overlap = 63.25
PHY-3002 : Step(155): len = 60205.6, overlap = 65
PHY-3002 : Step(156): len = 59664.6, overlap = 65
PHY-3002 : Step(157): len = 59152.8, overlap = 66
PHY-3002 : Step(158): len = 58780.8, overlap = 64.5
PHY-3002 : Step(159): len = 58545.6, overlap = 64.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.69959e-05
PHY-3002 : Step(160): len = 59093.4, overlap = 65.5
PHY-3002 : Step(161): len = 59542.2, overlap = 65.25
PHY-3002 : Step(162): len = 59674.3, overlap = 64
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000133992
PHY-3002 : Step(163): len = 60444, overlap = 61.75
PHY-3002 : Step(164): len = 60928.3, overlap = 56
PHY-3001 : Before Legalized: Len = 60928.3
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.105854s wall, 0.078125s user + 0.125000s system = 0.203125s CPU (191.9%)

PHY-3001 : After Legalized: Len = 80759.9, Over = 0
PHY-3001 : Trial Legalized: Len = 80759.9
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 12%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2434 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.064584s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (96.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00102686
PHY-3002 : Step(165): len = 76570.6, overlap = 7.5
PHY-3002 : Step(166): len = 73469.4, overlap = 15.25
PHY-3002 : Step(167): len = 71670.6, overlap = 16.5
PHY-3002 : Step(168): len = 70150.1, overlap = 19.5
PHY-3002 : Step(169): len = 69096.3, overlap = 22.75
PHY-3002 : Step(170): len = 68594.7, overlap = 25
PHY-3002 : Step(171): len = 68182.4, overlap = 28
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00205372
PHY-3002 : Step(172): len = 68495.1, overlap = 25.75
PHY-3002 : Step(173): len = 68603.4, overlap = 27.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00410743
PHY-3002 : Step(174): len = 68739.2, overlap = 26.75
PHY-3002 : Step(175): len = 68769.1, overlap = 26.25
PHY-3001 : Before Legalized: Len = 68769.1
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005361s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 75005, Over = 0
PHY-3001 : Legalized: Len = 75005, Over = 0
PHY-3001 : Spreading special nets. 21 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.007008s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 32 instances has been re-located, deltaX = 9, deltaY = 22, maxDist = 3.
PHY-3001 : Final: Len = 75825, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 8192, tnet num: 2434, tinst num: 971, tnode num: 11003, tedge num: 14441.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 120/2436.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 84272, over cnt = 215(0%), over = 398, worst = 9
PHY-1002 : len = 85952, over cnt = 137(0%), over = 199, worst = 4
PHY-1002 : len = 87144, over cnt = 72(0%), over = 107, worst = 4
PHY-1002 : len = 87672, over cnt = 42(0%), over = 67, worst = 4
PHY-1002 : len = 88640, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.160841s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (155.4%)

PHY-1001 : Congestion index: top1 = 33.49, top5 = 25.31, top10 = 20.36, top15 = 16.48.
PHY-1001 : End incremental global routing;  0.214980s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (145.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2434 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 7 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 13 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.079038s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (98.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.332819s wall, 0.406250s user + 0.015625s system = 0.421875s CPU (126.8%)

OPT-1001 : Current memory(MB): used = 237, reserve = 198, peak = 240.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2154/2436.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 88640, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007451s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (209.7%)

PHY-1001 : Congestion index: top1 = 33.49, top5 = 25.31, top10 = 20.36, top15 = 16.48.
OPT-1001 : End congestion update;  0.056901s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (109.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2434 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.065897s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (94.8%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 930 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 971 instances, 919 slices, 32 macros(271 instances: 166 mslices 105 lslices)
PHY-3001 : Cell area utilization is 12%
PHY-3001 : Initial: Len = 75891.4, Over = 0
PHY-3001 : End spreading;  0.005907s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (264.5%)

PHY-3001 : Final: Len = 75891.4, Over = 0
PHY-3001 : End incremental legalization;  0.041815s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (112.1%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 2 cells processed and 250 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.181286s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (103.4%)

OPT-1001 : Current memory(MB): used = 242, reserve = 203, peak = 242.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2434 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.064981s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (96.2%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2145/2436.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 88744, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.009098s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 33.56, top5 = 25.34, top10 = 20.38, top15 = 16.50.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2434 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.065038s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (96.1%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 33.137931
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.993854s wall, 1.062500s user + 0.031250s system = 1.093750s CPU (110.1%)

RUN-1003 : finish command "place" in  6.213947s wall, 9.171875s user + 3.031250s system = 12.203125s CPU (196.4%)

RUN-1004 : used memory is 231 MB, reserved memory is 193 MB, peak memory is 242 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 973 instances
RUN-1001 : 460 mslices, 459 lslices, 37 pads, 6 brams, 5 dsps
RUN-1001 : There are total 2436 nets
RUN-1001 : 1630 nets have 2 pins
RUN-1001 : 657 nets have [3 - 5] pins
RUN-1001 : 93 nets have [6 - 10] pins
RUN-1001 : 25 nets have [11 - 20] pins
RUN-1001 : 26 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 8192, tnet num: 2434, tinst num: 971, tnode num: 11003, tedge num: 14441.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 460 mslices, 459 lslices, 37 pads, 6 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2434 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 7 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 13 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 83048, over cnt = 212(0%), over = 392, worst = 9
PHY-1002 : len = 85056, over cnt = 126(0%), over = 179, worst = 4
PHY-1002 : len = 86560, over cnt = 45(0%), over = 67, worst = 4
PHY-1002 : len = 87640, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.154034s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (152.2%)

PHY-1001 : Congestion index: top1 = 33.51, top5 = 25.34, top10 = 20.30, top15 = 16.35.
PHY-1001 : End global routing;  0.207209s wall, 0.250000s user + 0.031250s system = 0.281250s CPU (135.7%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 258, reserve = 220, peak = 272.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 519, reserve = 483, peak = 519.
PHY-1001 : End build detailed router design. 3.103089s wall, 3.031250s user + 0.046875s system = 3.078125s CPU (99.2%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33856, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.282370s wall, 1.281250s user + 0.000000s system = 1.281250s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 551, reserve = 516, peak = 551.
PHY-1001 : End phase 1; 1.288358s wall, 1.296875s user + 0.000000s system = 1.296875s CPU (100.7%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 30% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 50% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 210400, over cnt = 57(0%), over = 57, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 552, reserve = 517, peak = 552.
PHY-1001 : End initial routed; 1.442610s wall, 2.390625s user + 0.109375s system = 2.500000s CPU (173.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/2158(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.613   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.003   |  -52.634  |  23   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.397316s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (98.3%)

PHY-1001 : Current memory(MB): used = 554, reserve = 518, peak = 554.
PHY-1001 : End phase 2; 1.840024s wall, 2.781250s user + 0.109375s system = 2.890625s CPU (157.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 210400, over cnt = 57(0%), over = 57, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.017454s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (89.5%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 210120, over cnt = 8(0%), over = 8, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.046938s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (99.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 210200, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.024986s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (62.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/2158(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.613   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.003   |  -52.634  |  23   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.392125s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (103.6%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 6 feed throughs used by 6 nets
PHY-1001 : End commit to database; 0.209528s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (96.9%)

PHY-1001 : Current memory(MB): used = 570, reserve = 535, peak = 570.
PHY-1001 : End phase 3; 0.820809s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (100.9%)

PHY-1003 : Routed, final wirelength = 210200
PHY-1001 : Current memory(MB): used = 570, reserve = 535, peak = 570.
PHY-1001 : End export database. 0.011032s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (141.6%)

PHY-1001 : End detail routing;  7.234124s wall, 8.125000s user + 0.156250s system = 8.281250s CPU (114.5%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 8192, tnet num: 2434, tinst num: 971, tnode num: 11003, tedge num: 14441.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : Too much hold violation, please check the src code.
RUN-1003 : finish command "route" in  8.195724s wall, 9.125000s user + 0.203125s system = 9.328125s CPU (113.8%)

RUN-1004 : used memory is 546 MB, reserved memory is 515 MB, peak memory is 570 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                     1064   out of  19600    5.43%
#reg                     1262   out of  19600    6.44%
#le                      1769
  #lut only               507   out of   1769   28.66%
  #reg only               705   out of   1769   39.85%
  #lut&reg                557   out of   1769   31.49%
#dsp                        5   out of     29   17.24%
#bram                       6   out of     64    9.38%
  #bram9k                   6
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         519
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         101
#3        config_inst_syn_9               GCLK               config             config_inst.jtck              81
#4        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    38
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-------------------------------------------------------------------------------------------------------------------------------------------+
|Instance                            |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-------------------------------------------------------------------------------------------------------------------------------------------+
|top                                 |IFOG501_2B                                       |1769   |793     |271     |1295    |6       |5       |
|  CLK120                            |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process                    |SignalProcessing                                 |1005   |280     |128     |824     |4       |5       |
|    ctrl_signal                     |SignalGenerator                                  |35     |31      |4       |23      |0       |0       |
|    demodu                          |Demodulation                                     |429    |89      |41      |355     |4       |0       |
|      fifo                          |Asys_fifo56X16                                   |57     |30      |6       |46      |4       |0       |
|        ram_inst                    |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst         |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |14     |6       |0       |14      |0       |0       |
|        wr_to_rd_cross_inst         |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |6       |0       |15      |0       |0       |
|    integ                           |Integration                                      |137    |20      |15      |109     |0       |0       |
|    modu                            |Modulation                                       |69     |36      |14      |65      |0       |1       |
|    rs422                           |Rs422Output                                      |311    |88      |46      |252     |0       |4       |
|    trans                           |SquareWaveGenerator                              |24     |16      |8       |20      |0       |0       |
|  u_uart                            |UART_Control                                     |152    |131     |7       |119     |0       |0       |
|    U0                              |speed_select_Tx                                  |35     |28      |7       |16      |0       |0       |
|    U1                              |uart_tx                                          |16     |16      |0       |16      |0       |0       |
|    U2                              |Ctrl_Data                                        |101    |87      |0       |87      |0       |0       |
|  wendu                             |DS18B20                                          |187    |142     |45      |76      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER                                   |382    |225     |79      |225     |0       |0       |
|    wrapper_cwc_top                 |cwc_top                                          |382    |225     |79      |225     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int                                      |138    |71      |0       |134     |0       |0       |
|        reg_inst                    |register                                         |133    |69      |0       |129     |0       |0       |
|        tap_inst                    |tap                                              |5      |2       |0       |5       |0       |0       |
|      trigger_inst                  |trigger                                          |244    |154     |79      |91      |0       |0       |
|        bus_inst                    |bus_top                                          |53     |35      |18      |17      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det                                          |53     |35      |18      |17      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl                                         |108    |79      |29      |53      |0       |0       |
+-------------------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1591  
    #2          2       431   
    #3          3       198   
    #4          4        28   
    #5        5-10       97   
    #6        11-50      45   
    #7       101-500     1    
  Average     2.08            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 8192, tnet num: 2434, tinst num: 971, tnode num: 11003, tedge num: 14441.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2434 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 7 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 13 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 6. Number of clock nets = 6 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 971
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2436, pip num: 18100
BIT-1002 : Init feedthrough completely, num: 6
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1445 valid insts, and 48457 bits set as '1'.
BIT-1004 : the usercode register value: 00000000010000010001110111111000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.429700s wall, 20.390625s user + 0.046875s system = 20.437500s CPU (595.9%)

RUN-1004 : used memory is 572 MB, reserved memory is 539 MB, peak memory is 692 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250811_144234.log"
