============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Tue Aug  5 10:49:39 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj -phy"
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.151449.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.151449 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -duty_cycle 50.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -duty_cycle 50.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6644314406912"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1318554959872"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -nowarn -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    new     |       new        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1573 instances
RUN-0007 : 367 luts, 952 seqs, 133 mslices, 70 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2117 nets
RUN-1001 : 1544 nets have 2 pins
RUN-1001 : 471 nets have [3 - 5] pins
RUN-1001 : 60 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     251     
RUN-1001 :   No   |  No   |  Yes  |     122     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     337     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     112     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |  15   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1571 instances, 367 luts, 952 seqs, 203 slices, 21 macros(203 instances: 133 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
USR-1006 : Generating FPGA-IP Autogen constraints ...
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7540, tnet num: 2115, tinst num: 1571, tnode num: 10702, tedge num: 12814.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2115 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.262052s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (101.4%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 523763
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1571.
PHY-3001 : End clustering;  0.000020s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 446878, overlap = 20.25
PHY-3002 : Step(2): len = 428050, overlap = 18
PHY-3002 : Step(3): len = 414849, overlap = 18
PHY-3002 : Step(4): len = 405334, overlap = 13.5
PHY-3002 : Step(5): len = 392925, overlap = 11.25
PHY-3002 : Step(6): len = 382362, overlap = 13.5
PHY-3002 : Step(7): len = 374274, overlap = 13.5
PHY-3002 : Step(8): len = 365562, overlap = 13.5
PHY-3002 : Step(9): len = 357863, overlap = 15.75
PHY-3002 : Step(10): len = 350625, overlap = 15.75
PHY-3002 : Step(11): len = 342379, overlap = 13.5
PHY-3002 : Step(12): len = 334348, overlap = 13.5
PHY-3002 : Step(13): len = 328811, overlap = 13.5
PHY-3002 : Step(14): len = 321204, overlap = 13.5
PHY-3002 : Step(15): len = 312620, overlap = 15.75
PHY-3002 : Step(16): len = 307361, overlap = 15.75
PHY-3002 : Step(17): len = 301143, overlap = 15.75
PHY-3002 : Step(18): len = 294758, overlap = 13.5
PHY-3002 : Step(19): len = 289909, overlap = 13.5
PHY-3002 : Step(20): len = 282928, overlap = 13.5
PHY-3002 : Step(21): len = 274804, overlap = 13.5
PHY-3002 : Step(22): len = 269733, overlap = 13.5
PHY-3002 : Step(23): len = 265025, overlap = 13.5
PHY-3002 : Step(24): len = 258379, overlap = 13.5
PHY-3002 : Step(25): len = 251518, overlap = 20.25
PHY-3002 : Step(26): len = 247290, overlap = 20.25
PHY-3002 : Step(27): len = 242857, overlap = 20.25
PHY-3002 : Step(28): len = 236182, overlap = 20.25
PHY-3002 : Step(29): len = 230213, overlap = 20.25
PHY-3002 : Step(30): len = 225594, overlap = 20.25
PHY-3002 : Step(31): len = 220792, overlap = 20.25
PHY-3002 : Step(32): len = 215192, overlap = 20.25
PHY-3002 : Step(33): len = 210862, overlap = 20.25
PHY-3002 : Step(34): len = 206483, overlap = 20.25
PHY-3002 : Step(35): len = 200506, overlap = 20.25
PHY-3002 : Step(36): len = 193683, overlap = 20.25
PHY-3002 : Step(37): len = 190920, overlap = 20.25
PHY-3002 : Step(38): len = 185908, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000118816
PHY-3002 : Step(39): len = 187890, overlap = 18
PHY-3002 : Step(40): len = 185893, overlap = 15.75
PHY-3002 : Step(41): len = 184431, overlap = 18
PHY-3002 : Step(42): len = 181535, overlap = 9
PHY-3002 : Step(43): len = 170766, overlap = 6.75
PHY-3002 : Step(44): len = 165329, overlap = 11.25
PHY-3002 : Step(45): len = 162036, overlap = 11.25
PHY-3002 : Step(46): len = 159565, overlap = 11.25
PHY-3002 : Step(47): len = 153865, overlap = 6.75
PHY-3002 : Step(48): len = 148026, overlap = 11.25
PHY-3002 : Step(49): len = 144794, overlap = 6.75
PHY-3002 : Step(50): len = 143259, overlap = 9
PHY-3002 : Step(51): len = 139643, overlap = 6.75
PHY-3002 : Step(52): len = 135813, overlap = 6.75
PHY-3002 : Step(53): len = 134391, overlap = 4.5
PHY-3002 : Step(54): len = 132693, overlap = 4.5
PHY-3002 : Step(55): len = 130424, overlap = 6.75
PHY-3002 : Step(56): len = 126427, overlap = 6.75
PHY-3002 : Step(57): len = 123271, overlap = 6.75
PHY-3002 : Step(58): len = 121930, overlap = 6.75
PHY-3002 : Step(59): len = 119643, overlap = 9
PHY-3002 : Step(60): len = 117013, overlap = 6.75
PHY-3002 : Step(61): len = 115569, overlap = 9
PHY-3002 : Step(62): len = 112655, overlap = 9
PHY-3002 : Step(63): len = 110249, overlap = 2.25
PHY-3002 : Step(64): len = 109067, overlap = 4.5
PHY-3002 : Step(65): len = 107567, overlap = 6.75
PHY-3002 : Step(66): len = 105048, overlap = 9
PHY-3002 : Step(67): len = 103378, overlap = 9
PHY-3002 : Step(68): len = 101154, overlap = 11.25
PHY-3002 : Step(69): len = 98809.9, overlap = 9
PHY-3002 : Step(70): len = 96344.2, overlap = 6.75
PHY-3002 : Step(71): len = 95389.6, overlap = 9
PHY-3002 : Step(72): len = 93447.1, overlap = 6.75
PHY-3002 : Step(73): len = 84761.8, overlap = 9
PHY-3002 : Step(74): len = 82160.3, overlap = 9
PHY-3002 : Step(75): len = 81941.5, overlap = 9
PHY-3002 : Step(76): len = 81552.2, overlap = 6.75
PHY-3002 : Step(77): len = 80771.6, overlap = 6.75
PHY-3002 : Step(78): len = 80106.8, overlap = 9
PHY-3002 : Step(79): len = 79436.7, overlap = 9.3125
PHY-3002 : Step(80): len = 78003.9, overlap = 9.875
PHY-3002 : Step(81): len = 76440.9, overlap = 9.875
PHY-3002 : Step(82): len = 74897.2, overlap = 12.25
PHY-3002 : Step(83): len = 74454.5, overlap = 12.4375
PHY-3002 : Step(84): len = 73275.4, overlap = 8.1875
PHY-3002 : Step(85): len = 71648.1, overlap = 7.9375
PHY-3002 : Step(86): len = 70534.3, overlap = 8.125
PHY-3002 : Step(87): len = 69688.5, overlap = 8.125
PHY-3002 : Step(88): len = 68360.3, overlap = 8.125
PHY-3002 : Step(89): len = 67039.2, overlap = 12.75
PHY-3002 : Step(90): len = 66433.8, overlap = 12.75
PHY-3002 : Step(91): len = 65862.3, overlap = 10.25
PHY-3002 : Step(92): len = 65805.5, overlap = 10.3125
PHY-3002 : Step(93): len = 65503.4, overlap = 10.3125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000237632
PHY-3002 : Step(94): len = 65735.1, overlap = 10.1875
PHY-3002 : Step(95): len = 65764, overlap = 7.9375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000475264
PHY-3002 : Step(96): len = 65925.1, overlap = 7.9375
PHY-3002 : Step(97): len = 65968.9, overlap = 7.9375
PHY-3001 : Before Legalized: Len = 65968.9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.009058s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (517.5%)

PHY-3001 : After Legalized: Len = 68145.5, Over = 1.1875
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2115 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.063839s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (97.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(98): len = 68375.7, overlap = 3.625
PHY-3002 : Step(99): len = 66669.5, overlap = 3.5625
PHY-3002 : Step(100): len = 65938.8, overlap = 4.25
PHY-3002 : Step(101): len = 63966.4, overlap = 4.8125
PHY-3002 : Step(102): len = 62849.1, overlap = 4.75
PHY-3002 : Step(103): len = 61909.3, overlap = 5.0625
PHY-3002 : Step(104): len = 60633.3, overlap = 7.0625
PHY-3002 : Step(105): len = 58267.5, overlap = 9.0625
PHY-3002 : Step(106): len = 56569.3, overlap = 12.1562
PHY-3002 : Step(107): len = 55197.8, overlap = 12.5312
PHY-3002 : Step(108): len = 52980.2, overlap = 17.75
PHY-3002 : Step(109): len = 49996.1, overlap = 20.7812
PHY-3002 : Step(110): len = 49212.4, overlap = 21.8125
PHY-3002 : Step(111): len = 48767.3, overlap = 23.0312
PHY-3002 : Step(112): len = 48234.8, overlap = 25
PHY-3002 : Step(113): len = 47231.4, overlap = 25.75
PHY-3002 : Step(114): len = 46832.6, overlap = 25.7812
PHY-3002 : Step(115): len = 46437.5, overlap = 25.6875
PHY-3002 : Step(116): len = 46131.9, overlap = 26
PHY-3002 : Step(117): len = 45454.8, overlap = 25.7812
PHY-3002 : Step(118): len = 45372.9, overlap = 25.75
PHY-3002 : Step(119): len = 45200.2, overlap = 24.9375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000191057
PHY-3002 : Step(120): len = 45074.1, overlap = 25.0312
PHY-3002 : Step(121): len = 44969.4, overlap = 24.4688
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000382115
PHY-3002 : Step(122): len = 44929.6, overlap = 24.7188
PHY-3002 : Step(123): len = 45358.3, overlap = 24.6875
PHY-3002 : Step(124): len = 45345.4, overlap = 24.5625
PHY-3002 : Step(125): len = 45386.9, overlap = 24.125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2115 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.070239s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (89.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000102939
PHY-3002 : Step(126): len = 45402.4, overlap = 72.2812
PHY-3002 : Step(127): len = 46632.6, overlap = 70.2188
PHY-3002 : Step(128): len = 47574.1, overlap = 67.4062
PHY-3002 : Step(129): len = 47594.4, overlap = 57.125
PHY-3002 : Step(130): len = 47538.8, overlap = 54.3125
PHY-3002 : Step(131): len = 47959.4, overlap = 50.9062
PHY-3002 : Step(132): len = 48158.4, overlap = 50
PHY-3002 : Step(133): len = 48067, overlap = 48.8125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000205878
PHY-3002 : Step(134): len = 48172.3, overlap = 48.875
PHY-3002 : Step(135): len = 48483.6, overlap = 40.4688
PHY-3002 : Step(136): len = 49221.6, overlap = 38.3125
PHY-3002 : Step(137): len = 49531.4, overlap = 37.2188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000411757
PHY-3002 : Step(138): len = 49892.9, overlap = 35.1562
PHY-3002 : Step(139): len = 49941.1, overlap = 35.3125
PHY-3002 : Step(140): len = 50903.5, overlap = 26.7812
PHY-3002 : Step(141): len = 51128, overlap = 23.9375
PHY-3002 : Step(142): len = 50798.6, overlap = 24.25
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 7540, tnet num: 2115, tinst num: 1571, tnode num: 10702, tedge num: 12814.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 92.84 peak overflow 2.62
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2117.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53568, over cnt = 226(0%), over = 1086, worst = 23
PHY-1001 : End global iterations;  0.069490s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (89.9%)

PHY-1001 : Congestion index: top1 = 45.15, top5 = 24.99, top10 = 15.97, top15 = 11.44.
PHY-1001 : End incremental global routing;  0.119962s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (104.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2115 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.066836s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (93.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.215778s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (101.4%)

OPT-1001 : Current memory(MB): used = 208, reserve = 175, peak = 208.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1652/2117.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53568, over cnt = 226(0%), over = 1086, worst = 23
PHY-1002 : len = 60312, over cnt = 192(0%), over = 498, worst = 17
PHY-1002 : len = 61904, over cnt = 119(0%), over = 323, worst = 16
PHY-1002 : len = 66392, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 66520, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.092758s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (101.1%)

PHY-1001 : Congestion index: top1 = 37.89, top5 = 24.66, top10 = 17.83, top15 = 13.28.
OPT-1001 : End congestion update;  0.142399s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (98.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2115 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056511s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (110.6%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.202034s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (100.5%)

OPT-1001 : Current memory(MB): used = 212, reserve = 179, peak = 212.
OPT-1001 : End physical optimization;  0.662488s wall, 0.625000s user + 0.031250s system = 0.656250s CPU (99.1%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 367 LUT to BLE ...
SYN-4008 : Packed 367 LUT and 187 SEQ to BLE.
SYN-4003 : Packing 765 remaining SEQ's ...
SYN-4005 : Packed 89 SEQ with LUT/SLICE
SYN-4006 : 105 single LUT's are left
SYN-4006 : 676 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1043/1361 primitive instances ...
PHY-3001 : End packing;  0.060762s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.9%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 802 instances
RUN-1001 : 375 mslices, 376 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1938 nets
RUN-1001 : 1371 nets have 2 pins
RUN-1001 : 462 nets have [3 - 5] pins
RUN-1001 : 63 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 800 instances, 751 slices, 21 macros(203 instances: 133 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 50858.4, Over = 50.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6326, tnet num: 1936, tinst num: 800, tnode num: 8616, tedge num: 11202.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1936 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.296317s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.46012e-05
PHY-3002 : Step(143): len = 50459, overlap = 51
PHY-3002 : Step(144): len = 49928.3, overlap = 54
PHY-3002 : Step(145): len = 49561.3, overlap = 55.75
PHY-3002 : Step(146): len = 49084.8, overlap = 56.5
PHY-3002 : Step(147): len = 48946.9, overlap = 57.75
PHY-3002 : Step(148): len = 48405.6, overlap = 58.5
PHY-3002 : Step(149): len = 47885.5, overlap = 58.5
PHY-3002 : Step(150): len = 47511.1, overlap = 59.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.92024e-05
PHY-3002 : Step(151): len = 47868.3, overlap = 59.25
PHY-3002 : Step(152): len = 48311.2, overlap = 57.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000127247
PHY-3002 : Step(153): len = 49469.3, overlap = 54
PHY-3002 : Step(154): len = 49665.5, overlap = 53
PHY-3002 : Step(155): len = 50738, overlap = 47
PHY-3002 : Step(156): len = 50931.1, overlap = 46.25
PHY-3002 : Step(157): len = 50658.3, overlap = 46.5
PHY-3001 : Before Legalized: Len = 50658.3
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.104647s wall, 0.078125s user + 0.046875s system = 0.125000s CPU (119.4%)

PHY-3001 : After Legalized: Len = 65025.4, Over = 0
PHY-3001 : Trial Legalized: Len = 65025.4
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1936 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.049339s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00102435
PHY-3002 : Step(158): len = 61148.6, overlap = 9.25
PHY-3002 : Step(159): len = 59614.7, overlap = 11
PHY-3002 : Step(160): len = 57858.8, overlap = 12.5
PHY-3002 : Step(161): len = 56862, overlap = 16.25
PHY-3002 : Step(162): len = 55950.2, overlap = 19.5
PHY-3002 : Step(163): len = 55323.7, overlap = 22.75
PHY-3002 : Step(164): len = 54954.2, overlap = 22.5
PHY-3002 : Step(165): len = 54659.6, overlap = 23
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00190719
PHY-3002 : Step(166): len = 54908.3, overlap = 21.5
PHY-3002 : Step(167): len = 54894.9, overlap = 20.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00381439
PHY-3002 : Step(168): len = 54992.4, overlap = 20.75
PHY-3002 : Step(169): len = 55055, overlap = 21.25
PHY-3001 : Before Legalized: Len = 55055
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005390s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : After Legalized: Len = 59702.1, Over = 0
PHY-3001 : Legalized: Len = 59702.1, Over = 0
PHY-3001 : Spreading special nets. 7 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005759s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (271.3%)

PHY-3001 : 9 instances has been re-located, deltaX = 3, deltaY = 8, maxDist = 2.
PHY-3001 : Final: Len = 59906.1, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6326, tnet num: 1936, tinst num: 800, tnode num: 8616, tedge num: 11202.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 69/1938.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 65064, over cnt = 147(0%), over = 215, worst = 6
PHY-1002 : len = 65504, over cnt = 108(0%), over = 153, worst = 6
PHY-1002 : len = 67200, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 67216, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 67296, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.132937s wall, 0.156250s user + 0.046875s system = 0.203125s CPU (152.8%)

PHY-1001 : Congestion index: top1 = 31.29, top5 = 22.06, top10 = 17.04, top15 = 13.21.
PHY-1001 : End incremental global routing;  0.182942s wall, 0.203125s user + 0.046875s system = 0.250000s CPU (136.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1936 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058178s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (107.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.269078s wall, 0.296875s user + 0.046875s system = 0.343750s CPU (127.8%)

OPT-1001 : Current memory(MB): used = 214, reserve = 182, peak = 214.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1715/1938.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 67296, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005288s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (295.5%)

PHY-1001 : Congestion index: top1 = 31.29, top5 = 22.06, top10 = 17.04, top15 = 13.21.
OPT-1001 : End congestion update;  0.051145s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1936 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051400s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.2%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 37 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 760 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 800 instances, 751 slices, 21 macros(203 instances: 133 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 59963.4, Over = 0
PHY-3001 : End spreading;  0.004788s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 59963.4, Over = 0
PHY-3001 : End incremental legalization;  0.033214s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (94.1%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.149261s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (94.2%)

OPT-1001 : Current memory(MB): used = 219, reserve = 187, peak = 219.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1936 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048945s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.8%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1707/1938.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 67328, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007047s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (221.7%)

PHY-1001 : Congestion index: top1 = 31.34, top5 = 22.05, top10 = 17.04, top15 = 13.22.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1936 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049497s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.7%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.793103
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.839550s wall, 0.843750s user + 0.062500s system = 0.906250s CPU (107.9%)

RUN-1003 : finish command "place" in  5.368349s wall, 7.375000s user + 2.953125s system = 10.328125s CPU (192.4%)

RUN-1004 : used memory is 197 MB, reserved memory is 165 MB, peak memory is 220 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD_5.6.5_SP3_151.449/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 802 instances
RUN-1001 : 375 mslices, 376 lslices, 37 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1938 nets
RUN-1001 : 1371 nets have 2 pins
RUN-1001 : 462 nets have [3 - 5] pins
RUN-1001 : 63 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6326, tnet num: 1936, tinst num: 800, tnode num: 8616, tedge num: 11202.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 375 mslices, 376 lslices, 37 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1936 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 64752, over cnt = 146(0%), over = 219, worst = 6
PHY-1002 : len = 65736, over cnt = 81(0%), over = 96, worst = 3
PHY-1002 : len = 66840, over cnt = 7(0%), over = 9, worst = 2
PHY-1002 : len = 66952, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.121418s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (128.7%)

PHY-1001 : Congestion index: top1 = 30.99, top5 = 21.81, top10 = 16.82, top15 = 13.10.
PHY-1001 : End global routing;  0.169974s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (119.5%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 230, reserve = 199, peak = 233.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 491, reserve = 463, peak = 491.
PHY-1001 : End build detailed router design. 3.222878s wall, 3.187500s user + 0.031250s system = 3.218750s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 32528, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.058304s wall, 1.062500s user + 0.000000s system = 1.062500s CPU (100.4%)

PHY-1001 : Current memory(MB): used = 523, reserve = 496, peak = 523.
PHY-1001 : End phase 1; 1.064654s wall, 1.062500s user + 0.000000s system = 1.062500s CPU (99.8%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 45% nets.
PHY-1001 : Routed 61% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 175968, over cnt = 37(0%), over = 37, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 526, reserve = 497, peak = 526.
PHY-1001 : End initial routed; 1.266477s wall, 2.062500s user + 0.125000s system = 2.187500s CPU (172.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1717(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.605   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -46.351  |  21   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.330570s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (99.3%)

PHY-1001 : Current memory(MB): used = 527, reserve = 499, peak = 527.
PHY-1001 : End phase 2; 1.597139s wall, 2.390625s user + 0.125000s system = 2.515625s CPU (157.5%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 175968, over cnt = 37(0%), over = 37, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015379s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (101.6%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 175960, over cnt = 13(0%), over = 13, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.027131s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (115.2%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 176096, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.022416s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (139.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1717(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.605   |   0.000   |   0   
RUN-1001 :   Hold   |  -3.055   |  -46.351  |  21   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.336102s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (97.6%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 3 feed throughs used by 3 nets
PHY-1001 : End commit to database; 0.165169s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (104.1%)

PHY-1001 : Current memory(MB): used = 544, reserve = 516, peak = 544.
PHY-1001 : End phase 3; 0.691217s wall, 0.656250s user + 0.031250s system = 0.687500s CPU (99.5%)

PHY-1003 : Routed, final wirelength = 176096
PHY-1001 : Current memory(MB): used = 544, reserve = 516, peak = 544.
PHY-1001 : End export database. 0.010191s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (153.3%)

PHY-1001 : End detail routing;  6.772459s wall, 7.484375s user + 0.187500s system = 7.671875s CPU (113.3%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6326, tnet num: 1936, tinst num: 800, tnode num: 8616, tedge num: 11202.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[27] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_28.dib[0] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[6] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dia[7] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[6] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[53] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_47.dib[8] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[54] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_66.dia[2] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[55] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_66.dia[5] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[2] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[16] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_9.dib[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[11] endpin u_uart/U2/reg2_syn_64.mi[0] slack -2911ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[10] endpin u_uart/U2/reg2_syn_64.mi[1] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[15] endpin u_uart/U2/reg2_syn_67.mi[0] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[12] endpin u_uart/U2/reg2_syn_67.mi[1] slack -2684ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[14] endpin u_uart/U2/reg2_syn_70.mi[0] slack -2897ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[13] endpin u_uart/U2/reg2_syn_70.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[9] endpin u_uart/U2/reg2_syn_73.mi[0] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[1] endpin u_uart/U2/reg2_syn_73.mi[1] slack -2815ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[8] endpin u_uart/U2/reg2_syn_76.mi[0] slack -3043ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[0] endpin u_uart/U2/reg2_syn_76.mi[1] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[7] endpin u_uart/U2/reg2_syn_79.mi[0] slack -3055ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[2] endpin u_uart/U2/reg2_syn_79.mi[1] slack -2684ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[6] endpin u_uart/U2/reg2_syn_82.mi[0] slack -2779ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[3] endpin u_uart/U2/reg2_syn_82.mi[1] slack -2897ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[5] endpin u_uart/U2/reg2_syn_85.mi[0] slack -2923ps
RUN-1001 : 3 hold-buffer to be created.
RUN-1001 : fix hold violation on net wendu/data[4] endpin u_uart/U2/reg2_syn_85.mi[1] slack -2863ps
RUN-1001 : 3 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6442, tnet num: 1994, tinst num: 858, tnode num: 8732, tedge num: 11318.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-6005 WARNING: One round of hold fix is not enough.
RUN-1001 : Hold violation on net wendu/data[0]_holdbuf3 endpin u_uart/U2/reg2_syn_76_mi[1] slack -465ps
RUN-1001 : Hold violation on net wendu/data[2]_holdbuf3 endpin u_uart/U2/reg2_syn_79_mi[1] slack -709ps
RUN-1001 : Hold violation on net wendu/data[7]_holdbuf3 endpin u_uart/U2/reg2_syn_79_mi[0] slack -473ps
RUN-1001 : Hold violation on net wendu/data[8]_holdbuf3 endpin u_uart/U2/reg2_syn_76_mi[0] slack -351ps
RUN-1001 : Hold violation on net wendu/data[3]_holdbuf3 endpin u_uart/U2/reg2_syn_82_mi[1] slack -527ps
RUN-1001 : Hold violation on net wendu/data[6]_holdbuf3 endpin u_uart/U2/reg2_syn_82_mi[0] slack -145ps
RUN-1001 : Hold violation on net wendu/data[5]_holdbuf3 endpin u_uart/U2/reg2_syn_85_mi[0] slack -309ps
RUN-1001 : Hold violation on net wendu/data[4]_holdbuf3 endpin u_uart/U2/reg2_syn_85_mi[1] slack -429ps
RUN-1001 : Hold violation on net wendu/data[9]_holdbuf3 endpin u_uart/U2/reg2_syn_73_mi[0] slack -460ps
RUN-1001 : Hold violation on net wendu/data[1]_holdbuf3 endpin u_uart/U2/reg2_syn_73_mi[1] slack -963ps
RUN-1001 : Hold violation on net wendu/data[10]_holdbuf3 endpin u_uart/U2/reg2_syn_64_mi[1] slack -802ps
RUN-1001 : Hold violation on net wendu/data[11]_holdbuf3 endpin u_uart/U2/reg2_syn_64_mi[0] slack -722ps
RUN-1001 : Hold violation on net wendu/data[14]_holdbuf3 endpin u_uart/U2/reg2_syn_70_mi[0] slack -396ps
RUN-1001 : Hold violation on net wendu/data[13]_holdbuf3 endpin u_uart/U2/reg2_syn_70_mi[1] slack -555ps
RUN-1001 : Hold violation on net wendu/data[15]_holdbuf3 endpin u_uart/U2/reg2_syn_67_mi[0] slack -238ps
RUN-1001 : Hold violation on net wendu/data[12]_holdbuf3 endpin u_uart/U2/reg2_syn_67_mi[1] slack -499ps
RUN-1001 : End hold fix;  3.035510s wall, 3.343750s user + 0.140625s system = 3.484375s CPU (114.8%)

RUN-1003 : finish command "route" in  10.315634s wall, 11.375000s user + 0.328125s system = 11.703125s CPU (113.5%)

RUN-1004 : used memory is 501 MB, reserved memory is 475 MB, peak memory is 544 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        37
  #input                   14
  #output                  22
  #inout                    1

Utilization Statistics
#lut                      895   out of  19600    4.57%
#reg                     1053   out of  19600    5.37%
#le                      1571
  #lut only               518   out of   1571   32.97%
  #reg only               676   out of   1571   43.03%
  #lut&reg                377   out of   1571   24.00%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       37   out of    188   19.68%
  #ireg                    12
  #oreg                    20
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         478
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         102
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q0    39
#4        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#5        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
      CE          OUTPUT        G16        LVCMOS33           8            NONE       NONE    
  DA_DATA[15]     OUTPUT         H2        LVCMOS33           8            NONE       OREG    
  DA_DATA[14]     OUTPUT         J1        LVCMOS33           8            NONE       OREG    
  DA_DATA[13]     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1571   |692     |203     |1086    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1071   |292     |141     |863     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |26     |20      |6       |21      |0       |0       |
|    demodu                  |Demodulation                                     |451    |97      |45      |351     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |55     |29      |6       |45      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |5       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |11     |5       |0       |11      |0       |0       |
|    integ                   |Integration                                      |143    |29      |15      |115     |0       |0       |
|    modu                    |Modulation                                       |104    |57      |21      |100     |0       |1       |
|    rs422                   |Rs422Output                                      |323    |73      |46      |256     |0       |4       |
|    trans                   |SquareWaveGenerator                              |24     |16      |8       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |175    |130     |7       |118     |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |25     |18      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |113    |84      |0       |86      |0       |0       |
|  wendu                     |DS18B20                                          |305    |260     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1390  
    #2          2       328   
    #3          3       106   
    #4          4        28   
    #5        5-10       67   
    #6        11-50      32   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 11, tpin num: 6442, tnet num: 1994, tinst num: 858, tnode num: 8732, tedge num: 11318.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1994 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 5 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 858
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1996, pip num: 14387
BIT-1002 : Init feedthrough completely, num: 5
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1279 valid insts, and 38741 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101010010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.207435s wall, 17.625000s user + 0.062500s system = 17.687500s CPU (551.5%)

RUN-1004 : used memory is 515 MB, reserved memory is 487 MB, peak memory is 668 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250805_104939.log"
